'use client';

import { useAuth } from '@/hooks/useAuth';
import { useChangelogNotification } from '@/hooks/useChangelogNotification';
import { useBillsStore } from '@/stores/billsStore';
import { useDarkMode } from '@/stores/themeStore';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useState, type ReactNode } from 'react';

// Dynamically import ChangelogViewer to avoid SSR issues
const ChangelogViewer = dynamic(() => import('@/components/settings/ChangelogViewer'), { ssr: false });

// Import Headless UI components
import { Dialog, Switch } from '@headlessui/react';

// Dynamically import icons
const ExclamationTriangleIcon = dynamic(
  () => import('@heroicons/react/24/outline').then(mod => mod.ExclamationTriangleIcon),
  { ssr: false }
);

interface SettingsOption {
  id: string;
  name: string;
  description: string;
  icon: ReactNode;
}



export default function SettingsPage() {
  const { isDarkMode, toggleDarkMode } = useDarkMode();
  const [isNotificationsEnabled, setIsNotificationsEnabled] = useState<boolean | null>(null);
  const [isClearConfirmOpen, setIsClearConfirmOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const { hasNewUpdate } = useChangelogNotification();


  const { bills, deleteBill } = useBillsStore();

  // Check notification permission on client side
  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    if (!('Notification' in window)) {
      setIsNotificationsEnabled(false);
      return;
    }

    try {
      setIsNotificationsEnabled(Notification.permission === 'granted');
    } catch (error) {
      console.error('Error checking notification permission:', error);
      setIsNotificationsEnabled(false);
    }
  }, []);

  // Memoized clear all bills function
  const clearAllBills = useCallback(async () => {
    if (!bills || bills.length === 0) {
      return;
    }

    setIsLoading(true);
    try {
      // Use Promise.all for parallel processing
      await Promise.all(bills.map(bill => deleteBill(bill.id)));
    } catch (error) {
      console.error('Error clearing bills:', error);
      // Here you would typically show an error notification
    } finally {
      setIsLoading(false);
    }
  }, [bills, deleteBill]);

  const handleClearBillsClick = useCallback(() => {
    setIsClearConfirmOpen(true);
  }, []);

  const closeClearConfirm = useCallback(() => {
    setIsClearConfirmOpen(false);
  }, []);

  const confirmClearBills = useCallback(() => {
    clearAllBills();
    closeClearConfirm();
  }, [clearAllBills, closeClearConfirm]);

  const handleRequestNotificationPermission = useCallback(async () => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return;
    }

    try {
      const currentPermission = Notification.permission;
      if (currentPermission === 'granted') {
        setIsNotificationsEnabled(true);
        return;
      }
      if (currentPermission === 'denied') {
        setIsNotificationsEnabled(false);
        return;
      }

      const permission = await Notification.requestPermission();
      setIsNotificationsEnabled(permission === 'granted');

      if (permission === 'granted' && navigator.serviceWorker) {
        const registration = await navigator.serviceWorker.ready;

        if (registration.pushManager) {
          try {
            const vapidKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;
            if (!vapidKey) {
              throw new Error('Missing NEXT_PUBLIC_VAPID_PUBLIC_KEY');
            }
            const urlBase64ToUint8Array = (base64String: string) => {
              const padding = '='.repeat((4 - base64String.length % 4) % 4);
              const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
              const rawData = window.atob(base64);
              const outputArray = new Uint8Array(rawData.length);
              for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
              }
              return outputArray;
            };

            const subscription = await registration.pushManager.subscribe({
              userVisibleOnly: true,
              applicationServerKey: urlBase64ToUint8Array(vapidKey),
            });

            await fetch('/api/subscribe', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(subscription),
            });
          } catch (err) {
            console.error('Failed to subscribe to push notifications:', err);
          }
        }
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  }, []);

  const toggleNotifications = useCallback(() => {
    if (!isNotificationsEnabled) {
      handleRequestNotificationPermission();
    } else {
      // Informative message that we can't programmatically disable notifications
      alert("To disable notifications, please use your browser settings.");
    }
  }, [isNotificationsEnabled, handleRequestNotificationPermission]);



  // Memoize settings options
  const settingsOptions: SettingsOption[] = useMemo(() => [
    {
      id: 'account',
      name: 'Account',
      description: 'Manage your account settings and preferences',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      )
    },
    {
      id: 'data-export',
      name: 'Data Export',
      description: 'Export your financial data',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
        </svg>
      )
    }
  ], []);



  return (
    <div className="space-y-6 max-w-4xl mx-auto container-lg animate-fadeIn">
      {/* Settings Header */}
      <div className="text-center space-y-2">
        <h1 className="text-display-md font-bold text-neutral-900 dark:text-neutral-100">Settings</h1>
        <p className="body-lg text-neutral-600 dark:text-neutral-400">Manage your preferences and account settings</p>
      </div>

      {/* Preferences Section */}
      <div className="bg-neutral-0 dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 overflow-hidden shadow-sm">
        <div className="p-6 border-b border-neutral-200 dark:border-neutral-700">
          <h2 className="heading-lg font-semibold text-neutral-900 dark:text-neutral-100">Preferences</h2>
          <p className="body-sm text-neutral-600 dark:text-neutral-400 mt-1">Customize your app experience</p>
        </div>

        <div className="p-6 space-y-6">
          {/* Notifications Toggle */}
          <div className="flex items-center justify-between py-3">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
              </div>
              <div>
                <div className="heading-md font-semibold text-neutral-900 dark:text-neutral-100">Notifications</div>
                <div className="body-sm text-neutral-600 dark:text-neutral-400">Get reminders for your bills</div>
              </div>
            </div>
            <Switch
              checked={isNotificationsEnabled === true}
              onChange={toggleNotifications}
              className={`${isNotificationsEnabled ? 'bg-primary-600' : 'bg-neutral-300 dark:bg-neutral-600'
                } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2`}
            >
              <span className="sr-only">Enable notifications</span>
              <span
                className={`${isNotificationsEnabled ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
              />
            </Switch>
          </div>

          {/* Display Mode Toggle */}
          <div className="flex items-center justify-between py-3">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-secondary-100 dark:bg-secondary-900/30 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-secondary-600 dark:text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                </svg>
              </div>
              <div>
                <div className="heading-md font-semibold text-neutral-900 dark:text-neutral-100">Display Mode</div>
                <div className="body-sm text-neutral-600 dark:text-neutral-400">Choose light or dark theme</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <span className="body-sm text-neutral-600 dark:text-neutral-400">Light</span>
              <button
                onClick={toggleDarkMode}
                className="p-3 rounded-lg bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
                aria-label="Toggle dark mode"
              >
                {isDarkMode ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-warning-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m8.66-13.66l-.71.71M4.05 19.95l-.71.71M21 12h-1M4 12H3m16.95 7.05l-.71-.71M4.05 4.05l-.71-.71M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-neutral-700 dark:text-neutral-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12.79A9 9 0 1111.21 3a7 7 0 109.79 9.79z" />
                  </svg>
                )}
              </button>
              <span className="body-sm text-neutral-600 dark:text-neutral-400">Dark</span>
            </div>
          </div>
        </div>
      </div>

      {/* What's New Section */}
      <div className="bg-neutral-0 dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-sm">
        <div className="p-6 border-b border-neutral-200 dark:border-neutral-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-accent-100 dark:bg-accent-900/30 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-accent-600 dark:text-accent-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="heading-lg font-semibold text-neutral-900 dark:text-neutral-100">What&apos;s New</h3>
              <p className="body-sm text-neutral-600 dark:text-neutral-400">Latest updates and improvements</p>
            </div>
          </div>
        </div>
        <div className="p-6">
          <ChangelogViewer showOnlyLatest={true} />
        </div>
      </div>

      {/* Data Management Section */}
      <div className="bg-neutral-0 dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-sm">
        <div className="p-6 border-b border-neutral-200 dark:border-neutral-700">
          <h2 className="heading-lg font-semibold text-neutral-900 dark:text-neutral-100">Data Management</h2>
          <p className="body-sm text-neutral-600 dark:text-neutral-400 mt-1">Backup and manage your bill data</p>
        </div>

        <div className="p-6 space-y-6">
          <div>
            <h3 className="heading-md font-semibold text-neutral-900 dark:text-neutral-100 mb-2">Backup & Export</h3>
            <p className="body-sm text-neutral-600 dark:text-neutral-400 mb-4">
              Download a backup of all your bills and preferences as a JSON file.
            </p>
            <button
              onClick={async () => {
                if (!user?.uid) return;
                const res = await fetch('/api/backup/export', {
                  headers: { 'x-user-uid': user.uid }
                });
                if (res.ok) {
                  const blob = await res.blob();
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `payday-backup-${user.uid}.json`;
                  document.body.appendChild(a);
                  a.click();
                  a.remove();
                  window.URL.revokeObjectURL(url);
                } else {
                  alert('Failed to download backup.');
                }
              }}
              className="inline-flex items-center gap-2 px-4 py-2 text-md rounded-lg min-h-[40px] 
                         bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-sm hover:shadow-md
                         font-medium leading-none transition-all duration-200 border border-transparent 
                         focus:outline-none focus:ring-2 focus:ring-offset-2 active:scale-[0.98] touch-manipulation"
            >
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download Backup
            </button>
          </div>

          <div>
            <h3 className="heading-md font-semibold text-neutral-900 dark:text-neutral-100 mb-2">Clear Data</h3>
            <p className="body-sm text-neutral-600 dark:text-neutral-400 mb-4">
              Permanently remove all bill data from the application. This action cannot be undone.
            </p>
            <button
              onClick={handleClearBillsClick}
              className="inline-flex items-center gap-2 px-4 py-2 text-md rounded-lg min-h-[40px]
                         bg-error-500 text-white hover:bg-error-600 focus:ring-error-500 shadow-sm hover:shadow-md
                         font-medium leading-none transition-all duration-200 border border-transparent 
                         focus:outline-none focus:ring-2 focus:ring-offset-2 active:scale-[0.98] touch-manipulation
                         disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading || !bills || bills.length === 0}
              aria-busy={isLoading}
            >
              <ExclamationTriangleIcon className="h-4 w-4" />
              {isLoading ? 'Processing...' : 'Clear All Bills'}
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {settingsOptions.map((option) => (
          <Link
            key={option.id}
            href={`/settings/${option.id}`}
            className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm flex items-center space-x-4 transition-all hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2"
          >
            <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded-full">
              {option.icon}
            </div>
            <div>
              <h3 className="font-medium">{option.name}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">{option.description}</p>
            </div>
          </Link>
        ))}
      </div>

      {/* App Information Section */}
      <div className="bg-neutral-0 dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-sm">
        <div className="text-center p-6">
          <h2 className="heading-xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">PayDay Pilot</h2>
          <p className="body-sm text-neutral-600 dark:text-neutral-400 mb-6">
            Version {process.env.NEXT_PUBLIC_APP_VERSION}
          </p>

          <div className="flex flex-col items-center gap-4 mb-6">
            <Link href="/changelog">
              <button className="inline-flex items-center gap-2 px-6 py-3 bg-primary-500 text-white rounded-lg shadow-sm hover:bg-primary-600 hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 relative">
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                View Changelog
                {hasNewUpdate && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-accent-500 rounded-full border-2 border-white dark:border-neutral-900">
                    <span className="absolute inset-0 bg-accent-500 rounded-full animate-ping"></span>
                  </span>
                )}
              </button>
            </Link>
          </div>

          <div className="flex justify-center space-x-6">
            <Link
              href="/privacy-policy"
              className="body-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:underline transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded px-2 py-1"
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms-of-service"
              className="body-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:underline transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded px-2 py-1"
            >
              Terms of Service
            </Link>
          </div>
        </div>
      </div>

      <Dialog
        open={isClearConfirmOpen}
        onClose={closeClearConfirm}
        className="relative z-10"
        aria-labelledby="clear-bills-dialog-title"
      >
        <div className="fixed inset-0 bg-black/25 backdrop-blur-sm" aria-hidden="true" />

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
              <Dialog.Title
                as="h3"
                className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100 flex items-center"
                id="clear-bills-dialog-title"
              >
                <ExclamationTriangleIcon className="h-6 w-6 mr-2 text-red-600" />
                Confirm Deletion
              </Dialog.Title>
              <div className="mt-2">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Are you sure you want to delete all your bills? This action is permanent and cannot be undone.
                </p>
              </div>

              <div className="mt-4 flex justify-end space-x-2">
                <button
                  type="button"
                  className="inline-flex justify-center rounded-md border border-transparent bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 transition-colors"
                  onClick={closeClearConfirm}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                  onClick={confirmClearBills}
                  disabled={isLoading}
                >
                  {isLoading ? 'Processing...' : 'Yes, Clear All Bills'}
                </button>
              </div>
            </Dialog.Panel>
          </div>
        </div>
      </Dialog>
    </div>
  );
}
