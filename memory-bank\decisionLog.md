# Decision Log

- [2025-05-03 07:30:45] - Initialized Memory Bank system as per user rules.
- [Date Unknown - Inferred from Memory: 90796e67...] - Resolved TypeScript type errors in `BillImportWizard.tsx` by adding type assertions (`key: 'vendor' as keyof Bill`) and updating interfaces (`BillFormData`).
- [Date Unknown - Inferred from Memory: 90796e67...] - Implemented safe Firebase Admin initialization pattern to handle build-time environments using `isServer` and `isBuildTime` checks.
- [Date Unknown - Inferred from Memory: 90796e67...] - Added explicit type annotations for Firestore snapshots (`FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>`) in Netlify functions.
- [Date Unknown - Inferred from Memory: 90796e67...] - Updated `netlify.toml` to include `NEXT_PHASE` environment variable for build process.
- [Date Unknown - Inferred from Memory: ********..., 0326267a...] - Adopted Netlify CLI (`netlify deploy --build --prod`) as the preferred deployment method.
- [Date Unknown - Inferred from Memory: 559755bb...] - Established standard for consistent, smaller icon sizing (text-sm/text-base preferred).
- [2025-05-03 07:35:24] - Finalized requirements for Payment Reminders feature (Email, 2 days prior, global toggle).
- [2025-05-03 07:38:00] - Outlined technical plan for Payment Reminders: Firebase Scheduled Function (daily check), SendGrid integration, Firestore query for due bills & user preference, User settings UI toggle.
- [2025-05-03 07:45:37] - **Revised Plan:** Decided against email notifications due to cost/complexity. Will implement **in-app reminders** instead, using frontend logic to display bills due soon directly in the UI.
- [2025-05-03 07:55:00] - Decision: Implement native device push notifications for bill reminders to provide timely alerts even when the app is not open.

* Rationale: Enhances user engagement and ensures timely bill payments.
* Technology Choice: Firebase Cloud Messaging (FCM) - leverages existing Firebase integration.
* Implications: Requires client-side permission handling, FCM token management, and a server-side function (likely Netlify/Firebase Function) with scheduling.

- [2025-05-03 08:35:28] - Adopted "Paper Maps & Blueprints" visual theme (Cream bg, Sepia text, Blueprint Blue accent, Muted Red highlight, Playfair/Lato fonts).
- [2025-05-07 20:12:00] - Removed Gmail import functionality and confirmed Google Calendar integration.
  - Rationale: User request to simplify integration and focus on Google Calendar.
  - Changes: Deleted `src/app/api/bills/import-gmail/route.ts`, removed Gmail-related UI from `src/app/settings/account.tsx`, and removed `fetchGroupedBillMessages` from `src/utils/importUtils.ts`. Verified `src/app/api/auth/google/route.ts` uses only Calendar API scopes.
- [2025-05-07 20:16:00] - Added confirmation dialog before Google Calendar connection.
  - Rationale: Improve user experience by preventing accidental connections and clarifying the action.
  - Changes: Modified `src/components/calendar/GoogleCalendarSync.tsx` to include a modal dialog.
- [2025-05-07 20:25:00] - Corrected Google Calendar sync logic to properly use authenticated user context.
  - Rationale: The sync hook was not receiving user information, preventing token storage and retrieval for the specific user.
  - Changes: Modified `src/hooks/useGoogleCalendarSync.ts` to accept `user` as a parameter. Updated `src/components/calendar/GoogleCalendarSync.tsx` to fetch user via `useAuth` and pass it to the hook, handling potential `undefined` cases.
- [2025-05-07 21:17:00] - Implemented intermediate auth-complete page (`/settings/auth-complete`) to handle Google OAuth redirect and ensure auth state is stable before loading settings page.
  - Rationale: Resolve client-side timing issues where components rendered before auth state or URL parameters were ready after redirect.
  - Changes: Modified `/api/auth/google/callback` redirect target, created `/settings/auth-complete/page.tsx`, simplified `GoogleCalendarSync.tsx` useEffect logic.
- [2025-05-07 21:17:00] - Bumped version to 0.4.0 and updated changelogs.
  - Rationale: Reflect recent feature changes (Gmail removal, Calendar focus) and bug fixes (auth flow).
  - Changes: Updated `package.json`, `CHANGELOG.md`, `public/CHANGELOG.md`.
- [2025-05-07 21:22:00] - Deleted obsolete Netlify function `netlify/functions/scheduled-gmail-import.ts`.
  - Rationale: The function relied on removed Gmail import functionality and was causing build errors.
  - Changes: Removed the file `netlify/functions/scheduled-gmail-import.ts`.
- [2025-05-07 21:44:00] - Marked `/api/calendar/events` route as dynamic.
  - Rationale: Prevent Next.js static generation error caused by the route accessing dynamic request data (`request.url`).
  - Changes: Added `export const dynamic = 'force-dynamic';` to `src/app/api/calendar/events/route.ts`.
- [2025-05-10 08:30:00] - Streamlined "Add Bill" workflow in `BillCalendar.new.tsx`.
  - Rationale: Improve user experience by allowing direct entry of bill details instead of a multi-click process.
  - Changes: Modified `handleAddBill` to prepare a new bill object with a temporary ID and open `EditBillPanel` directly. Modified `handleSaveBill` to differentiate between adding a new bill (with temp ID) and updating an existing one.
- [2025-05-10 08:46:00] - Initiated "True Renewal" feature implementation.
  - Rationale: To provide accurate historical bill tracking and a clear mechanism for handling recurring bills.
  - Phase 1 (Backend/Store Logic) Changes:
    - Added `renewalOfBillId?: string` and `isRenewedOriginal?: boolean` to `Bill` interface in `src/types/bill.ts`.
    - Implemented `renewBill` function in `src/stores/billsStore.tsx` to create a new bill instance based on an original, preserving the original and marking it as renewed.
    - Exposed `renewBill` via `BillsContextValue`.
  - Phase 2 (Frontend UI & Interaction) Changes (in `src/components/calendar/BillCalendar.new.tsx`):
    - Added state variables for renewal modal (`isRenewModalOpen`, `billToRenewDetails`).
    - Defined `RenewBillModalProps` interface and `RenewBillModal` component.
    - Implemented handler functions: `handleOpenRenewModal`, `handleCloseRenewModal`, `handleConfirmRenewal`.
    - Added "Renew Bill" button to `EditBillPanel`, conditionally rendered.
    - Ensured `renewBill` from `useBillsStore` is destructured and used.
    - Rendered `RenewBillModal`.
- [2025-05-10 08:58:00] - Corrected `renewBill` function in `billsStore.tsx` to conditionally add the `frequency` field to the payload for `addDoc`, preventing Firebase errors when `frequency` is undefined.
- [2025-05-10 08:59:00] - Further corrected `renewBill` function in `billsStore.tsx` to conditionally add the `vendor` field to the payload, preventing Firebase errors when `vendor` is undefined.
- [2025-05-10 09:02:00] - Corrected `renewBill` function in `billsStore.tsx` to omit `paidDate` from the new bill payload if it's undefined, preventing Firebase errors.
- [2025-05-10 09:09:00] - Implemented initial SmartTip generation in `DashboardPage.tsx`.
  - Rationale: To enable the AI assistant feature by providing dynamic, actionable tips to the user.
  - Changes:
    - Modified `getSmartTips` function to iterate through unpaid bills, use `getBillUrgency` from `billIntelligence.ts` to assess them.
    - The function now returns a `SmartTip` object for the bill deemed most urgent (critical or high priority).
    - Updated `handleSmartTipAction` to include logic for `view_bill_details_` events, enabling navigation to the specific bill's edit page.
- [2025-05-10 09:13:00] - Refactored Notifications System (`src/app/notifications/page.tsx`).
  - Rationale: To fix issue where notifications were not appearing due to incorrect bill data sourcing.
  - Changes:
    - Modified `NotificationsPage.tsx` to use `useBillsStore()` for fetching bill data, ensuring consistency with the rest of the application (supports Firebase and local storage via the central store).
    - Removed direct `localStorage.getItem('bills')` access for bill data.
    - Adjusted `useEffect` hooks to correctly load non-bill notifications and trigger bill notification generation based on data from `useBillsStore` and user preferences.
- [2025-05-10 09:18:00] - Enhanced `clearAll` function in `NotificationsPage.tsx`.
  - Rationale: To ensure the notification badge count correctly resets to zero when all notifications are cleared.
  - Changes: The `clearAll` function now also removes `notificationsReadStatus` from `localStorage` and clears the corresponding `readStatusMap` state. This complements the existing logic in `useUnreadNotificationCount` which already sets the count to zero if `notificationsCleared` is true.
- [2025-05-10 09:21:00] - Resolved potential infinite loop in `NotificationsPage.tsx` (first attempt).
  - Rationale: An infinite loop was causing errors to climb rapidly.
  - Changes: Removed `notifications` from the dependency array of the `generateBillNotifications` `useCallback` hook and added `useUserPreferences`.
- [2025-05-10 09:23:00] - Further refined `generateBillNotifications` `useCallback` dependencies in `NotificationsPage.tsx`.
  - Rationale: To address ESLint `exhaustive-deps` warning and ensure stability.
  - Changes: Removed `useUserPreferences` from dependency array (as `getState()` is used, which doesn't require the hook itself as a dependency for `useCallback`). Added `eslint-disable-next-line react-hooks/exhaustive-deps` as direct usage of `notifications` state within the `if (!enableBillReminders)` block is a known trade-off to prevent the loop while still accessing a potentially stale piece of state for a specific edge case (clearing non-bill notifications if reminders get disabled). The primary generation path relies on `prev` from `setNotifications`.
- [2025-05-10 09:26:00] - Updated application version and changelogs.
  - Rationale: To document recent feature additions (True Bill Renewal, initial SmartTips) and significant bug fixes (Notification system overhaul, Bill Renewal stability).
  - Changes:
    - Incremented version in `package.json` to `0.5.0`.
    - Added new entries to `CHANGELOG.md` and `public/CHANGELOG.md` detailing the changes for version 0.5.0.
    - Confirmed `src/app/changelog/page.tsx` will dynamically load the updated changelog and trigger `useChangelogNotification` as intended.
- [2025-05-10 09:34:00] - Improved mobile calendar navigation in `src/components/calendar/BillCalendar.new.tsx`.
  - Rationale: Address difficulty in tapping month navigation buttons on small screens.
  - Changes: Added `px-2 sm:px-0` to the `div` elements containing the month navigation and "Show Paid" controls to increase tap targets on mobile. (Attempt 1)
- [2025-05-10 09:39:00] - Further improved mobile calendar navigation in `src/components/calendar/BillCalendar.new.tsx`.
  - Rationale: Ensure visibility of month navigation controls on mobile after previous attempt was insufficient.
  - Changes: Modified the flex container for month navigation to `flex-col items-center gap-2` on mobile, and `sm:flex-row sm:justify-start sm:items-center sm:space-x-3` on larger screens. This allows controls to stack vertically if needed. (Attempt 2 - Still insufficient for inner controls)
- [2025-05-10 09:41:00] - Third attempt to fix mobile calendar navigation in `src/components/calendar/BillCalendar.new.tsx`.
  - Rationale: Ensure the inner month navigation controls (Previous/Month/Next) are also stacked vertically on mobile.
  - Changes: Modified the flex container for the _inner_ month navigation controls to `flex-col items-center gap-1` on mobile, and `sm:flex-row sm:space-x-2` on larger screens. (Attempt 3 - Incorrectly hid previous button)
- [2025-05-10 09:47:00] - Fourth attempt to fix mobile calendar navigation in `src/components/calendar/BillCalendar.new.tsx`.
  - Rationale: Ensure all inner month navigation controls (Previous/Month/Next) are visible and wrap correctly on mobile.
  - Changes: Modified the flex container for the _inner_ month navigation controls to `flex-row flex-wrap justify-center items-center gap-1` on mobile, and `sm:flex-nowrap sm:space-x-2` on larger screens. (Attempt 4 - Still hid previous button)
- [2025-05-10 09:51:00] - Fifth attempt to fix mobile calendar navigation in `src/components/calendar/BillCalendar.new.tsx`.
  - Rationale: Ensure all inner month navigation controls (Previous/Month/Next) are visible and distribute space evenly on mobile.
  - Changes: Modified the flex container for the _inner_ month navigation controls to use `justify-around` instead of `justify-center` on mobile. (Attempt 5 - Still hid previous button)
- [2025-05-10 09:53:00] - Sixth attempt to fix mobile calendar navigation in `src/components/calendar/BillCalendar.new.tsx`.
  - Rationale: Ensure all inner month navigation controls (Previous/Month/Next) stack vertically and are centered on mobile.
  - Changes: Modified the flex container for the _inner_ month navigation controls to `flex-col items-center` on mobile. (Attempt 6 - Still hid previous button)
- [2025-05-10 09:56:00] - Seventh attempt to fix mobile calendar navigation in `src/components/calendar/BillCalendar.new.tsx`.
  - Rationale: Ensure all inner month navigation controls (Previous/Month/Next) are laid out in a row and space themselves appropriately on mobile.
  - Changes: Modified the flex container for the _inner_ month navigation controls to `flex-row justify-between items-center` on mobile. (Attempt 7)
- [2025-05-11 09:21:00] - Decision: Implement push notifications using OneSignal.
  - Rationale: User preference for OneSignal as an easier way to implement push notifications. This supersedes the previous decision to use Firebase Cloud Messaging (FCM).
  - Implications: Requires research into OneSignal's Next.js/React SDK, setup of OneSignal account and app, client-side integration for permission and registration, and potentially backend changes for sending notifications.

_Note: This log records significant architectural, technical, and implementation decisions._
