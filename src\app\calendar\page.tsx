'use client';
import { BackToDashboard } from '@/components/navigation/BackToDashboard';
import dynamic from 'next/dynamic';

// Import the BillCalendarProvider
const BillCalendarProvider = dynamic(
  () => import('@/components/calendar/BillCalendarContext').then(mod => mod.BillCalendarProvider),
  { ssr: false }
);

// Dynamically import the CalendarApp component with SSR disabled
const CalendarApp = dynamic(
  () => import('@/components/calendar/BillCalendar.new').then(mod => mod.default),
  {
    ssr: false,
    loading: () => (
      <div className="h-[70vh] bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-8 w-32 bg-gray-300 dark:bg-gray-600 rounded mb-4"></div>
          <div className="h-[60vh] w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    )
  }
);

export default function CalendarPage() {
  return (
    <div className="w-full h-full min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <BackToDashboard />
        </div>
        <BillCalendarProvider>
          <CalendarApp compact={false} />
        </BillCalendarProvider>
      </div>
    </div>
  );
}
