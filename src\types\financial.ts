import { Bill } from './bill';

export interface FinancialData {
  balance: number;
  income: number;
  expenses: number;
  cashFlow: number;
  growthRate: number;
  dueBills: Bill[];
  upcomingBills: Bill[];
}

export interface Transaction {
  id: string;
  date: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

// Add FinancialSummary interface
export interface FinancialSummary {
  income: number;
  expenses: number;
  cashFlow: number;
  balance: number;
}

export interface AnalyticsData {
  incomeByCategory: CategoryTotal[];
  expensesByCategory: CategoryTotal[];
  monthlyTotals: MonthlyTotal[];
  savingsRate: number;
  billPaymentRate: number;
}

export interface CategoryTotal {
  category: string;
  amount: number;
  percentage: number;
}

export interface MonthlyTotal {
  month: string;
  income: number;
  expenses: number;
  savings: number;
}

export interface UserSettings {
  currency: string;
  reminderTime: string;
  notificationsEnabled: boolean;
  darkMode: boolean;
  displayName: string;
  emailNotifications: boolean;
  defaultView: string;
  language: string;
}

export type GoalType = 'savings' | 'debtRepayment' | 'investment' | 'purchase';
export type GoalPriority = 'low' | 'medium' | 'high';
export type GoalStatus = 'active' | 'paused' | 'completed' | 'cancelled';

export interface FinancialGoal {
  id: string;
  userId: string; // Link to the user
  name: string;
  description?: string;
  type: GoalType;
  targetAmount: number;
  currentAmount: number;
  targetDate?: string; // Optional target date (YYYY-MM-DD)
  startDate: string; // YYYY-MM-DD
  priority: GoalPriority;
  status: GoalStatus;
  // Optional link to a specific bill/debt being paid off
  linkedBillId?: string;
  // Optional field for recurring contributions
  recurringContribution?: {
    amount: number;
    frequency: 'weekly' | 'biweekly' | 'monthly';
  };
  createdAt: string;
  updatedAt: string;
}
