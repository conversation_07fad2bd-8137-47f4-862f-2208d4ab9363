// src/app/api/transactions/route.ts
import { Transaction } from '@/types/financial';
import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import transactionsStore from '../../../lib/transactionsStore';

// GET /api/transactions - Fetch all transactions
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/transactions - Fetching transactions');
    // In a real app, fetch from your database here
    return NextResponse.json(transactionsStore.getTransactions());
  } catch (error) {
    console.error('Failed to fetch transactions:', error);
    return NextResponse.json({ message: 'Failed to fetch transactions' }, { status: 500 });
  }
}

// POST /api/transactions - Add a new transaction
export async function POST(request: NextRequest) {
  try {
    const txData = await request.json();
    const now = new Date().toISOString();

    // Basic validation
    if (!txData.description || txData.amount === undefined || !txData.type || !txData.date) {
       return NextResponse.json({ message: 'Missing required transaction fields' }, { status: 400 });
    }
    if (!['income', 'expense'].includes(txData.type)) {
        return NextResponse.json({ message: 'Invalid transaction type' }, { status: 400 });
    }

    const newTransaction: Transaction = {
      id: uuidv4(),
      createdAt: now,
      updatedAt: now,
      description: txData.description,
      amount: txData.amount,
      date: txData.date, // Ensure date is handled correctly (ISO string expected)
      type: txData.type,
      category: txData.category ?? 'Uncategorized', // Default category
    };

    // In a real app, insert into your database here
    transactionsStore.addTransaction(newTransaction);
    console.log('POST /api/transactions - Added transaction:', newTransaction.id);

    // Recalculate financial summary (can be done here or triggered elsewhere)
    // For simplicity, we return the new transaction. Clients using SWR
    // will likely refetch summary data separately or use mutation.

    return NextResponse.json(newTransaction, { status: 201 });
  } catch (error) {
    console.error('Failed to add transaction:', error);
    return NextResponse.json({ message: 'Failed to add transaction' }, { status: 500 });
  }
}
