'use client';

import { useCallback, useEffect, useState } from 'react';

interface QueuedModal {
  id: string;
  priority: 'low' | 'normal' | 'high' | 'critical';
  component: React.ReactNode;
  onClose?: () => void;
}

interface ModalQueueState {
  queue: QueuedModal[];
  activeModal: QueuedModal | null;
}

// Global modal queue state
let globalModalQueue: ModalQueueState = {
  queue: [],
  activeModal: null
};

// Subscribers for state changes
const subscribers = new Set<() => void>();

function notifySubscribers() {
  subscribers.forEach(callback => callback());
}

function sortQueueByPriority(queue: QueuedModal[]): QueuedModal[] {
  const priorityOrder = { critical: 4, high: 3, normal: 2, low: 1 };
  return [...queue].sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
}

function processQueue() {
  if (globalModalQueue.activeModal || globalModalQueue.queue.length === 0) {
    return;
  }

  const sortedQueue = sortQueueByPriority(globalModalQueue.queue);
  const nextModal = sortedQueue[0];
  
  globalModalQueue.activeModal = nextModal;
  globalModalQueue.queue = globalModalQueue.queue.filter(modal => modal.id !== nextModal.id);
  
  notifySubscribers();
}

export function useModalQueue() {
  const [state, setState] = useState(globalModalQueue);

  useEffect(() => {
    const updateState = () => setState({ ...globalModalQueue });
    subscribers.add(updateState);
    
    return () => {
      subscribers.delete(updateState);
    };
  }, []);

  const addToQueue = useCallback((modal: Omit<QueuedModal, 'id'> & { id?: string }) => {
    const modalWithId = {
      ...modal,
      id: modal.id || `modal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };

    // Check if modal with same ID already exists
    const existingIndex = globalModalQueue.queue.findIndex(m => m.id === modalWithId.id);
    if (existingIndex !== -1) {
      // Update existing modal
      globalModalQueue.queue[existingIndex] = modalWithId;
    } else {
      // Add new modal
      globalModalQueue.queue.push(modalWithId);
    }

    processQueue();
    return modalWithId.id;
  }, []);

  const removeFromQueue = useCallback((id: string) => {
    globalModalQueue.queue = globalModalQueue.queue.filter(modal => modal.id !== id);
    
    if (globalModalQueue.activeModal?.id === id) {
      globalModalQueue.activeModal = null;
      // Process next modal in queue
      setTimeout(processQueue, 100); // Small delay for smooth transitions
    }
    
    notifySubscribers();
  }, []);

  const closeActiveModal = useCallback(() => {
    if (globalModalQueue.activeModal) {
      const activeModal = globalModalQueue.activeModal;
      globalModalQueue.activeModal = null;
      
      // Call the modal's onClose callback if it exists
      if (activeModal.onClose) {
        activeModal.onClose();
      }
      
      // Process next modal in queue
      setTimeout(processQueue, 100);
      notifySubscribers();
    }
  }, []);

  const clearQueue = useCallback(() => {
    globalModalQueue.queue = [];
    globalModalQueue.activeModal = null;
    notifySubscribers();
  }, []);

  return {
    activeModal: state.activeModal,
    queueLength: state.queue.length,
    addToQueue,
    removeFromQueue,
    closeActiveModal,
    clearQueue
  };
}

// Helper hook for individual modal management with queue integration
export function useQueuedModal(
  id: string,
  priority: QueuedModal['priority'] = 'normal'
) {
  const [isOpen, setIsOpen] = useState(false);
  const { addToQueue, removeFromQueue, activeModal } = useModalQueue();

  const openModal = useCallback((component: React.ReactNode) => {
    setIsOpen(true);
    addToQueue({
      id,
      priority,
      component,
      onClose: () => setIsOpen(false)
    });
  }, [id, priority, addToQueue]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    removeFromQueue(id);
  }, [id, removeFromQueue]);

  const isActive = activeModal?.id === id;

  return {
    isOpen: isOpen && isActive,
    openModal,
    closeModal,
    isActive,
    isQueued: isOpen && !isActive
  };
}
