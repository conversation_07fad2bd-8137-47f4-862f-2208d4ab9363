'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';

interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  priority?: boolean;
  width?: number;
  height?: number;
  sizes?: string;
}

export function ResponsiveImage({
  src,
  alt,
  className = '',
  priority = false,
  width: initialWidth,
  height: initialHeight,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
}: ResponsiveImageProps) {
  const [dimensions, setDimensions] = useState({
    width: initialWidth || 0,
    height: initialHeight || 0,
    aspectRatio: initialWidth && initialHeight ? initialWidth / initialHeight : 0,
    loaded: !!(initialWidth && initialHeight)
  });

  useEffect(() => {
    // Skip server-side execution
    if (typeof window === 'undefined') return;
    
    // Skip if dimensions are already provided, but still execute the hook
    if (initialWidth && initialHeight) return;
    
    // Load image to get natural dimensions
    const img = new globalThis.Image(1, 1); // Use minimal size for initial instantiation
    img.src = src.startsWith('/') ? src : `/${src}`;
    
    img.onload = () => {
      const aspectRatio = img.naturalWidth / img.naturalHeight;
      setDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
        aspectRatio,
        loaded: true
      });
      
      // Log for debugging
      console.log(`Image loaded: ${src}, Dimensions: ${img.naturalWidth}x${img.naturalHeight}, Aspect ratio: ${aspectRatio}`);
    };
    
    img.onerror = () => {
      console.error(`Failed to load image for dimension calculation: ${src}`);
      // Set fallback dimensions
      setDimensions({
        width: 300,
        height: 300,
        aspectRatio: 1,
        loaded: true
      });
    };
  }, [src, initialWidth, initialHeight]);

  // If dimensions aren't loaded yet, show a placeholder
  if (!dimensions.loaded) {
    return (
      <div 
        className={`bg-gray-200 dark:bg-gray-700 animate-pulse ${className}`}
        style={{ width: '100%', aspectRatio: '1' }}
        aria-label={`Loading image: ${alt}`}
      />
    );
  }

  return (
    <Image
      src={src}
      alt={alt}
      width={dimensions.width}
      height={dimensions.height}
      className={className}
      priority={priority}
      sizes={sizes}
      style={{
        // Ensure aspect ratio is preserved
        aspectRatio: `${dimensions.aspectRatio}`,
        objectFit: 'contain'
      }}
    />
  );
}

// Add display name to the component
ResponsiveImage.displayName = 'ResponsiveImage';
