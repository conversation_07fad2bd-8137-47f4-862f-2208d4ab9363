// src/types/rules.ts

/**
 * Defines the structure for a user-configurable automation rule.
 * Rules are used to automatically categorize bills or add tags based on conditions.
 */
export interface UserRule {
  /** A unique identifier for the rule (e.g., timestamp string or UUID). */
  id: string;
  /** The condition that must be met for the rule to trigger. */
  condition: {
    /** The bill field to check ('name' or 'description'). */
    field: 'name' | 'description';
    /** The comparison operator to use. */
    operator: 'contains' | 'equals' | 'startsWith' | 'endsWith';
    /** The text value to compare against. Comparison is case-insensitive. */
    value: string;
  };
  /** The action to perform if the condition is met. */
  action: {
    /** The type of action ('setCategory' or 'addTag'). */
    type: 'setCategory' | 'addTag';
    /** The category name or tag string to apply. */
    value: string;
  };
  /** Optional priority for the rule. Lower numbers run first. Default is 0. */
  priority?: number;
}
