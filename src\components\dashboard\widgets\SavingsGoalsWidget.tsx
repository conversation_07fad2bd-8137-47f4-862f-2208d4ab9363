'use client';

import { BaseWidget } from './BaseWidget';
import { useState, useEffect } from 'react';
import { useUserPreferences } from '@/stores/userPreferencesStore';

interface SavingsGoal {
  id: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  category: string;
  color: string;
}

export default function SavingsGoalsWidget() {
  const [goals, setGoals] = useState<SavingsGoal[]>([]);
  const [loading, setLoading] = useState(true);
  const { trackAction } = useUserPreferences();

  useEffect(() => {
    const timer = setTimeout(() => {
      // This would be an API call in production
      setGoals([
        {
          id: '1',
          name: 'Emergency Fund',
          targetAmount: 10000,
          currentAmount: 6500,
          targetDate: '2025-12-31',
          category: 'Emergency',
          color: 'blue'
        },
        {
          id: '2',
          name: 'New Car',
          targetAmount: 25000,
          currentAmount: 8000,
          targetDate: '2026-06-30',
          category: 'Vehicle',
          color: 'green'
        },
        {
          id: '3',
          name: 'Vacation',
          targetAmount: 3000,
          currentAmount: 1200,
          targetDate: '2025-08-01',
          category: 'Travel',
          color: 'purple'
        }
      ]);
      setLoading(false);
    }, 600);
    
    return () => clearTimeout(timer);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getProgressColor = (color: string) => {
    const colors: Record<string, { bg: string, text: string }> = {
      blue: { bg: 'bg-blue-500', text: 'text-blue-500' },
      green: { bg: 'bg-green-500', text: 'text-green-500' },
      purple: { bg: 'bg-purple-500', text: 'text-purple-500' }
    };
    return colors[color] || colors.blue;
  };

  const calculateProgress = (current: number, target: number) => {
    return Math.min(Math.round((current / target) * 100), 100);
  };

  const getDaysRemaining = (targetDate: string) => {
    const today = new Date();
    const target = new Date(targetDate);
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <BaseWidget 
      title="Savings Goals" 
      icon={
        <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      }
    >
      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {goals.map((goal) => {
            const progress = calculateProgress(goal.currentAmount, goal.targetAmount);
            const progressColor = getProgressColor(goal.color);
            const daysRemaining = getDaysRemaining(goal.targetDate);
            
            return (
              <div
                key={goal.id}
                className="space-y-2 cursor-pointer"
                onClick={() => trackAction('view_goal_details')}
              >
                <div className="flex justify-between items-baseline">
                  <div className="font-medium">{goal.name}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {daysRemaining > 0 ? `${daysRemaining} days left` : 'Due today'}
                  </div>
                </div>
                
                <div className="relative h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div 
                    className={`absolute left-0 top-0 h-full ${progressColor.bg} transition-all duration-500`}
                    style={{ width: `${progress}%` }}
                  />
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className={progressColor.text}>
                    {progress}% complete
                  </span>
                  <span className="font-medium">
                    {formatCurrency(goal.currentAmount)} / {formatCurrency(goal.targetAmount)}
                  </span>
                </div>
              </div>
            );
          })}
          
          <button
            className="w-full mt-2 py-2 text-sm text-blue-600 dark:text-blue-400 hover:underline"
            onClick={() => trackAction('view_all_goals')}
          >
            View all goals
          </button>
        </div>
      )}
    </BaseWidget>
  );
}