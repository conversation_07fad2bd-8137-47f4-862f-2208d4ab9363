# 0.6.5 – P<PERSON> stability, unified service worker, dashboard UX

Released: 2025-09-16

Highlights
- Unified service worker registration to avoid double registration and controller conflicts
- Fixed push subscription body shape so server accepts and saves subscriptions
- Ensured OneSignal uses the app’s custom service worker (no second SW)
- Prevented heavy dashboard card content from rendering twice (preview vs modal)
- Hid top navigation on dashboard for a cleaner, immersive card experience

What’s improved for you
- Reliable “Install app” experience with consistent offline behavior
- Notifications register more reliably and won’t collide with other workers
- Faster dashboard interactions, especially on mobile, with smoother modals

Technical Change Log
- settings/page: Convert VAPID key to Uint8Array and POST raw PushSubscription to /api/subscribe
- services/notificationService: Prefer /register-sw.js; fallback to direct /custom-sw.js; POST raw subscription
- notifications/OneSignalInitializer: Point OneSignal to `/custom-sw.js`
- dashboard/DashboardHome: Render only previewContent in cards; children only inside modal
- layout/AppLayout: Hide Header on dashboard; remove extra padding for dashboard pages
- package.json: Added build-out removal to dev:clean; bumped version to 0.6.5

Notes
- Netlify function `/.netlify/functions/subscribe` expects `{ subscription }` body; the in-app route `/api/subscribe` expects a raw PushSubscription. Production choice should be unified in a follow-up.

