{"extends": "./tsconfig.json", "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./tsconfig.tsbuildinfo", "skipLibCheck": true, "skipDefaultLibCheck": true, "noCheck": true, "transpileOnly": true, "isolatedModules": true, "noEmit": true, "checkJs": false, "allowJs": true, "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false}, "exclude": ["node_modules", ".next", "build-out", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "ts-node": {"transpileOnly": true, "compilerOptions": {"module": "commonjs"}}}