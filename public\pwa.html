<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Install PayDay Pilot</title>
  <meta name="description" content="Install PayDay Pilot on your device for an app-like experience.">
  <link rel="manifest" href="/manifest.json?v=0.6.5">
  <meta name="theme-color" content="#3880ff">
  <link rel="icon" type="image/png" href="/icons/icon-192x192.png?v=0.6.5">
  <link rel="apple-touch-icon" href="/icons/icon-192x192.png?v=0.6.5">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap">
  <style>
    :root {
      color-scheme: light dark;
      --primary: #3880ff;
      --surface: #ffffff;
      --surface-alt: #f1f5f9;
      --text: #0f172a;
      --text-muted: #475569;
    }
    * {
      box-sizing: border-box;
    }
    body {
      margin: 0;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem 1.5rem;
      font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(180deg, #f8fafc 0%, #e0f2fe 100%);
      color: var(--text);
    }
    .card {
      background: var(--surface);
      border-radius: 28px;
      padding: 3rem 2.5rem;
      max-width: 520px;
      width: 100%;
      text-align: center;
      box-shadow: 0 24px 70px rgba(56, 128, 255, 0.18);
    }
    h1 {
      font-size: 2.25rem;
      margin-bottom: 0.75rem;
      font-weight: 700;
    }
    p {
      margin: 0;
      color: var(--text-muted);
      line-height: 1.6;
    }
    .actions {
      margin: 2rem 0 2.5rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    button, a.button {
      appearance: none;
      border: none;
      border-radius: 999px;
      padding: 0.85rem 1.9rem;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: transform 0.15s ease, box-shadow 0.15s ease;
    }
    button.primary, a.button.primary {
      background: var(--primary);
      color: #ffffff;
      box-shadow: 0 16px 30px rgba(56, 128, 255, 0.25);
    }
    button.secondary, a.button.secondary {
      background: var(--surface-alt);
      color: var(--text);
    }
    button:hover, a.button:hover {
      transform: translateY(-1px);
    }
    .instructions {
      text-align: left;
      margin-top: 2.5rem;
      padding: 1.75rem;
      background: var(--surface-alt);
      border-radius: 18px;
    }
    .instructions h2 {
      margin-top: 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text);
    }
    .instructions ul {
      margin: 0;
      padding-left: 1.1rem;
      color: var(--text-muted);
      line-height: 1.6;
      font-size: 0.95rem;
    }
    .note {
      margin-top: 1rem;
      font-size: 0.85rem;
      color: var(--text-muted);
    }
    @media (prefers-color-scheme: dark) {
      body {
        background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
        color: #e2e8f0;
      }
      .card {
        background: #111827;
      }
      .instructions {
        background: #1e293b;
      }
      button.secondary, a.button.secondary {
        background: #1e293b;
        color: #e2e8f0;
      }
    }
  </style>
</head>
<body>
  <main class="card">
    <img src="/icons/icon-192x192.png?v=0.6.5" alt="PayDay Pilot" width="96" height="96" loading="lazy" />
    <h1>Install PayDay Pilot</h1>
    <p>Add PayDay Pilot to your home screen for a fast, offline-first experience that feels native on every device.</p>

    <div class="actions">
      <button id="install" class="primary" hidden>Install now</button>
      <a href="/" class="button secondary">Open the web app</a>
    </div>

    <div class="instructions" id="manual-instructions" hidden>
      <h2>Manual install steps</h2>
      <ul>
        <li><strong>iOS (Safari):</strong> tap the share icon and choose "Add to Home Screen".</li>
        <li><strong>Android (Chrome):</strong> open the menu (three dots) and select "Install app".</li>
        <li><strong>Desktop (Chrome or Edge):</strong> click the install icon in the address bar.</li>
      </ul>
      <p class="note">Already installed? Launch PayDay Pilot from your home screen or app launcher.</p>
    </div>
  </main>

  <script>
    (function () {
      if (!('serviceWorker' in navigator)) {
        document.getElementById('manual-instructions').hidden = false;
        return;
      }

      let deferredPrompt = null;
      const installButton = document.getElementById('install');
      const instructions = document.getElementById('manual-instructions');

      window.addEventListener('beforeinstallprompt', (event) => {
        event.preventDefault();
        deferredPrompt = event;
        installButton.hidden = false;
      });

      installButton.addEventListener('click', async () => {
        if (!deferredPrompt) {
          instructions.hidden = false;
          installButton.hidden = true;
          return;
        }

        deferredPrompt.prompt();
        const result = await deferredPrompt.userChoice;
        deferredPrompt = null;

        if (result.outcome === 'accepted') {
          installButton.hidden = true;
        } else {
          instructions.hidden = false;
        }
      });

      if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true) {
        window.location.replace('/');
      }

      if ('serviceWorker' in navigator) {\n        navigator.serviceWorker.getRegistration().then((registration) => {\n          if (registration) return;\n          const script = document.createElement('script');\n          script.src = '/register-sw.js?v=0.6.5';\n          script.async = true;\n          document.head.appendChild(script);\n        }).catch(() => {});\n      }\n\n      window.addEventListener('appinstalled', () => {
        installButton.hidden = true;
      });
    }());
  </script>
</body>
</html>
