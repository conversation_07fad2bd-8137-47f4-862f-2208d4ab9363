{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/bills/[id]", "regex": "^/api/bills/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/bills/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/transactions/[id]", "regex": "^/api/transactions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/transactions/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/bills/[id]", "regex": "^/bills/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/bills/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/bills/[id]/edit", "regex": "^/bills/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/bills/(?<nxtPid>[^/]+?)/edit(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/ai", "regex": "^/ai(?:/)?$", "routeKeys": {}, "namedRegex": "^/ai(?:/)?$"}, {"page": "/bills/new", "regex": "^/bills/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/bills/new(?:/)?$"}, {"page": "/bills/review", "regex": "^/bills/review(?:/)?$", "routeKeys": {}, "namedRegex": "^/bills/review(?:/)?$"}, {"page": "/calculator", "regex": "^/calculator(?:/)?$", "routeKeys": {}, "namedRegex": "^/calculator(?:/)?$"}, {"page": "/calendar", "regex": "^/calendar(?:/)?$", "routeKeys": {}, "namedRegex": "^/calendar(?:/)?$"}, {"page": "/changelog", "regex": "^/changelog(?:/)?$", "routeKeys": {}, "namedRegex": "^/changelog(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/debug", "regex": "^/debug(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/notifications", "regex": "^/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/settings/account", "regex": "^/settings/account(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/account(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": [{"source": "/favicon.ico", "destination": "/icons/icon-512x512.svg", "regex": "^/favicon\\.ico(?:/)?$"}, {"source": "/images/splash-icon.png", "destination": "/images/logo.png", "regex": "^/images/splash-icon\\.png(?:/)?$"}]}