/* Design System Foundation */
/* This file contains the core design tokens and CSS custom properties for the Payday Pilot design system */

:root {
    /* Typography Scale */
    --font-size-display-2xl: 4.5rem;
    /* 72px */
    --font-size-display-xl: 3.75rem;
    /* 60px */
    --font-size-display-lg: 3rem;
    /* 48px */
    --font-size-display-md: 2.25rem;
    /* 36px */
    --font-size-display-sm: 1.875rem;
    /* 30px */

    --font-size-text-xl: 1.25rem;
    /* 20px */
    --font-size-text-lg: 1.125rem;
    /* 18px */
    --font-size-text-md: 1rem;
    /* 16px - base */
    --font-size-text-sm: 0.875rem;
    /* 14px */
    --font-size-text-xs: 0.75rem;
    /* 12px */

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-snug: 1.3;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;

    /* Font Weights */
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Spacing Scale - 8pt grid */
    --space-0-5: 0.125rem;
    /* 2px */
    --space-1: 0.25rem;
    /* 4px */
    --space-1-5: 0.375rem;
    /* 6px */
    --space-2: 0.5rem;
    /* 8px - base unit */
    --space-3: 0.75rem;
    /* 12px */
    --space-4: 1rem;
    /* 16px */
    --space-5: 1.25rem;
    /* 20px */
    --space-6: 1.5rem;
    /* 24px */
    --space-8: 2rem;
    /* 32px */
    --space-10: 2.5rem;
    /* 40px */
    --space-12: 3rem;
    /* 48px */
    --space-16: 4rem;
    /* 64px */
    --space-20: 5rem;
    /* 80px */

    /* Border Radius */
    --radius-sm: 0.25rem;
    /* 4px */
    --radius-md: 0.5rem;
    /* 8px */
    --radius-lg: 0.75rem;
    /* 12px */
    --radius-xl: 1rem;
    /* 16px */
    --radius-2xl: 1.5rem;
    /* 24px */
    --radius-3xl: 2rem;
    /* 32px */

    /* Shadows */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 200ms ease-in-out;
    --transition-slow: 300ms ease-in-out;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* Accessibility Enhancements */
    /* WCAG AA compliant focus indicators */
    --focus-ring-primary: 0 0 0 2px rgba(59, 130, 246, 0.6);
    --focus-ring-offset: 0 0 0 4px rgba(255, 255, 255, 1);
    --focus-ring-error: 0 0 0 2px rgba(239, 68, 68, 0.6);

    /* High contrast text colors for accessibility */
    --text-contrast-high: #111827;
    /* neutral-900 for maximum contrast */
    --text-contrast-medium: #374151;
    /* neutral-700 for AA compliance */
    --text-contrast-low: #6b7280;
    /* neutral-500 for decorative text */

    /* Dark mode high contrast colors */
    --text-contrast-high-dark: #ffffff;
    --text-contrast-medium-dark: #e5e7eb;
    /* neutral-200 for AA compliance in dark mode */
    --text-contrast-low-dark: #9ca3af;
    /* neutral-400 for decorative text in dark mode */
}

/* Global Typography Styles */
.text-display-2xl {
    font-size: var(--font-size-display-2xl);
    line-height: var(--line-height-tight);
    letter-spacing: -0.02em;
    font-weight: var(--font-weight-bold);
}

.text-display-xl {
    font-size: var(--font-size-display-xl);
    line-height: var(--line-height-tight);
    letter-spacing: -0.02em;
    font-weight: var(--font-weight-bold);
}

.text-display-lg {
    font-size: var(--font-size-display-lg);
    line-height: var(--line-height-tight);
    letter-spacing: -0.02em;
    font-weight: var(--font-weight-bold);
}

.text-display-md {
    font-size: var(--font-size-display-md);
    line-height: var(--line-height-snug);
    letter-spacing: -0.02em;
    font-weight: var(--font-weight-bold);
}

.text-display-sm {
    font-size: var(--font-size-display-sm);
    line-height: var(--line-height-snug);
    letter-spacing: -0.01em;
    font-weight: var(--font-weight-semibold);
}

/* Heading Styles */
.heading-xl {
    font-size: var(--font-size-text-xl);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-semibold);
}

.heading-lg {
    font-size: var(--font-size-text-lg);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-semibold);
}

.heading-md {
    font-size: var(--font-size-text-md);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-semibold);
}

/* Body Text Styles */
.body-lg {
    font-size: var(--font-size-text-lg);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-regular);
}

.body-md {
    font-size: var(--font-size-text-md);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-regular);
}

.body-sm {
    font-size: var(--font-size-text-sm);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-regular);
}

.body-xs {
    font-size: var(--font-size-text-xs);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-regular);
}

/* Utility Classes for common spacing patterns */
.space-stack-2>*+* {
    margin-top: var(--space-2);
}

.space-stack-4>*+* {
    margin-top: var(--space-4);
}

.space-stack-6>*+* {
    margin-top: var(--space-6);
}

.space-stack-8>*+* {
    margin-top: var(--space-8);
}

/* Container styles for consistent max-widths and centering */
.container-sm {
    max-width: 640px;
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--space-4);
    padding-right: var(--space-4);
}

.container-md {
    max-width: 768px;
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--space-6);
    padding-right: var(--space-6);
}

/* Accessibility Utilities */
/* Enhanced focus management for keyboard navigation */
.focus-visible-enhanced:focus-visible {
    outline: none;
    box-shadow: var(--focus-ring-primary), var(--focus-ring-offset);
    border-radius: var(--radius-md);
}

.focus-error:focus-visible {
    outline: none;
    box-shadow: var(--focus-ring-error), var(--focus-ring-offset);
}

/* High contrast text utilities */
.text-high-contrast {
    color: var(--text-contrast-high);
}

.text-medium-contrast {
    color: var(--text-contrast-medium);
}

/* Screen reader utilities */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* Skip link for keyboard navigation */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 9999;
}

.skip-link:focus {
    top: 6px;
}

/* Dark mode accessibility enhancements */
@media (prefers-color-scheme: dark) {
    .text-high-contrast {
        color: var(--text-contrast-high-dark);
    }

    .text-medium-contrast {
        color: var(--text-contrast-medium-dark);
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

.container-lg {
    max-width: 1024px;
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--space-8);
    padding-right: var(--space-8);
}

.container-xl {
    max-width: 1280px;
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--space-8);
    padding-right: var(--space-8);
}