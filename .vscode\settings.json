{
    "files.autoSave": "onFocusChange",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit",
        "source.organizeImports": "explicit"
    },
    // Performance optimizations for faster development
    "typescript.preferences.includePackageJsonAutoImports": "off",
    "typescript.suggest.autoImports": false,
    "typescript.preferences.useLabelDetailsInCompletionEntries": false,
    "typescript.disableAutomaticTypeAcquisition": true,
    "typescript.maxTsServerMemory": 8192,
    "editor.quickSuggestions": {
        "strings": false
    },
    "editor.parameterHints.enabled": false,
    "editor.codeLens": false,
    "editor.hover.delay": 1000,
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/**": true,
        "**/.next/**": true,
        "**/build-out/**": true,
        "**/.turbo/**": true,
        "**/dist/**": true
    },
    "search.exclude": {
        "**/node_modules": true,
        "**/.next": true,
        "**/build-out": true,
        "**/.turbo": true,
        "**/dist": true,
        "**/*.log": true
    },
    "css.lint.unknownAtRules": "ignore",
    "less.lint.unknownAtRules": "ignore",
    "scss.lint.unknownAtRules": "ignore",
    "tailwindCSS.includeLanguages": {
        "plaintext": "html",
        "javascript": "javascript",
        "typescript": "typescriptreact",
        "css": "css"
    },
    "github.copilot.chat.reviewSelection": {
        "instructions": [
            "Please review the following code snippet and provide feedback on its correctness and efficiency. If you find any issues, please suggest improvements or alternatives. Make sure to check for edge cases and potential bugs. If the code is correct, please confirm that it meets best practices and standards. Provide a summary of your review and any suggestions for improvement. If you have any questions or need clarification, please ask. Please provide your feedback in a clear and concise manner. If you have any additional comments or suggestions, please include them as well. Please ensure that your feedback is constructive and helpful. If you find any security vulnerabilities, please highlight them and suggest fixes. If you find any performance issues, please suggest optimizations. If you find any readability issues, please suggest improvements. If you find any maintainability issues, please suggest improvements."
        ]
    },
    "chatgpt.openOnStartup": true
}