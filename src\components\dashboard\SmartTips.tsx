'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

export interface SmartTip {
  title: string;
  description: string;
  action: string;
  actionEvent: string;
}

interface SmartTipsProps {
  tip: SmartTip;
  onActionClick: (actionEvent: string) => void;
}

export default function SmartTips({ tip, onActionClick }: SmartTipsProps) {
  const [dismissed, setDismissed] = useState(false);

  const handleDismiss = () => {
    setDismissed(true);
  };

  if (dismissed) {
    return null;
  }

  return (
    <motion.div
      className="card-theme p-4 shadow-lg relative overflow-hidden"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Decorative elements */}
      <div className="absolute -top-6 -right-6 w-24 h-24 bg-white/10 rounded-full" />
      <div className="absolute top-10 -left-10 w-16 h-16 bg-purple-600/20 rounded-full" />
      <div className="flex items-center justify-between relative z-10">
        <div>
          <h3 className="font-semibold text-lg text-gray-900 dark:text-white">{tip.title}</h3>
          <p className="text-gray-700 dark:text-gray-200 text-sm mt-1">{tip.description}</p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onActionClick(tip.actionEvent)}
            className="px-4 py-1.5 bg-primary text-white rounded-lg text-sm font-medium transition-colors shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-dark focus:ring-offset-2"
            style={{
              backgroundColor: 'var(--theme-color)',
              color: '#fff',
              boxShadow: '0 1px 4px 0 rgba(0,0,0,0.08)'
            }}
          >
            {tip.action}
          </button>
          <button
            onClick={handleDismiss}
            className="p-1.5 hover:bg-black/10 dark:hover:bg-white/10 rounded-full transition-colors"
            aria-label="Dismiss tip"
          >
            <svg className="w-5 h-5 text-gray-400 dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </motion.div>
  );
}