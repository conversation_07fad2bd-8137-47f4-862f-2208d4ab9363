import { useEffect, useState } from 'react';

/**
 * Custom hook to detect when a new service worker update is available.
 * Returns true if an update is available, false otherwise.
 */
export function useServiceWorkerUpdate(): boolean {
  const [updateAvailable, setUpdateAvailable] = useState(false);

  useEffect(() => {
    // Handler for the custom event
    const handler = () => setUpdateAvailable(true);
    window.addEventListener('sw-update-available', handler);
    return () => window.removeEventListener('sw-update-available', handler);
  }, []);

  return updateAvailable;
}
