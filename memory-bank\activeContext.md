# Active Session Context

## Current Goals/Objectives

- [x] Fix TypeScript error on line 187 in `src/app/dashboard/UpcomingBills.tsx`.
- [x] Plan the 'Payment Reminders & Notifications' feature (Revised to In-App).
- [x] Ensure Google Calendar connection works and remove Gmail import functionality.
- [ ] Design UI for in-app bill reminders.
- [ ] Implement frontend logic for filtering and displaying upcoming/due bills.
- [ ] Implement native device push notifications for bill reminders.
- [x] Ensure `BillCalendar.new.tsx` is dark mode compatible.
- [x] Fix bill saving issue in `BillCalendar.new.tsx` by aligning data types with `billsStore`.
- [ ] Fix off-by-one day error when adding new bills in `BillCalendar.new.tsx`.
- [x] Improve "Add Bill" workflow in `BillCalendar.new.tsx` to open form directly.
- [x] Implement "True Renewal" feature for bills.
- [ ] Enhance AI Assistant/SmartTips on Dashboard. (Current Focus: Implemented basic urgent bill tip)
- [x] Fix Notification system. (Current Focus: Fixed infinite loop, previously refactored to use `useBillsStore`)
- [x] Update version to 0.5.0 and update changelogs.
- [ ] Fix inability to switch months in calendar on mobile devices.

## Recent Decisions/Changes

- [2025-05-03 07:30:18] - Created `memory-bank` directory.
- [2025-05-03 07:30:45] - Created `productContext.md`.
- [2025-05-03 07:31:00] - Created `activeContext.md`.
- [2025-05-03 07:31:26] - Created `systemPatterns.md`.
- [2025-05-03 07:31:48] - Created `decisionLog.md`.
- [2025-05-03 07:32:10] - Created `progress.md`.
- [2025-05-03 07:31:32] - Decided to focus on planning the next application feature.
- [2025-05-03 07:32:22] - Prioritized fixing TypeScript error in `UpcomingBills.tsx` before feature planning.
- [2025-05-03 07:34:44] - Applied fix for TS error in `UpcomingBills.tsx` (passed `bill.paymentHistory` to `predictNextBillDate`).
- [2025-05-03 07:33:32] - Selected 'Payment Reminders & Notifications' as the next feature to implement.
- [2025-05-03 07:35:24] - Finalized requirements for Payment Reminders feature (Email, 2 days prior, global toggle).
- [2025-05-03 07:38:00] - Outlined technical plan for Payment Reminders.
- [2025-05-03 07:40:00] - Initialized Firebase Functions directory.
- [2025-05-03 07:40:30] - Installed necessary npm dependencies in `functions` directory.
- [2025-05-03 07:42:04] - User requested guidance on SendGrid setup.
- [2025-05-03 07:45:37] - Pivoted feature plan from Email Notifications (SendGrid/Backend) to In-App Reminders (Frontend only) due to cost/complexity concerns.
- [2025-05-03 07:55:00] - Shift focus to implementing native device push notifications for bill reminders.
- [2025-05-03 08:35:28] - Current Focus: Implementing "Paper Maps & Blueprints" theme (applying colors, fonts, base styles to config and global CSS).
- [2025-05-03 08:38:00] - Current Focus: Integrating Lato and Playfair Display fonts via next/font in layout.tsx.

## Current Focus

[2025-05-11 10:41:00] - Successfully addressed all reported npm audit vulnerabilities.

## Previous Focus (Completed)

- Implement in-app notification count badge on TopNav and BottomNav.
- Ensure correct generation and read/unread status handling for bill reminders.
- [2025-05-03 08:38:00] - Integrating Lato and Playfair Display fonts via next/font in layout.tsx.
- [2025-05-07 20:16:00] - Added confirmation dialog to Google Calendar connection flow in `src/components/calendar/GoogleCalendarSync.tsx`.

## Open Questions/Issues

- Verify current FCM setup status (client-side hook exists, but backend/trigger mechanism?).
- Determine best approach for storing FCM tokens securely per user.
- Decide on triggering mechanism for the server-side notification function (e.g., scheduled Netlify/Firebase function).

## Recent Changes

- Deleted `src/app/api/bills/import-gmail/route.ts`.
- Removed Gmail specific code from `src/app/settings/account.tsx`.
- Removed `fetchGroupedBillMessages` function from `src/utils/importUtils.ts`.
- Verified `src/app/api/auth/google/route.ts` uses Calendar scopes.
- Modified notification merge logic in `notifications/page.tsx` to ensure newly generated reminders are marked unread.
- Added `useUnreadNotificationCount` hook.
- Integrated hook and badge display into `TopNav.tsx` and `BottomNav.tsx`.
- Reverted layout changes in `TopNav.tsx` to maintain original spacing.
- [2025-05-07 20:12:00] - Refactored Google authentication to use Calendar scopes and removed all Gmail import related code.
- [2025-05-10 08:30:00] - Modified `handleAddBill` and `handleSaveBill` in `BillCalendar.new.tsx` to open `EditBillPanel` directly for new bills.
- [2025-05-10 08:46:00] - Added `renewalOfBillId` and `isRenewedOriginal` to `Bill` type. Implemented `renewBill` function in `billsStore.tsx`.
- [2025-05-10 08:54:00] - Added `RenewBillModal` and associated UI logic to `BillCalendar.new.tsx`.
- [2025-05-10 08:58:00] - Fixed Firebase error in `renewBill` by conditionally adding `frequency` to payload.
- [2025-05-10 08:59:00] - Fixed Firebase error in `renewBill` by conditionally adding `vendor` to payload.
- [2025-05-10 09:02:00] - Fixed Firebase error in `renewBill` by omitting `paidDate` from payload if undefined.
- [2025-05-10 09:09:00] - Updated `getSmartTips` in `DashboardPage.tsx` to use `getBillUrgency` and generate a tip for the most urgent bill. Updated `handleSmartTipAction` to handle navigation for this tip.
- [2025-05-10 09:13:00] - Refactored `NotificationsPage.tsx` to use `useBillsStore` for bill data, instead of direct localStorage access, to ensure consistency.
- [2025-05-10 09:18:00] - Updated `clearAll` in `NotificationsPage.tsx` to also clear `notificationsReadStatus` from localStorage and state.
- [2025-05-10 09:21:00] - Fixed potential infinite loop in `NotificationsPage.tsx` by correcting dependencies for `generateBillNotifications` callback (first pass).
- [2025-05-10 09:23:00] - Further refined `generateBillNotifications` dependencies in `NotificationsPage.tsx` to satisfy ESLint and prevent loops.
- [2025-05-10 09:26:00] - Updated application version to 0.5.0 in `package.json` and updated `CHANGELOG.md` and `public/CHANGELOG.md` with recent features and fixes.
- [2025-05-10 09:34:00] - Added horizontal padding to month navigation controls in `src/components/calendar/BillCalendar.new.tsx` for better usability on mobile. (Attempt 1 - Insufficient)
- [2025-05-10 09:39:00] - Changed flex direction to column for the _outer_ month navigation container on mobile in `src/components/calendar/BillCalendar.new.tsx` to ensure visibility. (Attempt 2 - Still insufficient for inner controls)
- [2025-05-10 09:41:00] - Changed flex direction to column for the _inner_ month navigation controls (Prev/Month/Next) on mobile in `src/components/calendar/BillCalendar.new.tsx`. (Attempt 3 - Incorrectly hid previous button)
- [2025-05-10 09:47:00] - Changed _inner_ month navigation controls to `flex-row flex-wrap justify-center items-center gap-1` on mobile in `src/components/calendar/BillCalendar.new.tsx`. (Attempt 4 - Still hid previous button)
- [2025-05-10 09:51:00] - Changed _inner_ month navigation controls to `justify-around` on mobile in `src/components/calendar/BillCalendar.new.tsx`. (Attempt 5 - Still hid previous button)
- [2025-05-10 09:53:00] - Changed _inner_ month navigation controls to `flex-col items-center` on mobile in `src/components/calendar/BillCalendar.new.tsx`. (Attempt 6 - Still hid previous button)
- [2025-05-10 09:56:00] - Changed _inner_ month navigation controls to `flex-row justify-between items-center` on mobile in `src/components/calendar/BillCalendar.new.tsx`. (Attempt 7)
- [2025-05-11 09:21:00] - Updated decision log to reflect switch from FCM to OneSignal for push notifications.
- [2025-05-11 09:26:00] - Created `OneSignalInitializer.tsx` component with the provided App ID.
- [2025-05-11 09:27:00] - Added `OneSignalInitializer` to `src/app/layout.tsx`.
- [2025-05-11 09:32:00] - Confirmed OneSignal service worker files (e.g., `OneSignalSDKWorker.js`) are now in the `public/` directory.
- [2025-05-11 09:43:00] - Created `/api/onesignal-subscribe` API route to store Player IDs.
- [2025-05-11 09:43:00] - Fixed Firebase Admin auth import in the API route.
- [2025-05-11 09:44:00] - Updated `OneSignalInitializer.tsx` to call the new API endpoint.
- [2025-05-11 09:47:00] - Created Netlify function `send-onesignal-notifications.ts` for server-side sending.
- [2025-05-11 09:48:00] - Scheduled the Netlify function `send-onesignal-notifications` in `netlify.toml`.
- [2025-05-11 09:54:00] - Updated version to 0.6.0 and updated changelogs.
- [2025-05-11 10:25:00] - Re-enabled landing page logic in `src/app/page.tsx`.
- [2025-05-11 10:33:00] - Uninstalled `xlsx` package and updated `BillImportWizard.tsx` to reflect its removal.
- [2025-05-11 10:37:00] - Updated `next` package to `14.2.28` to resolve critical vulnerability.
- [2025-05-11 10:41:00] - Updated `netlify-cli` to latest version, resolving remaining vulnerabilities.

## Blockers

- None currently identified.

_Note: This file tracks the immediate focus, decisions, and roadblocks within the current development session._
