// Financial calculation utilities for loans, debts, and payments

import { LoanInfo, DebtInfo, PaymentFrequency, InterestType } from '@/types/bill';

/**
 * Convert payment frequency to number of payments per year
 */
export function getPaymentsPerYear(frequency: PaymentFrequency): number {
  switch (frequency) {
    case 'weekly': return 52;
    case 'biweekly': return 26;
    case 'monthly': return 12;
    case 'quarterly': return 4;
    case 'annually': return 1;
    default: return 12;
  }
}

/**
 * Convert annual interest rate to periodic rate
 */
export function getPeriodicRate(annualRate: number, frequency: PaymentFrequency): number {
  const paymentsPerYear = getPaymentsPerYear(frequency);
  return annualRate / 100 / paymentsPerYear;
}

/**
 * Calculate loan payment using PMT formula
 * PMT = P * [r(1+r)^n] / [(1+r)^n - 1]
 */
export function calculateLoanPayment(loanInfo: LoanInfo): number {
  const { originalAmount, interestRate, loanTerm, paymentFrequency } = loanInfo;
  
  if (interestRate === 0) {
    // No interest loan
    const paymentsPerYear = getPaymentsPerYear(paymentFrequency);
    const totalPayments = (loanTerm / 12) * paymentsPerYear;
    return originalAmount / totalPayments;
  }

  const periodicRate = getPeriodicRate(interestRate, paymentFrequency);
  const paymentsPerYear = getPaymentsPerYear(paymentFrequency);
  const totalPayments = (loanTerm / 12) * paymentsPerYear;

  const numerator = periodicRate * Math.pow(1 + periodicRate, totalPayments);
  const denominator = Math.pow(1 + periodicRate, totalPayments) - 1;

  return originalAmount * (numerator / denominator);
}

/**
 * Calculate minimum payment for credit card debt
 */
export function calculateMinimumPayment(debtInfo: DebtInfo): number {
  const { currentBalance, minimumPaymentCalculation, minimumPaymentPercentage, minimumPayment } = debtInfo;

  switch (minimumPaymentCalculation) {
    case 'percentage':
      return currentBalance * (minimumPaymentPercentage || 2) / 100;
    case 'fixed':
      return minimumPayment;
    case 'balance_based':
      // Common credit card formula: 1% of balance + interest + fees
      const interestCharge = currentBalance * (debtInfo.interestRate / 100 / 12);
      return Math.max(25, currentBalance * 0.01 + interestCharge);
    default:
      return minimumPayment;
  }
}

/**
 * Calculate total interest for a loan
 */
export function calculateTotalInterest(loanInfo: LoanInfo): number {
  const monthlyPayment = calculateLoanPayment(loanInfo);
  const paymentsPerYear = getPaymentsPerYear(loanInfo.paymentFrequency);
  const totalPayments = (loanInfo.loanTerm / 12) * paymentsPerYear;
  const totalPaid = monthlyPayment * totalPayments;
  
  return totalPaid - loanInfo.originalAmount;
}

/**
 * Calculate payoff date for a loan
 */
export function calculatePayoffDate(loanInfo: LoanInfo): Date {
  const startDate = new Date(loanInfo.startDate);
  const payoffDate = new Date(startDate);
  payoffDate.setMonth(payoffDate.getMonth() + loanInfo.loanTerm);
  
  return payoffDate;
}

/**
 * Calculate debt payoff time with minimum payments
 */
export function calculateDebtPayoffTime(debtInfo: DebtInfo, monthlyPayment?: number): {
  months: number;
  totalInterest: number;
  totalPaid: number;
} {
  const payment = monthlyPayment || calculateMinimumPayment(debtInfo);
  const monthlyRate = debtInfo.interestRate / 100 / 12;
  let balance = debtInfo.currentBalance;
  let months = 0;
  let totalInterest = 0;

  // Prevent infinite loop for payments too small
  if (payment <= balance * monthlyRate) {
    return { months: Infinity, totalInterest: Infinity, totalPaid: Infinity };
  }

  while (balance > 0.01 && months < 600) { // Max 50 years
    const interestCharge = balance * monthlyRate;
    const principalPayment = Math.min(payment - interestCharge, balance);
    
    balance -= principalPayment;
    totalInterest += interestCharge;
    months++;
  }

  return {
    months,
    totalInterest,
    totalPaid: debtInfo.currentBalance + totalInterest
  };
}

/**
 * Generate amortization schedule for a loan
 */
export function generateAmortizationSchedule(
  loanInfo: LoanInfo,
  numberOfPayments: number = 12
): Array<{
  paymentNumber: number;
  paymentDate: Date;
  paymentAmount: number;
  principalPayment: number;
  interestPayment: number;
  remainingBalance: number;
}> {
  const schedule = [];
  const monthlyPayment = calculateLoanPayment(loanInfo);
  const periodicRate = getPeriodicRate(loanInfo.interestRate, loanInfo.paymentFrequency);
  const paymentsPerYear = getPaymentsPerYear(loanInfo.paymentFrequency);
  const daysBetweenPayments = Math.round(365 / paymentsPerYear);
  
  let remainingBalance = loanInfo.remainingBalance || loanInfo.originalAmount;
  let currentDate = new Date(loanInfo.startDate);

  for (let i = 1; i <= numberOfPayments && remainingBalance > 0.01; i++) {
    const interestPayment = remainingBalance * periodicRate;
    const principalPayment = Math.min(monthlyPayment - interestPayment, remainingBalance);
    remainingBalance -= principalPayment;

    schedule.push({
      paymentNumber: i,
      paymentDate: new Date(currentDate),
      paymentAmount: monthlyPayment,
      principalPayment,
      interestPayment,
      remainingBalance: Math.max(0, remainingBalance)
    });

    // Move to next payment date
    currentDate.setDate(currentDate.getDate() + daysBetweenPayments);
  }

  return schedule;
}

/**
 * Calculate savings from extra payments
 */
export function calculateExtraPaymentSavings(
  loanInfo: LoanInfo,
  extraPayment: number
): {
  monthsSaved: number;
  interestSaved: number;
  newPayoffDate: Date;
} {
  const originalPayment = calculateLoanPayment(loanInfo);
  const originalInterest = calculateTotalInterest(loanInfo);
  const originalPayoffDate = calculatePayoffDate(loanInfo);

  // Calculate with extra payment
  const newLoanInfo = { ...loanInfo, extraPayment };
  const newMonthlyPayment = originalPayment + extraPayment;
  
  // Simplified calculation - in practice would need full amortization
  const periodicRate = getPeriodicRate(loanInfo.interestRate, loanInfo.paymentFrequency);
  const paymentsPerYear = getPaymentsPerYear(loanInfo.paymentFrequency);
  
  let balance = loanInfo.originalAmount;
  let months = 0;
  let totalInterest = 0;

  while (balance > 0.01 && months < 600) {
    const interestCharge = balance * periodicRate;
    const principalPayment = Math.min(newMonthlyPayment - interestCharge, balance);
    
    balance -= principalPayment;
    totalInterest += interestCharge;
    months++;
  }

  const newPayoffDate = new Date(loanInfo.startDate);
  newPayoffDate.setMonth(newPayoffDate.getMonth() + Math.round(months * 12 / paymentsPerYear));

  return {
    monthsSaved: loanInfo.loanTerm - Math.round(months * 12 / paymentsPerYear),
    interestSaved: originalInterest - totalInterest,
    newPayoffDate
  };
}

/**
 * Validate financial inputs
 */
export function validateFinancialInputs(
  amount: number,
  interestRate: number,
  term: number
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (amount <= 0) {
    errors.push('Amount must be greater than 0');
  }

  if (interestRate < 0 || interestRate > 100) {
    errors.push('Interest rate must be between 0% and 100%');
  }

  if (term <= 0 || term > 600) {
    errors.push('Term must be between 1 and 600 months');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
