@echo off
echo 🚀 Quick Project Migration
echo =========================
echo.

set "TARGET_DIR=C:\dev\payday-pilot-next"

echo 📂 Current location: %CD%
echo 🎯 Target location: %TARGET_DIR%
echo.

echo ⚠️  This will move your project outside OneDrive to avoid build issues.
set /p "CONFIRM=Continue? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo ❌ Migration cancelled.
    pause
    exit /b 0
)

echo.
echo 🧹 Cleaning build artifacts...
if exist ".next" rmdir /s /q ".next" 2>nul
if exist ".turbo" rmdir /s /q ".turbo" 2>nul
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache" 2>nul
if exist "build-out" rmdir /s /q "build-out" 2>nul

echo 📁 Creating target directory...
if not exist "C:\dev" mkdir "C:\dev"
if exist "%TARGET_DIR%" (
    echo ⚠️  Target directory exists. Removing...
    rmdir /s /q "%TARGET_DIR%" 2>nul
)

echo 📋 Copying project files...
robocopy "%CD%" "%TARGET_DIR%" /E /XD .git .next .turbo node_modules build-out out dist /XF *.log npm-debug.log* yarn-debug.log* yarn-error.log* .DS_Store Thumbs.db /R:3 /W:5 /MT:8 /NFL /NDL /NP

if %ERRORLEVEL% LEQ 7 (
    echo ✅ Project copied successfully
) else (
    echo ❌ Copy failed. Please run the PowerShell script instead.
    pause
    exit /b 1
)

echo 📂 Changing to new directory...
cd /d "%TARGET_DIR%"

echo 📦 Installing dependencies...
call npm install

echo 🏗️  Testing build...
call npm run build

echo.
echo 🎉 Migration completed!
echo.
echo 📝 Next steps:
echo    1. Update your IDE to open: %TARGET_DIR%
echo    2. Update bookmarks and shortcuts
echo    3. Delete old OneDrive copy after verification
echo.
pause
