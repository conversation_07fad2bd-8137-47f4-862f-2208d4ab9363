'use client';

import { motion, AnimatePresence } from 'framer-motion';

interface UpdateBadgeProps {
  show: boolean;
  size?: 'sm' | 'md' | 'lg';
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  className?: string;
  children?: React.ReactNode;
}

const sizeClasses = {
  sm: 'h-2 w-2',
  md: 'h-3 w-3',
  lg: 'h-4 w-4'
};

const positionClasses = {
  'top-right': '-top-1 -right-1',
  'top-left': '-top-1 -left-1',
  'bottom-right': '-bottom-1 -right-1',
  'bottom-left': '-bottom-1 -left-1'
};

export function UpdateBadge({ 
  show, 
  size = 'md', 
  position = 'top-right', 
  className = '',
  children 
}: UpdateBadgeProps) {
  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ 
            scale: 1, 
            opacity: 1,
            transition: {
              type: "spring",
              stiffness: 500,
              damping: 30
            }
          }}
          exit={{ scale: 0, opacity: 0 }}
          className={`
            absolute ${positionClasses[position]} ${sizeClasses[size]}
            bg-red-500 rounded-full border-2 border-white dark:border-gray-900
            flex items-center justify-center
            ${className}
          `}
        >
          {children && (
            <span className="text-white text-xs font-bold leading-none">
              {children}
            </span>
          )}
          
          {/* Pulse animation */}
          <motion.div
            animate={{
              scale: [1, 1.5, 1],
              opacity: [1, 0, 1]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className={`
              absolute inset-0 bg-red-500 rounded-full
              ${sizeClasses[size]}
            `}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
}

/**
 * Update indicator specifically for dashboard cards
 */
export function DashboardUpdateIndicator({ show }: { show: boolean }) {
  return (
    <UpdateBadge 
      show={show} 
      size="md" 
      position="top-right"
      className="z-10"
    />
  );
}

/**
 * Update indicator with text/number
 */
export function UpdateBadgeWithText({ 
  show, 
  text, 
  size = 'lg',
  position = 'top-right',
  className = ''
}: {
  show: boolean;
  text: string | number;
  size?: 'sm' | 'md' | 'lg';
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  className?: string;
}) {
  const textSizeClasses = {
    sm: 'h-4 w-4 text-xs',
    md: 'h-5 w-5 text-xs',
    lg: 'h-6 w-6 text-sm'
  };

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ 
            scale: 1, 
            opacity: 1,
            transition: {
              type: "spring",
              stiffness: 500,
              damping: 30
            }
          }}
          exit={{ scale: 0, opacity: 0 }}
          className={`
            absolute ${positionClasses[position]} ${textSizeClasses[size]}
            bg-red-500 rounded-full border-2 border-white dark:border-gray-900
            flex items-center justify-center
            ${className}
          `}
        >
          <span className="text-white font-bold leading-none">
            {text}
          </span>
          
          {/* Pulse animation */}
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.5, 0, 0.5]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className={`
              absolute inset-0 bg-red-500 rounded-full
              ${textSizeClasses[size]}
            `}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default UpdateBadge;
