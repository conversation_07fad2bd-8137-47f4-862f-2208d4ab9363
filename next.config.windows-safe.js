// Windows-safe Next.js configuration
// Temporary config to bypass EPERM issues

const isDev = process.env.NODE_ENV !== 'production';

// Simplified PWA configuration - disabled for Windows compatibility
const withPWA = (config) => config; // No-op

// Bundle analyzer (only when explicitly enabled)
const withBundleAnalyzer = process.env.ANALYZE === 'true'
  ? require('@next/bundle-analyzer')({
    enabled: true,
    openAnalyzer: false
  })
  : (config) => config;

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: isDev ? false : true,
  
  // Environment variables
  env: {
    NEXT_PUBLIC_APP_VERSION: require('./package.json').version,
  },
  
  // Disable TypeScript checking for faster builds
  typescript: {
    ignoreBuildErrors: true,
    tsconfigPath: './tsconfig.json',
  },
  
  // Disable ESLint for faster builds
  eslint: {
    ignoreDuringBuilds: true,
    dirs: [],
  },
  
  // Image optimization
  images: {
    formats: ['image/webp'],
    minimumCacheTTL: 60,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'www.gstatic.com',
      },
    ],
  },
  
  // Minimal experimental features to avoid conflicts
  experimental: {
    // Disable features that might cause file system issues
    serverComponentsExternalPackages: undefined,
    optimizePackageImports: undefined,
    optimizeCss: false, // Disable CSS optimization that might cause issues
    swcMinify: false, // Disable SWC minification
    turbo: {}, // Minimal turbo config
  },
  
  // Disable features that might cause file system issues
  skipTrailingSlashRedirect: true,
  skipMiddlewareUrlNormalize: true,
  
  // Simple rewrites
  async rewrites() {
    return [
      { source: '/favicon.ico', destination: '/icons/icon-512x512.svg' },
      { source: '/images/splash-icon.png', destination: '/images/logo.png' },
    ];
  },

  // NO WEBPACK CONFIGURATION - this is what was causing the issues
  // webpack: undefined, // Completely disable custom webpack config
  
  // Disable source maps for faster builds
  productionBrowserSourceMaps: false,
  
  // Simple HTTP options
  httpAgentOptions: {
    keepAlive: true,
  },
  
  // Basic security headers
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'Referrer-Policy', value: 'strict-origin-when-cross-origin' },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        ],
      },
    ];
  },
};

// Export without complex plugin chains
module.exports = nextConfig;
