'use client';

import { BaseWidget } from './BaseWidget';
import { useState, useEffect } from 'react';
import { useUserPreferences } from '@/stores/userPreferencesStore';

interface ForecastDay {
  date: string;
  balance: number;
  income: number;
  expenses: number;
}

export default function CashflowForecastWidget() {
  const [forecast, setForecast] = useState<ForecastDay[]>([]);
  const [loading, setLoading] = useState(true);
  const { trackAction } = useUserPreferences();

  useEffect(() => {
    const timer = setTimeout(() => {
      // This would be an API call in production
      setForecast([
        { date: '2025-04-24', balance: 1500, income: 0, expenses: 200 },
        { date: '2025-04-25', balance: 1300, income: 0, expenses: 0 },
        { date: '2025-04-26', balance: 1300, income: 2000, expenses: 1200 },
        { date: '2025-04-27', balance: 2100, income: 0, expenses: 100 },
        { date: '2025-04-28', balance: 2000, income: 0, expenses: 500 }
      ]);
      setLoading(false);
    }, 700);
    
    return () => clearTimeout(timer);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const getBarHeight = (value: number, maxValue: number) => {
    return Math.max((value / maxValue) * 100, 5);
  };

  return (
    <BaseWidget 
      title="Cashflow Forecast" 
      icon={
        <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      }
    >
      {loading ? (
        <div className="space-y-4">
          <div className="animate-pulse space-y-2">
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-end justify-between h-32 gap-2">
            {forecast.map((day, index) => {
              const maxBalance = Math.max(...forecast.map(d => d.balance));
              const height = getBarHeight(day.balance, maxBalance);
              
              return (
                <div 
                  key={day.date}
                  className="flex-1 flex flex-col items-center gap-1"
                  onClick={() => trackAction('view_forecast_day')}
                >
                  <div className="w-full relative">
                    <div 
                      className="w-full bg-blue-500 dark:bg-blue-600 rounded-t absolute bottom-0"
                      style={{ height: `${height}%` }}
                    />
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 whitespace-nowrap">
                    {formatDate(day.date)}
                  </div>
                </div>
              );
            })}
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500 dark:text-gray-400">Projected Balance</span>
              <span className="font-medium">{formatCurrency(forecast[forecast.length - 1].balance)}</span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-gray-500 dark:text-gray-400">Upcoming Income</span>
              <span className="text-green-600 dark:text-green-400">
                +{formatCurrency(forecast.reduce((sum, day) => sum + day.income, 0))}
              </span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-gray-500 dark:text-gray-400">Upcoming Expenses</span>
              <span className="text-red-600 dark:text-red-400">
                -{formatCurrency(forecast.reduce((sum, day) => sum + day.expenses, 0))}
              </span>
            </div>
          </div>
          
          <button
            className="w-full py-2 text-sm text-blue-600 dark:text-blue-400 hover:underline"
            onClick={() => trackAction('view_full_forecast')}
          >
            View detailed forecast
          </button>
        </div>
      )}
    </BaseWidget>
  );
}