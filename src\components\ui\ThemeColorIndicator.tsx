'use client';

import { ThemeColor, useTheme } from '@/stores/themeStore';

interface ThemeColorIndicatorProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function ThemeColorIndicator({ className = '', size = 'md' }: ThemeColorIndicatorProps) {
  const { themeColor } = useTheme();

  // Map theme colors to CSS variables - Keep for title
  const colorMap: Record<ThemeColor, string> = {
    default: 'var(--blue-theme)',
    blue: 'var(--blue-theme)',
    purple: 'var(--purple-theme)',
    green: 'var(--green-theme)',
    orange: 'var(--orange-theme)',
    teal: 'var(--teal-theme)',
  };

  // Map theme colors to Tailwind background classes using CSS variables
  const bgColorClasses: Record<ThemeColor, string> = {
    default: 'bg-[var(--blue-theme)]',
    blue: 'bg-[var(--blue-theme)]',
    purple: 'bg-[var(--purple-theme)]',
    green: 'bg-[var(--green-theme)]',
    orange: 'bg-[var(--orange-theme)]',
    teal: 'bg-[var(--teal-theme)]',
  };

  // Size classes
  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-6 h-6',
  };

  return (
    <div
      className={`rounded-full ${sizeClasses[size]} ${bgColorClasses[themeColor]} ${className}`}
      title={`Current theme: ${themeColor}`}
    />
  );
}