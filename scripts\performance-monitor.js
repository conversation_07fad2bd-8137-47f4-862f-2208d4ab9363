#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const PERFORMANCE_LOG = path.join(__dirname, '..', 'performance.log');

function log(message) {
  const entry = `[${new Date().toISOString()}] ${message}`;
  console.log(entry);
  fs.appendFileSync(PERFORMANCE_LOG, entry + '\n');
}

async function measure(command, args) {
  return new Promise((resolve, reject) => {
    const start = Date.now();
    const child = spawn(command, args, { shell: true });
    let stderr = '';

    child.stderr.on('data', (chunk) => {
      stderr += chunk.toString();
    });

    child.on('close', (code) => {
      const duration = (Date.now() - start) / 1000;
      if (code === 0) {
        resolve({ duration });
      } else {
        reject(new Error(`${command} ${args.join(' ')} failed after ${duration.toFixed(2)}s\n${stderr}`));
      }
    });

    child.on('error', reject);
  });
}

async function runFullTest() {
  log('Running performance test sequence');

  try {
    log('Cleaning previous build artefacts');
    await measure('rimraf', ['.next', '.turbo', 'node_modules/.cache']);

    log('Measuring development startup time');
    const dev = await measure('npm', ['run', 'dev:fast']);
    log(`Development server cold start: ${dev.duration.toFixed(2)}s`);

    log('Measuring production build time');
    const build = await measure('npm', ['run', 'build']);
    log(`Production build duration: ${build.duration.toFixed(2)}s`);
  } catch (error) {
    log(`Performance test failed: ${error.message}`);
    process.exit(1);
  }
}

async function runSingle(command) {
  try {
    const result = await measure('npm', ['run', command]);
    log(`${command} completed in ${result.duration.toFixed(2)}s`);
  } catch (error) {
    log(`${command} failed: ${error.message}`);
    process.exit(1);
  }
}

const [, , mode] = process.argv;

switch (mode) {
  case 'test':
    runFullTest();
    break;
  case 'dev':
    runSingle('dev:fast');
    break;
  case 'build':
    runSingle('build');
    break;
  default:
    console.log('Usage: node performance-monitor.js [test|dev|build]');
    process.exit(1);
}
