'use client';

import { FinancialSummary, Transaction } from '@/types/financial';
import { createContext, ReactNode, useCallback, useContext } from 'react';
import useSWR, { mutate } from 'swr';

const fetcher = async (url: string) => {
  try {
    const res = await fetch(url);
    if (!res.ok) {
      const errorText = await res.text().catch(() => 'No error details available');
      const error = new Error(`An error occurred: ${res.statusText} (Status: ${res.status})`);
      (error as any).status = res.status;
      (error as any).info = errorText;
      throw error;
    }
    return await res.json();
  } catch (error) {
    if (error instanceof Error) {
      console.error(`Fetch error for ${url}:`, error);
      throw error;
    }
    // If it's not an Error instance, wrap it in one
    const wrappedError = new Error(`Unknown error occurred while fetching ${url}`);
    (wrappedError as any).originalError = error;
    console.error(`Fetch error for ${url}:`, wrappedError);
    throw wrappedError;
  }
};

interface FinancialContextValue {
  transactions: Transaction[] | undefined;
  isLoading: boolean;
  error: any;
  addTransaction: (txData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateTransaction: (id: string, txData: Partial<Transaction>) => Promise<void>;
  deleteTransaction: (id: string) => Promise<boolean>;
}

const FinancialContext = createContext<FinancialContextValue | undefined>(undefined);

export function FinancialProvider({ children }: { children: ReactNode }) {
  const apiUrl = '/api/transactions';

  // Use the reusable SWR config factory
  const { data: transactions, error, isLoading } = useSWR<Transaction[]>(
    apiUrl, 
    fetcher, 
    createSWRConfig()
  );

  // Helper to generate timestamp once and reuse
  const getTimestamp = useCallback(() => new Date().toISOString(), []);

  // Helper to handle API errors consistently
  const handleApiError = useCallback((error: any, operation: string) => {
    console.error(`Error ${operation}:`, error);
    // You could add more sophisticated error handling here, like showing a toast notification
    return error;
  }, []);

  const addTransaction = useCallback(async (txData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>) => {
    const timestamp = getTimestamp();
    const tempId = `temp-${Date.now()}`;

    // Optimistic update with a temporary ID
    mutate(
      apiUrl, 
      (currentData: Transaction[] | undefined = []) => [
        ...currentData,
        { 
          ...txData, 
          id: tempId, 
          createdAt: timestamp, 
          updatedAt: timestamp 
        } as Transaction
      ], 
      false
    );

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...txData,
          createdAt: timestamp,
          updatedAt: timestamp
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to add transaction: ${response.status} ${errorText}`);
      }

      // Revalidate to get the server-generated ID
      mutate(apiUrl);
      return await response.json();
    } catch (err) {
      handleApiError(err, 'adding transaction');
      // Revalidate to remove the optimistic update if it failed
      mutate(apiUrl);
      throw err;
    }
  }, [apiUrl, getTimestamp, handleApiError]);

  const updateTransaction = useCallback(async (id: string, txData: Partial<Transaction>) => {
    const updateUrl = `${apiUrl}/${id}`;
    const timestamp = getTimestamp();

    // Optimistic update with consistent timestamp
    mutate(
      apiUrl, 
      (currentData: Transaction[] | undefined = []) =>
        currentData.map(t => 
          t.id === id ? { ...t, ...txData, updatedAt: timestamp } : t
        ), 
      false
    );

    try {
      const response = await fetch(updateUrl, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...txData,
          updatedAt: timestamp
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update transaction: ${response.status} ${errorText}`);
      }

      // Revalidate both the collection and individual resource
      mutate(apiUrl);
      mutate(updateUrl);
      return await response.json();
    } catch (err) {
      handleApiError(err, `updating transaction ${id}`);
      // Revalidate to revert the optimistic update if it failed
      mutate(apiUrl);
      throw err;
    }
  }, [apiUrl, getTimestamp, handleApiError]);

  const deleteTransaction = useCallback(async (id: string) => {
    const deleteUrl = `${apiUrl}/${id}`;

    // Store the original data for potential rollback
    let originalData: Transaction[] | undefined;

    // Optimistic update - remove the transaction from the list
    mutate(
      apiUrl, 
      (currentData: Transaction[] | undefined = []) => {
        originalData = [...currentData];
        return currentData.filter(t => t.id !== id);
      }, 
      false
    );

    try {
      const response = await fetch(deleteUrl, { 
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.status !== 204 && !response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete transaction: ${response.status} ${errorText}`);
      }

      // Success - no need to revalidate as our optimistic update is correct
      return true;
    } catch (err) {
      handleApiError(err, `deleting transaction ${id}`);

      // Rollback the optimistic update by restoring the original data
      if (originalData) {
        mutate(apiUrl, originalData, false);
      } else {
        // If we don't have the original data, revalidate from the server
        mutate(apiUrl);
      }

      throw err;
    }
  }, [apiUrl, handleApiError]);

  const value: FinancialContextValue = {
    transactions,
    isLoading,
    error,
    addTransaction,
    updateTransaction,
    deleteTransaction,
  };

  return (
    <FinancialContext.Provider value={value}>
      {children}
    </FinancialContext.Provider>
  );
}

export function useFinancialStore() {
  const context = useContext(FinancialContext);

  if (context === undefined) {
    throw new Error('useFinancialStore must be used within a FinancialProvider');
  }

  return context;
}

// Create a reusable SWR config factory to ensure consistency
export const createSWRConfig = (customConfig = {}) => ({
  revalidateOnFocus: false,
  revalidateIfStale: true,
  revalidateOnMount: true,
  dedupingInterval: 10000,
  focusThrottleInterval: 10000,
  errorRetryCount: 3,
  suspense: false,
  shouldRetryOnError: false,
  keepPreviousData: true,
  onErrorRetry: (error: Error, key: string, config: any, revalidate: any, { retryCount }: { retryCount: number }) => {
    // Only retry on network errors, not on 4xx or 5xx responses
    if ((error as any).status >= 400) return;

    // Exponential backoff
    const delay = Math.min(1000 * 2 ** retryCount, 30000);
    setTimeout(() => revalidate({ retryCount }), delay);
  },
  ...customConfig
});

export function useFinancialSummary() {
  const apiUrl = '/api/financial-summary';

  // Use the enhanced SWR configuration with a longer cache time for summary data
  const { data, error, isLoading } = useSWR<FinancialSummary>(
    apiUrl, 
    fetcher, 
    createSWRConfig({
      dedupingInterval: 15000,  // Even longer deduping interval for summary data
      refreshInterval: 60000,   // Auto-refresh every minute
    })
  );

  return {
    summary: data,
    isLoadingSummary: isLoading,
    errorSummary: error,
  };
}
