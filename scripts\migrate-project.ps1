# PowerShell script to migrate project outside OneDrive
# This script safely moves the project to C:\dev\ for better development experience

param(
    [string]$TargetPath = "C:\dev\payday-pilot-next",
    [switch]$Force = $false
)

Write-Host "🚀 Project Migration Script" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan
Write-Host ""

$currentPath = Get-Location
$sourcePath = $currentPath.Path

Write-Host "📂 Current location: $sourcePath" -ForegroundColor Yellow
Write-Host "🎯 Target location: $TargetPath" -ForegroundColor Green
Write-Host ""

# Check if we're in OneDrive
if ($sourcePath -notlike "*OneDrive*") {
    Write-Host "ℹ️  Project is not in OneDrive folder. Migration may not be necessary." -ForegroundColor Blue
    $continue = Read-Host "Continue anyway? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        Write-Host "❌ Migration cancelled." -ForegroundColor Red
        exit 0
    }
}

# Check if target directory exists
if (Test-Path $TargetPath) {
    if (-not $Force) {
        Write-Host "⚠️  Target directory already exists: $TargetPath" -ForegroundColor Yellow
        $overwrite = Read-Host "Overwrite existing directory? (y/N)"
        if ($overwrite -ne "y" -and $overwrite -ne "Y") {
            Write-Host "❌ Migration cancelled." -ForegroundColor Red
            exit 0
        }
    }
    
    Write-Host "🗑️  Removing existing target directory..." -ForegroundColor Yellow
    try {
        Remove-Item $TargetPath -Recurse -Force
        Write-Host "✅ Existing directory removed" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to remove existing directory: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Create target directory
Write-Host "📁 Creating target directory..." -ForegroundColor Yellow
try {
    $targetDir = Split-Path $TargetPath -Parent
    if (-not (Test-Path $targetDir)) {
        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
    }
    Write-Host "✅ Target directory structure created" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to create target directory: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Stop OneDrive to prevent sync conflicts
Write-Host "⏸️  Stopping OneDrive sync..." -ForegroundColor Yellow
try {
    Get-Process OneDrive -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 3
    Write-Host "✅ OneDrive stopped" -ForegroundColor Green
}
catch {
    Write-Host "ℹ️  OneDrive not running or couldn't be stopped" -ForegroundColor Blue
}

# Clean build artifacts before migration
Write-Host "🧹 Cleaning build artifacts..." -ForegroundColor Yellow
$itemsToClean = @(".next", ".turbo", "node_modules\.cache", "build-out", "out")
foreach ($item in $itemsToClean) {
    $itemPath = Join-Path $sourcePath $item
    if (Test-Path $itemPath) {
        try {
            Remove-Item $itemPath -Recurse -Force
            Write-Host "  ✅ Cleaned $item" -ForegroundColor Green
        }
        catch {
            Write-Host "  ⚠️  Could not clean $item" -ForegroundColor Yellow
        }
    }
}

# Copy project files
Write-Host "📋 Copying project files..." -ForegroundColor Yellow
try {
    # Use robocopy for better performance and reliability
    $robocopyArgs = @(
        $sourcePath,
        $TargetPath,
        "/E",           # Copy subdirectories including empty ones
        "/XD",          # Exclude directories
        ".git",
        ".next",
        ".turbo",
        "node_modules",
        "build-out",
        "out",
        "dist",
        "/XF",          # Exclude files
        "*.log",
        "npm-debug.log*",
        "yarn-debug.log*",
        "yarn-error.log*",
        ".DS_Store",
        "Thumbs.db",
        "/R:3",         # Retry 3 times
        "/W:5",         # Wait 5 seconds between retries
        "/MT:8",        # Multi-threaded copy
        "/NFL",         # No file list
        "/NDL",         # No directory list
        "/NP"           # No progress
    )
    
    $result = Start-Process -FilePath "robocopy" -ArgumentList $robocopyArgs -Wait -PassThru -NoNewWindow
    
    # Robocopy exit codes: 0-7 are success, 8+ are errors
    if ($result.ExitCode -le 7) {
        Write-Host "✅ Project files copied successfully" -ForegroundColor Green
    }
    else {
        throw "Robocopy failed with exit code: $($result.ExitCode)"
    }
}
catch {
    Write-Host "❌ Failed to copy project files: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "🔄 Trying alternative copy method..." -ForegroundColor Yellow
    
    try {
        Copy-Item $sourcePath -Destination $TargetPath -Recurse -Force -Exclude @("node_modules", ".next", ".turbo", "build-out", "out", "dist")
        Write-Host "✅ Project files copied with alternative method" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Alternative copy method also failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Change to new directory
Write-Host "📂 Changing to new directory..." -ForegroundColor Yellow
try {
    Set-Location $TargetPath
    Write-Host "✅ Changed to: $(Get-Location)" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to change directory: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
try {
    npm install
    Write-Host "✅ Dependencies installed" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to install dependencies: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 You can install them manually later with: npm install" -ForegroundColor Blue
}

# Test build
Write-Host "🏗️  Testing build..." -ForegroundColor Yellow
try {
    npm run build
    Write-Host "✅ Build test successful!" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  Build test failed, but project migration completed" -ForegroundColor Yellow
    Write-Host "💡 You may need to run: npm run build:windows-safe" -ForegroundColor Blue
}

# Restart OneDrive
Write-Host "🔄 Restarting OneDrive..." -ForegroundColor Yellow
try {
    Start-Process "$env:LOCALAPPDATA\Microsoft\OneDrive\OneDrive.exe"
    Write-Host "✅ OneDrive restarted" -ForegroundColor Green
}
catch {
    Write-Host "ℹ️  Please restart OneDrive manually" -ForegroundColor Blue
}

Write-Host ""
Write-Host "🎉 Project migration completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Update your IDE/editor to open: $TargetPath" -ForegroundColor White
Write-Host "   2. Update any bookmarks or shortcuts" -ForegroundColor White
Write-Host "   3. Consider deleting the old OneDrive copy after verification" -ForegroundColor White
Write-Host "   4. Your builds should now work without EPERM errors!" -ForegroundColor White
Write-Host ""
Write-Host "💡 Benefits of the new location:" -ForegroundColor Cyan
Write-Host "   ✅ No OneDrive sync conflicts" -ForegroundColor Green
Write-Host "   ✅ Better file system performance" -ForegroundColor Green
Write-Host "   ✅ No Windows permission issues" -ForegroundColor Green
Write-Host "   ✅ Faster builds and development" -ForegroundColor Green
