'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import NotificationCard from './NotificationCard';

interface Notification {
  id: string;
  title: string;
  body: string;
  date: string;
  read: boolean;
  type?: string;
  billId?: string;
}

interface NotificationsModalProps {
  className?: string;
}

export function NotificationsModal({ className = '' }: NotificationsModalProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [readStatusMap, setReadStatusMap] = useState<Record<string, boolean>>({});
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');
  const [isLoading, setIsLoading] = useState(true);

  // Load notifications and read status from localStorage
  useEffect(() => {
    try {
      const storedNotifications = localStorage.getItem('notifications');
      const storedReadStatus = localStorage.getItem('notificationsReadStatus');

      if (storedNotifications) {
        const parsedNotifications = JSON.parse(storedNotifications);
        setNotifications(parsedNotifications);
      }

      if (storedReadStatus) {
        const parsedReadStatus = JSON.parse(storedReadStatus);
        setReadStatusMap(parsedReadStatus);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save read status to localStorage
  const saveReadStatus = (readStatus: Record<string, boolean>) => {
    try {
      localStorage.setItem('notificationsReadStatus', JSON.stringify(readStatus));
      // Trigger storage event to update badge count in other components
      window.dispatchEvent(new Event('storage'));
    } catch (error) {
      console.error('Error saving read status:', error);
    }
  };

  // Mark notification as read
  const markAsRead = (id: string) => {
    const updatedMap = { ...readStatusMap, [id]: true };
    setReadStatusMap(updatedMap);
    saveReadStatus(updatedMap);
  };

  // Delete notification
  const deleteNotification = (id: string) => {
    const updatedNotifications = notifications.filter(n => n.id !== id);
    setNotifications(updatedNotifications);

    // Remove from read status map
    const updatedReadStatus = { ...readStatusMap };
    delete updatedReadStatus[id];
    setReadStatusMap(updatedReadStatus);

    // Save to localStorage
    try {
      localStorage.setItem('notifications', JSON.stringify(updatedNotifications));
      saveReadStatus(updatedReadStatus);
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  // Mark all as read
  const markAllAsRead = () => {
    const allReadMap = { ...readStatusMap };
    notifications.forEach(notification => {
      allReadMap[notification.id] = true;
    });
    setReadStatusMap(allReadMap);
    saveReadStatus(allReadMap);
  };

  // Clear all notifications
  const clearAll = () => {
    setNotifications([]);
    setReadStatusMap({});

    try {
      localStorage.setItem('notifications', '[]');
      localStorage.removeItem('notificationsReadStatus');
      localStorage.setItem('notificationsCleared', 'true');
      window.dispatchEvent(new Event('storage'));
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  };

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    const isRead = readStatusMap[notification.id] || false;
    switch (filter) {
      case 'unread':
        return !isRead;
      case 'read':
        return isRead;
      default:
        return true;
    }
  });

  const unreadCount = notifications.filter(n => !readStatusMap[n.id]).length;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <div className={`w-full max-w-2xl mx-auto ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex-1">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Notifications
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {unreadCount > 0 ? `${unreadCount} unread notification${unreadCount !== 1 ? 's' : ''}` : 'All caught up!'}
          </p>
        </div>

        {notifications.length > 0 && (
          <div className="flex gap-2 flex-shrink-0">
            {unreadCount > 0 && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={markAllAsRead}
                className="px-3 py-2 text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-200 min-h-[44px] touch-manipulation"
              >
                Mark all read
              </motion.button>
            )}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={clearAll}
              className="px-3 py-2 text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors duration-200 min-h-[44px] touch-manipulation"
            >
              Clear all
            </motion.button>
          </div>
        )}
      </div>

      {/* Filter tabs */}
      {notifications.length > 0 && (
        <div className="flex gap-1 mb-6 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
          {(['all', 'unread', 'read'] as const).map((filterOption) => (
            <motion.button
              key={filterOption}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setFilter(filterOption)}
              className={`
                flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 min-h-[44px] touch-manipulation
                ${filter === filterOption
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }
              `}
            >
              <span className="flex items-center justify-center gap-1">
                {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
                {filterOption === 'unread' && unreadCount > 0 && (
                  <span className="px-1.5 py-0.5 text-xs bg-red-500 text-white rounded-full">
                    {unreadCount}
                  </span>
                )}
              </span>
            </motion.button>
          ))}
        </div>
      )}

      {/* Notifications list */}
      <div className="space-y-3">
        <AnimatePresence mode="popLayout">
          {filteredNotifications.length > 0 ? (
            filteredNotifications.map((notification) => (
              <NotificationCard
                key={notification.id}
                notification={{
                  ...notification,
                  read: readStatusMap[notification.id] || false
                }}
                onClick={() => {
                  if (!readStatusMap[notification.id]) {
                    markAsRead(notification.id);
                  }
                }}
                onMarkAsRead={() => markAsRead(notification.id)}
                onDelete={() => deleteNotification(notification.id)}
              />
            ))
          ) : notifications.length === 0 ? (
            <EmptyState />
          ) : (
            <NoResultsState filter={filter} />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}

// Empty state component
function EmptyState() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-8 sm:py-12"
    >
      <div className="text-5xl sm:text-6xl mb-4">📬</div>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        No notifications yet
      </h3>
      <p className="text-gray-600 dark:text-gray-400 mb-6 px-4">
        You have no notifications yet—bill reminders will show here.
      </p>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 min-h-[44px] touch-manipulation"
      >
        Customize notifications
      </motion.button>
    </motion.div>
  );
}

// No results state component
function NoResultsState({ filter }: { filter: string }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-8 sm:py-12"
    >
      <div className="text-3xl sm:text-4xl mb-3">🔍</div>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        No {filter} notifications
      </h3>
      <p className="text-gray-600 dark:text-gray-400 px-4">
        {filter === 'unread'
          ? "You're all caught up! No unread notifications."
          : filter === 'read'
            ? "No read notifications to show."
            : "No notifications match your current filter."
        }
      </p>
    </motion.div>
  );
}

export default NotificationsModal;
