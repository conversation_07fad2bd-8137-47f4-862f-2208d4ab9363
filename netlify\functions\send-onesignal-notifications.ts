import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@netlify/functions";
import { Timestamp } from "firebase-admin/firestore";
import { db } from "./utils/firebase-admin";

const ONE_SIGNAL_APP_ID = "b5e3ef51-c148-40a7-8728-fa25176cd761";
const ONE_SIGNAL_REST_API_KEY = process.env.ONE_SIGNAL_REST_API_KEY;

interface UserProfile {
  oneSignalPlayerIds?: string[];
  // other user fields
}

interface Bill {
  id: string;
  name: string;
  dueDate: Timestamp; // Using admin SDK Timestamp
  // other bill fields
}

const handler: Handler = async (event: HandlerEvent, context: HandlerContext) => {
  if (!ONE_SIGNAL_REST_API_KEY) {
    console.error("OneSignal REST API Key is not configured in environment variables.");
    return {
      statusCode: 500,
      body: JSON.stringify({ error: "OneSignal REST API Key not configured." }),
    };
  }

  try {
    const usersSnapshot = await db.collection("users").get();
    let notificationsSentCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      const userId = userDoc.id;
      const userData = userDoc.data() as UserProfile;
      const playerIds = userData.oneSignalPlayerIds;

      if (!playerIds || playerIds.length === 0) {
        // console.log(`User ${userId} has no OneSignal Player IDs. Skipping.`);
        continue;
      }

      // Calculate due date range (e.g., bills due in the next 2 days)
      const today = new Date();
      const twoDaysFromNow = new Date(today);
      twoDaysFromNow.setDate(today.getDate() + 2);

      const todayTimestamp = Timestamp.fromDate(today);
      const twoDaysFromNowTimestamp = Timestamp.fromDate(twoDaysFromNow);

      const dueBillsSnapshot = await db.collection(`users/${userId}/bills`)
        .where("dueDate", ">=", todayTimestamp)
        .where("dueDate", "<=", twoDaysFromNowTimestamp)
        .where("paid", "==", false) // Only unpaid bills
        .get();

      if (dueBillsSnapshot.empty) {
        // console.log(`User ${userId} has no bills due soon. Skipping.`);
        continue;
      }

      const dueBills: Bill[] = [];
      dueBillsSnapshot.forEach((doc: any) => {
        dueBills.push({ id: doc.id, ...doc.data() } as Bill);
      });

      const billNames = dueBills.map(bill => bill.name).join(', ');
      const notificationMessage = `Reminder: You have bills due soon: ${billNames}.`;

      const notificationPayload = {
        app_id: ONE_SIGNAL_APP_ID,
        include_player_ids: playerIds,
        headings: { en: "Upcoming Bill Reminder" },
        contents: { en: notificationMessage },
        // small_icon: "ic_stat_onesignal_default", // Optional: ensure this icon exists in your Android res/drawable
        // data: { custom_data_key: "custom_data_value" }, // Optional
      };

      try {
        const response = await fetch("https://api.onesignal.com/notifications", {
          method: "POST",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": `Basic ${ONE_SIGNAL_REST_API_KEY}`,
          },
          body: JSON.stringify(notificationPayload),
        });

        const responseData = await response.json();
        if (response.ok && responseData.id) {
          console.log(`Successfully sent notification to user ${userId} for bills: ${billNames}. OneSignal ID: ${responseData.id}`);
          notificationsSentCount++;
        } else {
          console.error(`Failed to send OneSignal notification to user ${userId}. Status: ${response.status}`, responseData);
        }
      } catch (fetchError) {
        console.error(`Error sending OneSignal notification to user ${userId}:`, fetchError);
      }
    }

    return {
      statusCode: 200,
      body: JSON.stringify({ message: `Notification check complete. Sent ${notificationsSentCount} notifications.` }),
    };
  } catch (error) {
    console.error("Error processing scheduled notifications:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return {
      statusCode: 500,
      body: JSON.stringify({ error: "Failed to process scheduled notifications", details: errorMessage }),
    };
  }
};

export { handler };

