// Give the service worker access to Firebase Messaging.
importScripts('https://www.gstatic.com/firebasejs/11.6.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.6.1/firebase-messaging-compat.js');

// Initialize the Firebase app in the service worker by passing in the messagingSenderId.
// TODO: Replace with your actual Firebase project configuration
firebase.initializeApp({
  apiKey: "AIzaSyA2uCt4w6XMIjDAdCoUmxfrY9Q441XUfRo", // Replace with your actual apiKey
  authDomain: "payday-pilot.firebaseapp.com", // Replace with your actual authDomain
  projectId: "payday-pilot", // Replace with your actual projectId
  storageBucket: "payday-pilot.firebasestorage.app", // Replace with your actual storageBucket
  messagingSenderId: "122539156310", // Replace with your actual messagingSenderId
  appId: "1:122539156310:web:33f3153bfceb0437792cf7", // Replace with your actual appId
  measurementId: "G-GN0V3SX78P" // Replace with your actual measurementId (optional)
});

// Retrieve an instance of Firebase Messaging so that it can handle background messages.
const messaging = firebase.messaging();

messaging.onBackgroundMessage(function (payload) {
  console.log('[firebase-messaging-sw.js] Received background message: ', payload);

  const notificationTitle = payload.data?.title || payload.notification?.title || 'PayDay Pilot';
  const notificationBody = payload.data?.body || payload.notification?.body || 'You have a new message.';

  const notificationOptions = {
    body: notificationBody,
    icon: payload.data?.icon || '/icons/icon-192x192.png', // Use icon from data, fallback to default
    badge: payload.data?.badge || '/icons/icon-72x72.svg', // Use badge from data, fallback to default
    data: { // Pass along all data, especially the URL
      ...payload.data, // Include any other data fields sent from the server
      url: payload.data?.url || '/' // Default URL if not specified
    },
    vibrate: [200, 100, 200], // Add vibration pattern for mobile devices
    tag: 'payday-pilot-notification', // Add a tag to group similar notifications
    renotify: true, // Notify even if replacing an existing notification
  };

  return self.registration.showNotification(notificationTitle, notificationOptions);
});

self.addEventListener('notificationclick', function (event) {
  console.log('[firebase-messaging-sw.js] Notification click Received.', event.notification);

  event.notification.close(); // Android needs explicit close.

  const urlToOpen = event.notification.data?.url || '/';

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then(windowClients => {
      // Check if there is already a window/tab open with the target URL
      for (let i = 0; i < windowClients.length; i++) {
        const client = windowClients[i];
        // If a window/tab matching the URL already exists, focus it
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }
      // If no window/tab is found, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});
