import { Bill } from '../../types/bill';
import {
  BILL_TYPE_PATTERNS,
  calculateNextDueDate,
  createRenewedBill,
  DEFAULT_RENEWAL_OPTIONS,
  detectBillType,
  detectBillTypeWithConfidence,
  processAutomaticRenewal,
  shouldRenewBill
} from '../billRenewal';

// Mock bill data for testing
const createMockBill = (overrides: Partial<Bill> = {}): Bill => ({
  id: 'test-bill-1',
  name: 'Test Bill',
  amount: 100,
  dueDate: '2024-01-15',
  category: 'Utilities',
  billType: 'regular',
  isRecurring: true,
  frequency: 'monthly',
  reminderDays: 3,
  notes: '',
  vendor: '',
  isLoan: false,
  isDebt: false,
  isPaid: false,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  ...overrides
});

describe('Bill Renewal System', () => {
  describe('calculateNextDueDate', () => {
    test('calculates monthly dates correctly', () => {
      expect(calculateNextDueDate('2024-01-15', 'monthly')).toBe('2024-02-15');
      expect(calculateNextDueDate('2024-01-31', 'monthly')).toBe('2024-02-29'); // Leap year
      expect(calculateNextDueDate('2023-01-31', 'monthly')).toBe('2023-02-28'); // Non-leap year
    });

    test('calculates quarterly dates correctly', () => {
      expect(calculateNextDueDate('2024-01-15', 'quarterly')).toBe('2024-04-15');
      expect(calculateNextDueDate('2024-01-31', 'quarterly')).toBe('2024-04-30');
    });

    test('calculates annual dates correctly', () => {
      expect(calculateNextDueDate('2024-02-29', 'annually')).toBe('2025-02-28'); // Leap to non-leap
      expect(calculateNextDueDate('2023-02-28', 'annually')).toBe('2024-02-28'); // Non-leap to leap
    });

    test('calculates weekly dates correctly', () => {
      expect(calculateNextDueDate('2024-01-15', 'weekly')).toBe('2024-01-22');
    });

    test('calculates biweekly dates correctly', () => {
      expect(calculateNextDueDate('2024-01-15', 'biweekly')).toBe('2024-01-29');
    });

    test('handles business day adjustment', () => {
      // Saturday -> Monday
      const saturday = calculateNextDueDate('2024-01-13', 'weekly', { respectBusinessDays: true });
      expect(saturday).toBe('2024-01-22'); // Should move to Monday

      // Sunday -> Monday
      const sunday = calculateNextDueDate('2024-01-14', 'weekly', { respectBusinessDays: true });
      expect(sunday).toBe('2024-01-22'); // Should move to Monday
    });

    test('returns null for invalid dates', () => {
      expect(calculateNextDueDate('invalid-date', 'monthly')).toBeNull();
    });
  });

  describe('detectBillType', () => {
    test('detects utility bills', () => {
      const result = detectBillType('Electric Bill', 'Utilities');
      expect(result).toBeTruthy();
      expect(result?.isRecurring).toBe(true);
      expect(result?.defaultFrequency).toBe('monthly');
    });

    test('detects rent bills', () => {
      const result = detectBillType('Monthly Rent Payment');
      expect(result).toBeTruthy();
      expect(result?.category).toBe('Housing');
      expect(result?.defaultFrequency).toBe('monthly');
    });

    test('detects subscription bills', () => {
      const result = detectBillType('Netflix Subscription');
      expect(result).toBeTruthy();
      expect(result?.category).toBe('Subscriptions');
    });

    test('detects quarterly bills', () => {
      const result = detectBillType('Quarterly Tax Payment');
      expect(result).toBeTruthy();
      expect(result?.defaultFrequency).toBe('quarterly');
    });

    test('returns null for unrecognized bills', () => {
      const result = detectBillType('Random Bill Name');
      expect(result).toBeNull();
    });
  });

  describe('detectBillTypeWithConfidence', () => {
    test('provides confidence scores', () => {
      const result = detectBillTypeWithConfidence('Electric Bill', 'Utilities', 150);
      expect(result.confidence).toBeGreaterThan(50);
      expect(result.pattern).toBeTruthy();
      expect(result.suggestions.length).toBeGreaterThan(0);
    });

    test('gives higher confidence for exact matches', () => {
      const exact = detectBillTypeWithConfidence('rent', 'Housing');
      const partial = detectBillTypeWithConfidence('apartment rent payment', 'Housing');
      expect(exact.confidence).toBeGreaterThan(partial.confidence);
    });

    test('considers amount in confidence calculation', () => {
      const highRent = detectBillTypeWithConfidence('rent', 'Housing', 2000);
      const lowRent = detectBillTypeWithConfidence('rent', 'Housing', 200);
      expect(highRent.confidence).toBeGreaterThan(lowRent.confidence);
    });
  });

  describe('createRenewedBill', () => {
    test('creates renewed bill with correct properties', () => {
      const originalBill = createMockBill({
        isPaid: true,
        paidDate: '2024-01-15',
        isRenewedOriginal: false
      });

      const renewed = createRenewedBill(originalBill, '2024-02-15');

      expect(renewed.name).toBe(originalBill.name);
      expect(renewed.amount).toBe(originalBill.amount);
      expect(renewed.dueDate).toBe('2024-02-15');
      expect(renewed.isPaid).toBe(false);
      expect(renewed.paidDate).toBeUndefined();
      expect(renewed.renewalOfBillId).toBe(originalBill.id);
      expect(renewed.isRenewedOriginal).toBe(false);
    });

    test('clears calculated fields', () => {
      const originalBill = createMockBill({
        calculatedPayment: 100,
        totalInterest: 50,
        payoffDate: '2025-01-01'
      });

      const renewed = createRenewedBill(originalBill, '2024-02-15');

      expect(renewed.calculatedPayment).toBeUndefined();
      expect(renewed.totalInterest).toBeUndefined();
      expect(renewed.payoffDate).toBeUndefined();
    });
  });

  describe('shouldRenewBill', () => {
    test('should renew explicitly recurring bills', () => {
      const bill = createMockBill({ isRecurring: true, isPaid: true });
      const result = shouldRenewBill(bill);
      expect(result.shouldRenew).toBe(true);
    });

    test('should not renew one-time bills when skipOneTime is true', () => {
      const bill = createMockBill({ isRecurring: false });
      const result = shouldRenewBill(bill, { skipOneTime: true });
      expect(result.shouldRenew).toBe(false);
      expect(result.reason).toContain('one-time');
    });

    test('should not renew already renewed bills', () => {
      const bill = createMockBill({ isRenewedOriginal: true });
      const result = shouldRenewBill(bill);
      expect(result.shouldRenew).toBe(false);
      expect(result.reason).toContain('already has been renewed');
    });

    test('should auto-detect recurring bills when enabled', () => {
      const bill = createMockBill({
        name: 'Electric Bill',
        isRecurring: undefined,
        category: 'Utilities'
      });
      const result = shouldRenewBill(bill, { autoDetectRecurring: true });
      expect(result.shouldRenew).toBe(true);
      expect(result.reason).toContain('Auto-detected');
    });

    test('should not renew unpaid bills unless significantly overdue', () => {
      const recentBill = createMockBill({
        isPaid: false,
        dueDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 5 days ago
      });
      const result = shouldRenewBill(recentBill);
      expect(result.shouldRenew).toBe(false);
      expect(result.reason).toContain('not yet paid');
    });
  });

  describe('processAutomaticRenewal', () => {
    test('processes renewal for valid recurring bill', () => {
      const bill = createMockBill({
        isRecurring: true,
        frequency: 'monthly',
        isPaid: true
      });

      const result = processAutomaticRenewal(bill, DEFAULT_RENEWAL_OPTIONS);
      expect(result.success).toBe(true);
      expect(result.renewedBill).toBeTruthy();
      expect(result.renewedBill?.dueDate).toBe('2024-02-15');
    });

    test('skips renewal for non-recurring bills', () => {
      const bill = createMockBill({ isRecurring: false });
      const result = processAutomaticRenewal(bill, DEFAULT_RENEWAL_OPTIONS);
      expect(result.success).toBe(false);
      expect(result.skipped).toBe(true);
    });

    test('handles bills without frequency by auto-detecting', () => {
      const bill = createMockBill({
        name: 'Electric Bill',
        isRecurring: true,
        frequency: undefined
      });

      const result = processAutomaticRenewal(bill, {
        ...DEFAULT_RENEWAL_OPTIONS,
        autoDetectRecurring: true
      });
      expect(result.success).toBe(true);
      expect(result.renewedBill?.dueDate).toBe('2024-02-15'); // Should default to monthly
    });

    test('respects advance generation limits', () => {
      // Use a date that's definitely far in the future (3 months from now)
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 3);
      const futureDateString = futureDate.toISOString().split('T')[0];

      const bill = createMockBill({
        isRecurring: true,
        frequency: 'monthly',
        dueDate: futureDateString,
        isPaid: true, // Make sure it's paid so it passes the payment check
        paidDate: '2023-01-15' // Add a paidDate to ensure it passes the payment check
      });

      const result = processAutomaticRenewal(bill, {
        ...DEFAULT_RENEWAL_OPTIONS,
        advanceMonths: 1
      });
      expect(result.success).toBe(false);
      expect(result.skipped).toBe(true);
      expect(result.reason).toContain('beyond advance generation limit');
    });
  });

  describe('BILL_TYPE_PATTERNS', () => {
    test('contains expected bill types', () => {
      expect(BILL_TYPE_PATTERNS.utilities).toBeTruthy();
      expect(BILL_TYPE_PATTERNS.rent).toBeTruthy();
      expect(BILL_TYPE_PATTERNS.subscriptions).toBeTruthy();
      expect(BILL_TYPE_PATTERNS.insurance).toBeTruthy();
    });

    test('all patterns have required properties', () => {
      Object.values(BILL_TYPE_PATTERNS).forEach(pattern => {
        expect(pattern.keywords).toBeDefined();
        expect(pattern.keywords.length).toBeGreaterThan(0);
        expect(pattern.defaultFrequency).toBeDefined();
        expect(typeof pattern.isRecurring).toBe('boolean');
      });
    });
  });
});
