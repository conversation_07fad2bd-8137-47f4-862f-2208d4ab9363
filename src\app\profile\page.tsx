'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface UserProfile {
  name: string;
  email: string;
  avatar: string;
  currency: string;
  monthlyIncome: number;
  monthlyExpenses: number;
}

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const [profile, setProfile] = useState<UserProfile>({
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/images/avatar-placeholder.png',
    currency: 'USD',
    monthlyIncome: 5000,
    monthlyExpenses: 3500,
  });
  
  const [formData, setFormData] = useState<UserProfile>(profile);
  
  // Load profile from localStorage on mount
  useEffect(() => {
    try {
      const savedProfile = localStorage.getItem('user_profile');
      if (savedProfile) {
        setProfile(JSON.parse(savedProfile));
        setFormData(JSON.parse(savedProfile));
      }
    } catch (error) {
      console.error('Failed to load profile data:', error);
    }
  }, []);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'monthlyIncome' || name === 'monthlyExpenses' 
        ? parseFloat(value) || 0 
        : value
    }));
  };
  
  const handleSave = () => {
    setProfile(formData);
    setIsEditing(false);
    
    // Save to localStorage
    try {
      localStorage.setItem('user_profile', JSON.stringify(formData));
    } catch (error) {
      console.error('Failed to save profile data:', error);
    }
  };
  
  const handleCancel = () => {
    setFormData(profile);
    setIsEditing(false);
  };
  
  const currencyOptions = [
    { value: 'USD', label: 'US Dollar ($)' },
    { value: 'EUR', label: 'Euro (€)' },
    { value: 'GBP', label: 'British Pound (£)' },
    { value: 'JPY', label: 'Japanese Yen (¥)' },
    { value: 'CAD', label: 'Canadian Dollar (C$)' },
    { value: 'AUD', label: 'Australian Dollar (A$)' },
    { value: 'CNY', label: 'Chinese Yuan (¥)' },
    { value: 'INR', label: 'Indian Rupee (₹)' },
    { value: 'BRL', label: 'Brazilian Real (R$)' },
    { value: 'MXN', label: 'Mexican Peso (Mex$)' },
  ];
  
  const getCurrencySymbol = (currencyCode: string) => {
    const currency = currencyOptions.find(c => c.value === currencyCode);
    if (!currency) return '$';
    return currency.label.match(/\(([^)]+)\)/)?.[1] || '$';
  };
  
  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
        {/* Profile Header */}
        <div className="relative h-32 bg-gradient-to-r from-primary to-primary-dark">
          <div className="absolute -bottom-16 left-6">
            <div className="relative w-32 h-32 rounded-full border-4 border-white dark:border-gray-800 overflow-hidden bg-white dark:bg-gray-700">
              {profile.avatar ? (
                <Image 
                  src={profile.avatar} 
                  alt={profile.name} 
                  fill 
                  className="object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-4xl font-bold text-gray-300">
                  {profile.name.charAt(0)}
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Profile Content */}
        <div className="pt-20 px-6 pb-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h1 className="text-2xl font-bold">{profile.name}</h1>
              <p className="text-gray-500 dark:text-gray-400">{profile.email}</p>
            </div>
            
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Edit Profile
              </button>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                >
                  Save
                </button>
              </div>
            )}
          </div>
          
          {/* Financial Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Monthly Income</h3>
              <p className="text-2xl font-bold">{getCurrencySymbol(profile.currency)}{profile.monthlyIncome.toLocaleString()}</p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Monthly Expenses</h3>
              <p className="text-2xl font-bold">{getCurrencySymbol(profile.currency)}{profile.monthlyExpenses.toLocaleString()}</p>
            </div>
          </div>
          
          {/* Edit Form */}
          {isEditing ? (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="avatar" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Profile Picture URL
                </label>
                <input
                  type="text"
                  id="avatar"
                  name="avatar"
                  value={formData.avatar}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                  placeholder="https://example.com/avatar.jpg"
                />
              </div>
              
              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Preferred Currency
                </label>
                <select
                  id="currency"
                  name="currency"
                  value={formData.currency}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                >
                  {currencyOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="monthlyIncome" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Monthly Income
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        {getCurrencySymbol(formData.currency)}
                      </span>
                    </div>
                    <input
                      type="number"
                      id="monthlyIncome"
                      name="monthlyIncome"
                      value={formData.monthlyIncome}
                      onChange={handleInputChange}
                      className="w-full pl-8 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="monthlyExpenses" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Monthly Expenses
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        {getCurrencySymbol(formData.currency)}
                      </span>
                    </div>
                    <input
                      type="number"
                      id="monthlyExpenses"
                      name="monthlyExpenses"
                      value={formData.monthlyExpenses}
                      onChange={handleInputChange}
                      className="w-full pl-8 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h2 className="text-lg font-medium mb-4">Account Information</h2>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Currency</h3>
                      <p>{currencyOptions.find(c => c.value === profile.currency)?.label || 'US Dollar ($)'}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Account Created</h3>
                      <p>January 15, 2023</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h2 className="text-lg font-medium mb-4">Financial Overview</h2>
                
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Savings Rate</h3>
                    <p className="text-lg font-medium">
                      {profile.monthlyIncome > 0 
                        ? ((1 - profile.monthlyExpenses / profile.monthlyIncome) * 100).toFixed(1) 
                        : '0.0'}%
                    </p>
                  </div>
                  
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-primary rounded-full" 
                      style={{ 
                        width: `${profile.monthlyIncome > 0 
                          ? Math.min(100, (profile.monthlyExpenses / profile.monthlyIncome) * 100) 
                          : 0}%` 
                      }}
                    />
                  </div>
                  
                  <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
                    <span>Expenses</span>
                    <span>Income</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
