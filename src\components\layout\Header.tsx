﻿'use client';

import { useAuth } from '@/hooks/useAuth';
import { useAuthStore } from '@/stores/authStore';
import { Bars3Icon, HomeIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function Header() {
    const { user, isAuthenticated } = useAuth();
    const { signOut } = useAuthStore();
    const [scrolled, setScrolled] = useState(false);
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const pathname = usePathname();

    // Handle scroll effect for header with smoother transitions
    useEffect(() => {
        const handleScroll = () => {
            const offset = window.scrollY;
            setScrolled(offset > 20);
        };

        window.addEventListener('scroll', handleScroll, { passive: true });
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    // Close mobile menu when route changes
    useEffect(() => {
        setMobileMenuOpen(false);
    }, [pathname]);

    // Enhanced navigation links with active state indicators
    const navigationLinks = [
        { href: '/dashboard', label: 'Dashboard', icon: HomeIcon },
    ];

    const isActive = (href: string) => pathname === href;

    return (
        <header
            className={`
                fixed top-0 left-0 right-0 z-30 
                transition-all duration-300 ease-out
                ${scrolled
                    ? 'bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm shadow-lg border-b border-neutral-200 dark:border-neutral-800'
                    : 'bg-transparent'
                }
            `}
            role="banner"
        >
            <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
                <div className='flex justify-between items-center h-16'>
                    {/* Logo with enhanced styling */}
                    <div className='flex-shrink-0'>
                        <Link
                            href='/dashboard'
                            className='flex items-center group focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg p-1 -m-1'
                            aria-label="PaydayPilot - Go to dashboard"
                        >
                            <div className='h-8 w-8 bg-primary-600 rounded-lg mr-3 flex items-center justify-center group-hover:bg-primary-700 transition-colors duration-200'>
                                <span className='text-white font-semibold text-sm'>PP</span>
                            </div>
                            <span className='heading-lg font-bold text-neutral-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200'>
                                PaydayPilot
                            </span>
                        </Link>
                    </div>

                    {/* Navigation for authenticated users */}
                    {isAuthenticated && (
                        <>
                            {/* Desktop Navigation */}
                            <nav className="hidden md:flex space-x-6" aria-label="Main navigation">
                                {navigationLinks.map(({ href, label, icon: Icon }) => (
                                    <Link
                                        key={href}
                                        href={href}
                                        className={`
                                            inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg
                                            transition-all duration-200 ease-out
                                            focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
                                            ${isActive(href)
                                                ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-950'
                                                : 'text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-neutral-800'
                                            }
                                        `}
                                        aria-current={isActive(href) ? 'page' : undefined}
                                    >
                                        <Icon className="w-4 h-4 mr-2" aria-hidden="true" />
                                        {label}
                                    </Link>
                                ))}
                            </nav>

                            {/* Mobile menu button */}
                            <button
                                type="button"
                                className="md:hidden inline-flex items-center justify-center p-2 rounded-lg text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
                                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                                aria-expanded={mobileMenuOpen}
                                aria-label="Toggle mobile menu"
                            >
                                <Bars3Icon className="w-6 h-6" aria-hidden="true" />
                            </button>
                        </>
                    )}
                </div>

                {/* Mobile Navigation Menu */}
                {isAuthenticated && mobileMenuOpen && (
                    <nav
                        className="md:hidden py-4 border-t border-neutral-200 dark:border-neutral-800"
                        aria-label="Mobile navigation"
                    >
                        <div className="space-y-2">
                            {navigationLinks.map(({ href, label, icon: Icon }) => (
                                <Link
                                    key={href}
                                    href={href}
                                    className={`
                                        flex items-center px-3 py-2 text-sm font-medium rounded-lg
                                        transition-all duration-200 ease-out
                                        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
                                        ${isActive(href)
                                            ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-950'
                                            : 'text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-neutral-800'
                                        }
                                    `}
                                    aria-current={isActive(href) ? 'page' : undefined}
                                >
                                    <Icon className="w-4 h-4 mr-3" aria-hidden="true" />
                                    {label}
                                </Link>
                            ))}
                        </div>
                    </nav>
                )}
            </div>
        </header>
    );
}
