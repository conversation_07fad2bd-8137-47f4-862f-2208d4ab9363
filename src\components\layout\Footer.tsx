'use client';

import useMediaQuery from '@/hooks/useMediaQuery';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import Image from 'next/image';
import { useMemo, useState } from 'react';

export default function Footer() {
    const [year] = useState(() => new Date().getFullYear());
    const [expandedSection, setExpandedSection] = useState<string | null>(null);
    const isDesktop = useMediaQuery('(min-width: 768px)');

    // Overall compact toggle for mobile: collapsed by default
    const [mobileExpanded, setMobileExpanded] = useState(false);
    const showExpanded = useMemo(() => (isDesktop ? true : mobileExpanded), [isDesktop, mobileExpanded]);

    const toggleSection = (section: string) => {
        if (expandedSection === section) {
            setExpandedSection(null);
        } else {
            setExpandedSection(section);
        }
    };

    return (
        <footer className="bg-white dark:bg-neutral-900 border-t border-neutral-200 dark:border-neutral-800 mobile-footer">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Compact header row on mobile */}
                {!isDesktop && (
                    <div className="py-3 flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Image src="/icons/icon-48x48.png" alt="PaydayPilot" width={24} height={24} className="h-6 w-6 rounded-md" priority />
                            <span className="font-semibold text-neutral-900 dark:text-white">PaydayPilot</span>
                        </div>
                        <button
                            type="button"
                            className="flex items-center gap-1 text-neutral-600 dark:text-neutral-300 px-2 py-1 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-800"
                            onClick={() => setMobileExpanded(v => !v)}
                            aria-expanded={showExpanded}
                            aria-controls="footer-sections"
                        >
                            {showExpanded ? <ChevronUpIcon className="h-5 w-5" /> : <ChevronDownIcon className="h-5 w-5" />}
                            <span className="text-sm">{showExpanded ? 'Less' : 'More'}</span>
                        </button>
                    </div>
                )}

                {showExpanded && (
                    <div className="py-4 md:py-12" id="footer-sections">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
                            {/* Logo and description */}
                            <div className="col-span-1 md:col-span-1">
                                <div className="flex items-center">
                                    <div className="h-8 w-8 bg-primary-600 rounded-lg mr-3 flex items-center justify-center">
                                        <span className="text-white font-semibold text-sm">PP</span>
                                    </div>
                                    <span className="heading-lg font-bold text-neutral-900 dark:text-white">PaydayPilot</span>
                                </div>
                                <p className="mt-4 body-sm text-neutral-600 dark:text-neutral-400 leading-relaxed">
                                    Stay on top of your finances with bill reminders, expense tracking, and smart financial insights.
                                </p>
                                <div className="mt-6 flex space-x-4">
                                    <a
                                        href="#"
                                        className="text-neutral-500 hover:text-primary-600 dark:text-neutral-400 dark:hover:text-primary-400 transition-colors duration-200"
                                        aria-label="Follow us on Twitter"
                                    >
                                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                                        </svg>
                                    </a>
                                    <a
                                        href="#"
                                        className="text-neutral-500 hover:text-primary-600 dark:text-neutral-400 dark:hover:text-primary-400 transition-colors duration-200"
                                        aria-label="View our GitHub repository"
                                    >
                                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                                        </svg>
                                    </a>
                                </div>
                            </div>

                            {/* Quick links */}
                            <div className="col-span-1">
                                <button
                                    className="flex justify-between items-center w-full md:block py-3 md:py-0 rounded-lg md:rounded-none hover:bg-neutral-100 dark:hover:bg-neutral-800 md:hover:bg-transparent px-3 md:px-0 transition-colors duration-200"
                                    onClick={() => toggleSection('app')}
                                >
                                    <h3 className="caption-md font-semibold text-neutral-900 dark:text-white tracking-wider uppercase">
                                        App
                                    </h3>
                                    <span className="md:hidden">
                                        {expandedSection === 'app' ? (
                                            <ChevronUpIcon className="h-5 w-5 text-neutral-500" />
                                        ) : (
                                            <ChevronDownIcon className="h-5 w-5 text-neutral-500" />
                                        )}
                                    </span>
                                </button>
                                <ul className={`mt-3 md:mt-4 space-y-3 transition-all duration-300 ${isDesktop || expandedSection === 'app' ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden md:max-h-96 md:opacity-100'}`}>
                                    <li>
                                        <Link href="/dashboard" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Dashboard
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/bills" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Bills
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/calendar" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Calendar
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/reports" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Reports
                                        </Link>
                                    </li>
                                </ul>
                            </div>

                            {/* Resources */}
                            <div className="col-span-1">
                                <button
                                    className="flex justify-between items-center w-full md:block py-3 md:py-0 rounded-lg md:rounded-none hover:bg-neutral-100 dark:hover:bg-neutral-800 md:hover:bg-transparent px-3 md:px-0 transition-colors duration-200"
                                    onClick={() => toggleSection('resources')}
                                >
                                    <h3 className="caption-md font-semibold text-neutral-900 dark:text-white tracking-wider uppercase">
                                        Resources
                                    </h3>
                                    <span className="md:hidden">
                                        {expandedSection === 'resources' ? (
                                            <ChevronUpIcon className="h-5 w-5 text-neutral-500" />
                                        ) : (
                                            <ChevronDownIcon className="h-5 w-5 text-neutral-500" />
                                        )}
                                    </span>
                                </button>
                                <ul className={`mt-3 md:mt-4 space-y-3 transition-all duration-300 ${isDesktop || expandedSection === 'resources' ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden md:max-h-96 md:opacity-100'}`}>
                                    <li>
                                        <Link href="/help" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Help Center
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/guides" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Guides
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/blog" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Blog
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/support" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Support
                                        </Link>
                                    </li>
                                </ul>
                            </div>

                            {/* Legal */}
                            <div className="col-span-1">
                                <button
                                    className="flex justify-between items-center w-full md:block py-3 md:py-0 rounded-lg md:rounded-none hover:bg-neutral-100 dark:hover:bg-neutral-800 md:hover:bg-transparent px-3 md:px-0 transition-colors duration-200"
                                    onClick={() => toggleSection('legal')}
                                >
                                    <h3 className="caption-md font-semibold text-neutral-900 dark:text-white tracking-wider uppercase">
                                        Legal
                                    </h3>
                                    <span className="md:hidden">
                                        {expandedSection === 'legal' ? (
                                            <ChevronUpIcon className="h-5 w-5 text-neutral-500" />
                                        ) : (
                                            <ChevronDownIcon className="h-5 w-5 text-neutral-500" />
                                        )}
                                    </span>
                                </button>
                                <ul className={`mt-3 md:mt-4 space-y-3 transition-all duration-300 ${isDesktop || expandedSection === 'legal' ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden md:max-h-96 md:opacity-100'}`}>
                                    <li>
                                        <Link href="/privacy" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Privacy Policy
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/terms" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Terms of Service
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/cookies" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Cookie Policy
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/security" className="body-sm text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 block py-1">
                                            Security
                                        </Link>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        {/* Bottom section with copyright */}
                        <div className="mt-8 md:mt-12 pt-6 md:pt-8 border-t border-neutral-200 dark:border-neutral-800">
                            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                                <p className="caption-md text-neutral-500 dark:text-neutral-400">
                                    &copy; {year} PaydayPilot. All rights reserved.
                                </p>
                                <div className="flex space-x-6">
                                    <Link href="/accessibility" className="caption-md text-neutral-500 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                                        Accessibility
                                    </Link>
                                    <Link href="/sitemap" className="caption-md text-neutral-500 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                                        Sitemap
                                    </Link>
                                    <Link href="/changelog" className="caption-md text-neutral-500 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                                        Changelog
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

            </div>
        </footer>
    );
}
