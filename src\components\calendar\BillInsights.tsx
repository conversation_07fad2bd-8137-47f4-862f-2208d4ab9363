import { useBillsStore } from '@/stores/billsStore';
import { useFinancialStore } from '@/stores/financialStore';
import { BILL_CATEGORIES } from '@/types/bill';
import { BillPattern, BillPrediction, PaymentRecommendation, analyzeBillPatterns, generatePaymentRecommendations, predictUpcomingBills } from '@/utils/financialIntelligence';
import { ChartBarIcon } from '@heroicons/react/24/outline';
import { useEffect, useMemo, useState } from 'react';

interface BillInsightsProps {
    isLoading?: boolean;
}

export default function BillInsights({ isLoading: initialIsLoading = false }: BillInsightsProps) {
    const { bills } = useBillsStore();
    // Destructure isLoading and error from useFinancialStore
    const { transactions, isLoading: isLoadingTransactions, error: transactionsError } = useFinancialStore();
    const [patterns, setPatterns] = useState<{ [billId: string]: BillPattern }>({});
    const [predictions, setPredictions] = useState<BillPrediction[]>([]);
    const [recommendations, setRecommendations] = useState<PaymentRecommendation[]>([]);

    // Memoize user income transactions
    const userIncome = useMemo(
        () =>
            transactions
                ? transactions.filter(t => t.type === 'income').map(t => ({ amount: t.amount, date: t.date }))
                : [],
        [transactions]
    );

    useEffect(() => {
        if (!bills || bills.length === 0) return;

        // Analyze bill patterns
        const detectedPatterns = analyzeBillPatterns(bills);
        setPatterns(detectedPatterns);

        // Generate predictions
        const billPredictions = predictUpcomingBills(bills, detectedPatterns);
        setPredictions(billPredictions);

        // Use real user income (or empty array)
        const payRecommendations = generatePaymentRecommendations(bills, userIncome);
        setRecommendations(payRecommendations);
    }, [bills, userIncome]);

    // Quick stats calculations
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const unpaidBills = bills?.filter(b => !b.paidDate) || [];
    const overdueBills = unpaidBills.filter(b => new Date(b.dueDate) < today);
    const dueThisWeek = unpaidBills.filter(b => new Date(b.dueDate) >= startOfWeek && new Date(b.dueDate) <= today);
    const dueThisMonth = unpaidBills.filter(b => new Date(b.dueDate) >= startOfMonth && new Date(b.dueDate) <= endOfMonth);
    const totalDueThisWeek = dueThisWeek.reduce((sum, b) => sum + b.amount, 0);
    const totalDueThisMonth = dueThisMonth.reduce((sum, b) => sum + b.amount, 0);

    // On-time payment rate
    const allPayments = bills?.flatMap(b => b.paymentHistory || []) || [];
    const onTimePayments = allPayments.filter(p => p.onTime).length;
    const onTimeRate = allPayments.length > 0 ? Math.round((onTimePayments / allPayments.length) * 100) : null;

    // Category breakdown (bar chart data)
    const categoryTotals: Record<string, number> = {};
    for (const cat of BILL_CATEGORIES) categoryTotals[cat] = 0;
    for (const b of unpaidBills) {
        categoryTotals[b.category || 'Other'] += b.amount;
    }
    const topCategories = Object.entries(categoryTotals)
        .filter(([_, amt]) => amt > 0)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5);

    // Spending trends: this month vs last month
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
    const paidBills = bills?.filter(b => b.paidDate) || [];
    const paidThisMonth = paidBills.filter(b => new Date(b.paidDate!) >= startOfMonth && new Date(b.paidDate!) <= endOfMonth);
    const paidLastMonth = paidBills.filter(b => new Date(b.paidDate!) >= lastMonth && new Date(b.paidDate!) <= endOfLastMonth);
    const totalPaidThisMonth = paidThisMonth.reduce((sum, b) => sum + b.amount, 0);
    const totalPaidLastMonth = paidLastMonth.reduce((sum, b) => sum + b.amount, 0);
    const trend = totalPaidLastMonth > 0 ? Math.round(((totalPaidThisMonth - totalPaidLastMonth) / totalPaidLastMonth) * 100) : null;

    // Smart suggestions
    const recurringBills = unpaidBills.filter(b => b.isRecurring);
    const unusualBills = unpaidBills.filter(b => b.amount > (paidBills.reduce((sum, b) => sum + b.amount, 0) / (paidBills.length || 1)) * 1.5);

    // Next actions
    const nextActions: string[] = [];
    if (overdueBills.length > 0) nextActions.push(`Mark ${overdueBills.length} overdue bill(s) as paid`);
    if (recurringBills.length > 0) nextActions.push('Consider automating recurring bills');
    if (unusualBills.length > 0) nextActions.push('Review unusually high bills');
    if (unpaidBills.length === 0) nextActions.push('All bills are paid! 🎉');

    // Combine initialIsLoading with isLoadingTransactions for a general loading state
    const isLoading = initialIsLoading || isLoadingTransactions;

    if (isLoading) {
        return (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm mb-4 animate-pulse">
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
            </div>
        );
    }

    // Group predictions by confidence
    const lowConfidencePredictions = predictions.filter(p => p.confidence === 'low');

    return (
      <div
        className="relative p-5 md:p-6 bg-card-light dark:bg-card-dark border border-border-light dark:border-border-dark rounded-2xl shadow-lg overflow-hidden"
      >
        <h3 className="font-serif text-xl md:text-2xl font-bold text-text-light dark:text-text-dark tracking-tight mb-4">Bill Insights</h3>

        {/* Handle transactions error state */}
        {transactionsError && (
          <div className="mb-6 p-4 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded-lg">
            <p className="text-sm font-medium text-red-800 dark:text-red-200">
              Could not load transaction-dependent insights: {transactionsError.message || 'Unknown error'}
            </p>
          </div>
        )}

        {/* Quick Stats Section - Some stats depend on bills only, some on transactions (via userIncome for recommendations) */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6 border-b border-border-light dark:border-border-dark pb-4">
            <div>
                <div className="text-xs text-medium-gray dark:text-medium-gray/80">Due This Week</div>
                <div className="font-bold text-lg text-text-light dark:text-text-dark">${totalDueThisWeek.toFixed(2)}</div>
            </div>
            <div>
                <div className="text-xs text-medium-gray dark:text-medium-gray/80">Due This Month</div>
                <div className="font-bold text-lg text-text-light dark:text-text-dark">${totalDueThisMonth.toFixed(2)}</div>
            </div>
            <div>
                <div className="text-xs text-medium-gray dark:text-medium-gray/80">Overdue Bills</div>
                <div className="font-bold text-lg text-red-600 dark:text-red-400">{overdueBills.length}</div>
            </div>
            <div>
                <div className="text-xs text-medium-gray dark:text-medium-gray/80">On-Time Payment Rate</div>
                <div className="font-bold text-lg text-text-light dark:text-text-dark">{onTimeRate !== null ? `${onTimeRate}%` : 'N/A'}</div>
            </div>
        </div>

        {/* Category Breakdown */}
        <div className="mb-6">
            <div className="text-sm font-medium text-text-light dark:text-text-dark mb-2">Top Bill Categories</div>
            <div className="space-y-1">
                {topCategories.length === 0 ? <div className="text-sm text-medium-gray dark:text-medium-gray/80 italic">No unpaid bills</div> : topCategories.map(([cat, amt]) => (
                    <div key={cat} className="flex items-center gap-2 group">
                        <div className="w-24 text-sm text-text-light dark:text-text-dark font-medium font-serif flex-shrink-0">{cat}</div>
                        <div className="flex-1 bg-finance-green/10 dark:bg-finance-green/20 rounded h-3 overflow-hidden relative">
                            <div className="bg-finance-green h-3 rounded transition-all duration-700 ease-in-out" style={{ width: `${Math.min(100, (amt / (topCategories[0]?.[1] || 1)) * 100)}%` }}></div>
                        </div>
                        <div className="w-12 text-sm text-right font-mono text-text-light/90 dark:text-text-dark/90">${amt.toFixed(0)}</div>
                    </div>
                ))}
            </div>
        </div>

        {/* Spending Trends */}
        <div className="mb-6">
            <div className="text-sm font-medium text-text-light dark:text-text-dark mb-1">Spending Trend</div>
            <div className="text-sm text-text-light/90 dark:text-text-dark/90">
                {trend === null ? 'No spending data for last month.' : trend === 0 ? 'Spending unchanged from last month.' : trend > 0 ? `▲ ${trend}% increase from last month.` : `▼ ${Math.abs(trend)}% decrease from last month.`}
            </div>
        </div>

        {/* Combined Predictions & Suggestions Area */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-border-light dark:border-border-dark pt-4">
            {/* Predictions Section */}
            <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm font-medium text-text-light dark:text-text-dark">
                    <ChartBarIcon className="h-5 w-5 text-finance-green" />
                    Bill Predictions
                </div>

                {lowConfidencePredictions.length > 0 ? (
                    <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg border border-yellow-300 dark:border-yellow-700">
                        <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            Unusual amounts expected for {lowConfidencePredictions.length} bills:
                        </p>
                        <ul className="mt-2 space-y-1">
                            {lowConfidencePredictions.map(prediction => (
                                <li key={prediction.billId} className="text-sm text-yellow-700 dark:text-yellow-300">
                                    <span className='font-medium'>{prediction.billName}:</span> ${prediction.predictedAmount.toFixed(2)} ({prediction.reason})
                                </li>
                            ))}
                        </ul>
                    </div>
                ) : (
                    <p className="text-sm text-medium-gray dark:text-medium-gray/80 italic">
                        All upcoming bills appear to be within normal ranges.
                    </p>
                )}
            </div>

            {/* Smart Suggestions / Next Actions */}
            <div className="space-y-3">
              <div className="text-sm font-medium text-text-light dark:text-text-dark">Suggestions</div>
                {nextActions.length > 0 ? (
                  <ul className="list-disc list-outside pl-5 space-y-1">
                    {nextActions.map((action, i) => <li key={i} className="text-sm text-text-light/90 dark:text-text-dark/90">{action}</li>)}
                  </ul>
                ) : (
                  <p className="text-sm text-medium-gray dark:text-medium-gray/80 italic">No specific suggestions right now.</p>
                )}
            </div>
        </div>
      </div>
    );
}
