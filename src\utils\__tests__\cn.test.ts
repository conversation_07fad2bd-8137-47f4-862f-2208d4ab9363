import { cn } from '../cn'

describe('cn utility function', () => {
  it('combines class names correctly', () => {
    const result = cn('class1', 'class2', 'class3')
    expect(result).toBe('class1 class2 class3')
  })

  it('handles conditional classes', () => {
    const result = cn('base-class', true && 'conditional-class', false && 'hidden-class')
    expect(result).toBe('base-class conditional-class')
  })

  it('handles undefined and null values', () => {
    const result = cn('class1', undefined, null, 'class2')
    expect(result).toBe('class1 class2')
  })

  it('handles empty strings', () => {
    const result = cn('class1', '', 'class2')
    expect(result).toBe('class1 class2')
  })

  it('handles arrays of classes', () => {
    const result = cn(['class1', 'class2'], 'class3')
    expect(result).toBe('class1 class2 class3')
  })

  it('handles objects with boolean values', () => {
    const result = cn({
      'class1': true,
      'class2': false,
      'class3': true,
    })
    expect(result).toBe('class1 class3')
  })

  it('merges Tailwind classes correctly', () => {
    // Test that conflicting Tailwind classes are merged properly
    const result = cn('p-4', 'p-6')
    // The exact behavior depends on the implementation
    // tailwind-merge should keep the last conflicting class
    expect(result).toContain('p-')
  })

  it('handles complex combinations', () => {
    const isActive = true
    const isDisabled = false

    const result = cn(
      'base-class',
      {
        'active-class': isActive,
        'disabled-class': isDisabled,
      },
      isActive && 'conditional-active',
      ['array-class1', 'array-class2']
    )

    expect(result).toContain('base-class')
    expect(result).toContain('active-class')
    expect(result).toContain('conditional-active')
    expect(result).toContain('array-class1')
    expect(result).toContain('array-class2')
    expect(result).not.toContain('disabled-class')
  })

  it('handles responsive Tailwind classes', () => {
    const result = cn('p-4', 'md:p-6', 'lg:p-8')
    expect(result).toBe('p-4 md:p-6 lg:p-8')
  })

  it('handles hover and focus states', () => {
    const result = cn('bg-blue-500', 'hover:bg-blue-600', 'focus:bg-blue-700')
    expect(result).toBe('bg-blue-500 hover:bg-blue-600 focus:bg-blue-700')
  })

  it('handles dark mode classes', () => {
    const result = cn('bg-white', 'dark:bg-gray-900', 'text-black', 'dark:text-white')
    expect(result).toBe('bg-white dark:bg-gray-900 text-black dark:text-white')
  })

  it('handles duplicate classes', () => {
    const result = cn('class1', 'class2', 'class1', 'class3', 'class2')
    // clsx doesn't remove duplicates by default, but the result should be valid
    expect(result).toContain('class1')
    expect(result).toContain('class2')
    expect(result).toContain('class3')
  })

  it('handles empty input', () => {
    const result = cn()
    expect(result).toBe('')
  })

  it('handles only falsy values', () => {
    const result = cn(false, null, undefined, '')
    expect(result).toBe('')
  })

  it('preserves important classes', () => {
    const result = cn('!important-class', 'normal-class')
    expect(result).toContain('!important-class')
    expect(result).toContain('normal-class')
  })

  it('handles arbitrary value classes', () => {
    const result = cn('bg-[#ff0000]', 'w-[100px]', 'h-[50vh]')
    expect(result).toBe('bg-[#ff0000] w-[100px] h-[50vh]')
  })

  it('handles variant classes correctly', () => {
    const variant = 'primary'
    const size = 'large'

    const result = cn(
      'button-base',
      {
        'button-primary': variant === 'primary',
        'button-secondary': variant === 'secondary',
        'button-large': size === 'large',
        'button-small': size === 'small',
      }
    )

    expect(result).toContain('button-base')
    expect(result).toContain('button-primary')
    expect(result).toContain('button-large')
    expect(result).not.toContain('button-secondary')
    expect(result).not.toContain('button-small')
  })
})
