'use client';

import { getAuth as getFirebaseAuth } from '@/lib/firebase';
import { useEffect } from 'react';
import OneSignal from 'react-onesignal';

const ONE_SIGNAL_APP_ID = "************************************";

type FirebaseAuthInstance = Awaited<ReturnType<typeof getFirebaseAuth>>;

let authPromise: Promise<FirebaseAuthInstance | null> | null = null;

async function ensureFirebaseAuth(): Promise<FirebaseAuthInstance | null> {
  if (typeof window === 'undefined') {
    return null;
  }

  if (!authPromise) {
    authPromise = getFirebaseAuth()
      .then((authInstance) => authInstance)
      .catch((error) => {
        console.warn('Failed to initialize Firebase auth for OneSignal:', error);
        return null;
      });
  }

  return authPromise;
}

const sendPlayerIdToServer = async (playerId: string) => {
  const auth = await ensureFirebaseAuth();
  const user = auth?.currentUser;

  if (!user) {
    console.log('OneSignal Player ID obtained, but no authenticated user is available. Skipping sync.');
    return;
  }

  if (typeof user.getIdToken !== 'function') {
    console.warn('Firebase user instance does not expose getIdToken; skipping OneSignal sync.');
    return;
  }

  try {
    const idToken = await user.getIdToken();
    const response = await fetch('/api/onesignal-subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${idToken}`,
      },
      body: JSON.stringify({ oneSignalPlayerId: playerId }),
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Successfully sent OneSignal Player ID to server:', result.message);
    } else {
      let errorDetails: unknown;
      try {
        errorDetails = await response.json();
      } catch (parseError) {
        errorDetails = await response.text();
      }
      console.error('Failed to send OneSignal Player ID to server:', response.status, errorDetails);
    }
  } catch (error) {
    console.error('Error sending OneSignal Player ID to server:', error);
  }
};

export default function OneSignalInitializer() {
  useEffect(() => {
    // Prevent execution in SSR environment
    if (typeof window === 'undefined') return;

    // Skip OneSignal initialization in localhost environments
    const isLocalhost = window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1';

    if (isLocalhost) {
      console.log('OneSignal initialization skipped in localhost environment');
      return;
    }

    // Early return if already initialized to prevent multiple initialization attempts
    if ((window as any).OneSignalInitialized) {
      console.log('OneSignal already initialized (react-onesignal).');
      return;
    }

    // Mark as initialized early to prevent potential race conditions
    (window as any).OneSignalInitialized = true;

    try {
      console.log('Initializing OneSignal SDK with react-onesignal...');

      const initSdk = () => {
        OneSignal.init({
          appId: ONE_SIGNAL_APP_ID,
          allowLocalhostAsSecureOrigin: true,
          // IMPORTANT: Use our existing custom service worker to avoid double registration
          serviceWorker: {
            path: '/custom-sw.js',
          },
        })
          .then(() => {
            console.log('OneSignal SDK Initialized via react-onesignal');

            // Setup event listeners after successful initialization
            OneSignal.Notifications.addEventListener('permissionChange', (permission) => {
              console.log('OneSignal permission changed:', permission);
            });

            OneSignal.User.PushSubscription.addEventListener('change', (change) => {
              console.log('Push subscription changed: ', change);
              if (change.current.id) {
                const playerId = change.current.id;
                console.log("OneSignal Player ID:", playerId);
                sendPlayerIdToServer(playerId);
              }
            });

            // Also attempt to send player ID if already subscribed on init
            const currentSubscriptionId = OneSignal.User.PushSubscription.id;
            if (currentSubscriptionId) {
              console.log("OneSignal Player ID (on init):", currentSubscriptionId);
              sendPlayerIdToServer(currentSubscriptionId);
            }
          })
          .catch(error => {
            console.error('Error during OneSignal initialization:', error);
            // Reset initialization flag on error to allow future retry
            (window as any).OneSignalInitialized = false;
          });
      };

      // Defer initialization to prevent blocking the main thread
      if ('requestIdleCallback' in window) {
        (window as any).requestIdleCallback(initSdk);
      } else {
        setTimeout(initSdk, 1500);
      }
    } catch (error) {
      console.error('Error setting up OneSignal initialization:', error);
      // Reset initialization flag on error to allow future retry
      (window as any).OneSignalInitialized = false;
    }
  }, []);

  return null; // This component does not render anything
}
