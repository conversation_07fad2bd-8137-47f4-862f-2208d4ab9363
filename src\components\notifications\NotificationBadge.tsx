'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface NotificationBadgeProps {
  count: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function NotificationBadge({ count, className = '', size = 'md' }: NotificationBadgeProps) {
  if (count <= 0) return null;

  const sizeClasses = {
    sm: 'h-4 w-4 text-xs',
    md: 'h-5 w-5 text-xs',
    lg: 'h-6 w-6 text-sm'
  };

  const displayCount = count > 99 ? '99+' : count.toString();

  return (
    <AnimatePresence>
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0, opacity: 0 }}
        whileHover={{ scale: 1.1 }}
        className={`
          ${sizeClasses[size]}
          bg-red-500 text-white rounded-full
          flex items-center justify-center
          font-bold leading-none
          shadow-lg border-2 border-white dark:border-gray-900
          ${className}
        `}
      >
        {displayCount}
      </motion.div>
    </AnimatePresence>
  );
}

export default NotificationBadge;
