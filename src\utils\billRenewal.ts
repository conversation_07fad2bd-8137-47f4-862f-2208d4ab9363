// Intelligent Bill Renewal System
// Handles automatic bill renewal with smart due date calculation and bill type detection

import { Bill, PaymentFrequency } from '@/types/bill';
import { addDays, addMonths, addQuarters, addWeeks, addYears, format, getDaysInMonth, lastDayOfMonth } from 'date-fns';
import { parseLocalDateString } from './date';

export interface BillRenewalOptions {
  /** How many months in advance to generate renewed bills */
  advanceMonths: number;
  /** Whether to respect business days for certain bill types */
  respectBusinessDays: boolean;
  /** Whether to automatically detect recurring patterns */
  autoDetectRecurring: boolean;
  /** Skip renewal for bills marked as one-time */
  skipOneTime: boolean;
}

export interface RenewalResult {
  success: boolean;
  renewedBill?: Bill;
  error?: string;
  skipped?: boolean;
  reason?: string;
}

export interface BillTypePattern {
  keywords: string[];
  defaultFrequency: PaymentFrequency;
  isRecurring: boolean;
  category?: string;
}

// Common bill type patterns for automatic detection
// Order matters - more specific patterns should come first
export const BILL_TYPE_PATTERNS: Record<string, BillTypePattern> = {
  quarterly: {
    keywords: ['quarterly', 'quarter', 'quarterly tax', 'estimated tax'],
    defaultFrequency: 'quarterly',
    isRecurring: true,
    category: 'Taxes'
  },
  annual: {
    keywords: ['annual', 'yearly', 'year', 'registration', 'license', 'membership'],
    defaultFrequency: 'annually',
    isRecurring: true,
    category: 'Annual'
  },
  utilities: {
    keywords: ['electric', 'electricity', 'gas', 'water', 'sewer', 'utility', 'power', 'energy'],
    defaultFrequency: 'monthly',
    isRecurring: true,
    category: 'Utilities'
  },
  rent: {
    keywords: ['rent', 'rental', 'lease', 'housing', 'apartment', 'condo'],
    defaultFrequency: 'monthly',
    isRecurring: true,
    category: 'Housing'
  },
  mortgage: {
    keywords: ['mortgage', 'home loan', 'house payment'],
    defaultFrequency: 'monthly',
    isRecurring: true,
    category: 'Housing'
  },
  insurance: {
    keywords: ['insurance', 'premium', 'policy', 'coverage'],
    defaultFrequency: 'monthly',
    isRecurring: true,
    category: 'Insurance'
  },
  subscriptions: {
    keywords: ['subscription', 'netflix', 'spotify', 'amazon', 'hulu', 'disney', 'apple', 'google', 'microsoft', 'adobe'],
    defaultFrequency: 'monthly',
    isRecurring: true,
    category: 'Subscriptions'
  },
  phone: {
    keywords: ['phone', 'mobile', 'cell', 'verizon', 'att', 'tmobile', 'sprint'],
    defaultFrequency: 'monthly',
    isRecurring: true,
    category: 'Utilities'
  },
  internet: {
    keywords: ['internet', 'wifi', 'broadband', 'comcast', 'xfinity', 'spectrum', 'cox'],
    defaultFrequency: 'monthly',
    isRecurring: true,
    category: 'Utilities'
  },
  credit_card: {
    keywords: ['credit card', 'visa', 'mastercard', 'amex', 'discover', 'card payment'],
    defaultFrequency: 'monthly',
    isRecurring: true,
    category: 'Credit Cards'
  },
  loan: {
    keywords: ['loan', 'payment', 'auto loan', 'car payment', 'student loan'],
    defaultFrequency: 'monthly',
    isRecurring: true,
    category: 'Loans'
  }
};

/**
 * Detects if a bill is likely recurring based on name and category
 */
export function detectBillType(billName: string, category?: string): BillTypePattern | null {
  const searchText = `${billName} ${category || ''}`.toLowerCase();

  for (const [type, pattern] of Object.entries(BILL_TYPE_PATTERNS)) {
    if (pattern.keywords.some(keyword => searchText.includes(keyword))) {
      return pattern;
    }
  }

  return null;
}

/**
 * Calculates the next due date for a recurring bill with smart edge case handling
 */
export function calculateNextDueDate(
  currentDueDate: string,
  frequency: PaymentFrequency,
  options: Partial<BillRenewalOptions> = {}
): string | null {
  const currentDate = parseLocalDateString(currentDueDate);
  if (!currentDate) {
    console.error('Invalid current due date:', currentDueDate);
    return null;
  }

  let nextDate: Date;

  try {
    switch (frequency) {
      case 'weekly':
        nextDate = addWeeks(currentDate, 1);
        break;

      case 'biweekly':
        nextDate = addWeeks(currentDate, 2);
        break;

      case 'monthly':
        nextDate = calculateNextMonthlyDate(currentDate);
        break;

      case 'quarterly':
        nextDate = calculateNextQuarterlyDate(currentDate);
        break;

      case 'annually':
        nextDate = calculateNextAnnualDate(currentDate);
        break;

      default:
        console.warn('Unknown frequency:', frequency);
        return null;
    }

    // Apply business day adjustment if requested
    if (options.respectBusinessDays) {
      nextDate = adjustForBusinessDays(nextDate);
    }

    return format(nextDate, 'yyyy-MM-dd');
  } catch (error) {
    console.error('Error calculating next due date:', error);
    return null;
  }
}

/**
 * Handles monthly date calculation with edge cases
 */
function calculateNextMonthlyDate(currentDate: Date): Date {
  const currentDay = currentDate.getDate();
  const nextMonth = addMonths(currentDate, 1);
  const daysInNextMonth = getDaysInMonth(nextMonth);

  // Handle month-end dates (e.g., Jan 31 → Feb 28/29)
  if (currentDay > daysInNextMonth) {
    // Use the last day of the next month
    return lastDayOfMonth(nextMonth);
  }

  return new Date(nextMonth.getFullYear(), nextMonth.getMonth(), currentDay);
}

/**
 * Handles quarterly date calculation with edge cases
 */
function calculateNextQuarterlyDate(currentDate: Date): Date {
  const currentDay = currentDate.getDate();
  const nextQuarter = addQuarters(currentDate, 1);
  const daysInNextMonth = getDaysInMonth(nextQuarter);

  // Handle month-end dates
  if (currentDay > daysInNextMonth) {
    return lastDayOfMonth(nextQuarter);
  }

  return new Date(nextQuarter.getFullYear(), nextQuarter.getMonth(), currentDay);
}

/**
 * Handles annual date calculation with leap year considerations
 */
function calculateNextAnnualDate(currentDate: Date): Date {
  const nextYear = addYears(currentDate, 1);

  // Handle February 29th on non-leap years
  if (currentDate.getMonth() === 1 && currentDate.getDate() === 29) {
    // If next year is not a leap year, use February 28th
    const isLeapYear = (year: number) => (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
    if (!isLeapYear(nextYear.getFullYear())) {
      return new Date(nextYear.getFullYear(), 1, 28);
    }
  }

  return nextYear;
}

/**
 * Adjusts date to avoid weekends (moves to next Monday)
 */
function adjustForBusinessDays(date: Date): Date {
  const dayOfWeek = date.getDay();

  // If it's Saturday (6), move to Monday (+2 days)
  if (dayOfWeek === 6) {
    return addDays(date, 2);
  }

  // If it's Sunday (0), move to Monday (+1 day)
  if (dayOfWeek === 0) {
    return addDays(date, 1);
  }

  // Weekday, no adjustment needed
  return date;
}

/**
 * Creates a renewed bill from an original bill
 */
export function createRenewedBill(
  originalBill: Bill,
  newDueDate: string,
  options: Partial<BillRenewalOptions> = {}
): Omit<Bill, 'id'> {
  const now = new Date().toISOString();

  return {
    name: originalBill.name,
    amount: originalBill.amount,
    dueDate: newDueDate,
    category: originalBill.category,
    billType: originalBill.billType,
    isRecurring: originalBill.isRecurring,
    frequency: originalBill.frequency,
    reminderDays: originalBill.reminderDays,
    notes: originalBill.notes,
    vendor: originalBill.vendor,
    isLoan: originalBill.isLoan,
    isDebt: originalBill.isDebt,
    loanInfo: originalBill.loanInfo,
    debtInfo: originalBill.debtInfo,

    // Reset payment status
    isPaid: false,
    paidDate: undefined,

    // Renewal tracking
    renewalOfBillId: originalBill.id,
    isRenewedOriginal: false,

    // Timestamps
    createdAt: now,
    updatedAt: now,

    // Clear calculated fields that should be recalculated
    calculatedPayment: undefined,
    nextPaymentDate: undefined,
    payoffDate: undefined,
    totalInterest: undefined,
    totalPaid: undefined,
    paymentHistory: undefined
  };
}

/**
 * Determines if a bill should be automatically renewed
 */
export function shouldRenewBill(
  bill: Bill,
  options: Partial<BillRenewalOptions> = {}
): { shouldRenew: boolean; reason: string } {
  // Skip if explicitly marked as one-time
  if (options.skipOneTime && bill.isRecurring === false) {
    return { shouldRenew: false, reason: 'Bill marked as one-time payment' };
  }

  // Skip if already renewed
  if (bill.isRenewedOriginal) {
    return { shouldRenew: false, reason: 'Bill already has been renewed' };
  }

  // Skip if not paid yet (unless it's overdue by more than 30 days)
  if (!bill.paidDate) {
    const dueDate = parseLocalDateString(bill.dueDate);
    if (dueDate) {
      const daysPastDue = Math.floor((Date.now() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
      if (daysPastDue < 30) {
        return { shouldRenew: false, reason: 'Bill not yet paid and not significantly overdue' };
      }
    }
  }

  // Auto-detect recurring if enabled
  if (options.autoDetectRecurring && bill.isRecurring === undefined) {
    const detectedType = detectBillType(bill.name, bill.category);
    if (detectedType?.isRecurring) {
      return { shouldRenew: true, reason: 'Auto-detected as recurring bill' };
    }
  }

  // Explicitly marked as recurring
  if (bill.isRecurring === true) {
    return { shouldRenew: true, reason: 'Bill marked as recurring' };
  }

  return { shouldRenew: false, reason: 'Bill not marked as recurring' };
}

/**
 * Processes automatic renewal for a single bill
 */
export function processAutomaticRenewal(
  bill: Bill,
  options: BillRenewalOptions
): RenewalResult {
  const renewalCheck = shouldRenewBill(bill, options);

  if (!renewalCheck.shouldRenew) {
    return {
      success: false,
      skipped: true,
      reason: renewalCheck.reason
    };
  }

  // Determine frequency
  let frequency = bill.frequency;
  if (!frequency && options.autoDetectRecurring) {
    const detectedType = detectBillType(bill.name, bill.category);
    frequency = detectedType?.defaultFrequency || 'monthly';
  }

  if (!frequency) {
    return {
      success: false,
      error: 'No frequency specified and could not auto-detect'
    };
  }

  // Calculate next due date
  const nextDueDate = calculateNextDueDate(bill.dueDate, frequency, options);
  if (!nextDueDate) {
    return {
      success: false,
      error: 'Failed to calculate next due date'
    };
  }

  // Check if we should generate this far in advance
  const nextDate = parseLocalDateString(nextDueDate);
  const today = new Date();
  const monthsInAdvance = options.advanceMonths || 1;
  const maxAdvanceDate = addMonths(today, monthsInAdvance);

  if (nextDate && nextDate > maxAdvanceDate) {
    return {
      success: false,
      skipped: true,
      reason: `Next due date (${nextDueDate}) is beyond advance generation limit`
    };
  }

  // Create renewed bill
  try {
    const renewedBillData = createRenewedBill(bill, nextDueDate, options);

    return {
      success: true,
      renewedBill: renewedBillData as Bill, // Will get ID when saved
      reason: `Successfully renewed for ${nextDueDate}`
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error creating renewed bill'
    };
  }
}

/**
 * Processes automatic renewal for multiple bills
 */
export function processBatchRenewal(
  bills: Bill[],
  options: BillRenewalOptions
): { results: RenewalResult[]; summary: { total: number; renewed: number; skipped: number; errors: number } } {
  const results: RenewalResult[] = [];
  let renewed = 0;
  let skipped = 0;
  let errors = 0;

  for (const bill of bills) {
    const result = processAutomaticRenewal(bill, options);
    results.push(result);

    if (result.success) {
      renewed++;
    } else if (result.skipped) {
      skipped++;
    } else {
      errors++;
    }
  }

  return {
    results,
    summary: {
      total: bills.length,
      renewed,
      skipped,
      errors
    }
  };
}

/**
 * Default renewal options
 */
export const DEFAULT_RENEWAL_OPTIONS: BillRenewalOptions = {
  advanceMonths: 1,
  respectBusinessDays: false,
  autoDetectRecurring: true,
  skipOneTime: true
};

/**
 * Enhanced bill type detection with confidence scoring
 */
export function detectBillTypeWithConfidence(
  billName: string,
  category?: string,
  amount?: number
): { pattern: BillTypePattern | null; confidence: number; suggestions: string[] } {
  const searchText = `${billName} ${category || ''}`.toLowerCase();
  let bestMatch: { pattern: BillTypePattern; confidence: number; type: string } | null = null;
  const suggestions: string[] = [];

  for (const [type, pattern] of Object.entries(BILL_TYPE_PATTERNS)) {
    let confidence = 0;
    let matchedKeywords = 0;
    let hasExactMatch = false;

    for (const keyword of pattern.keywords) {
      if (searchText.includes(keyword)) {
        matchedKeywords++;
        // Longer keywords get higher confidence
        confidence += keyword.length * 15;

        // Check for exact matches - significantly boost confidence
        if (searchText.trim() === keyword || billName.toLowerCase().trim() === keyword) {
          hasExactMatch = true;
          confidence += 200; // Much higher bonus for exact matches
        }
        // Partial word matches get bonus
        else if (billName.toLowerCase().includes(keyword)) {
          confidence += 25;
        }
      }
    }

    // Normalize confidence by number of keywords but give base score
    if (matchedKeywords > 0) {
      // Apply base confidence
      let baseConfidence = (confidence / pattern.keywords.length) + 30;

      // Ensure exact matches always have higher confidence
      if (hasExactMatch) {
        baseConfidence += 50; // Additional bonus to ensure exact matches win
      }

      confidence = baseConfidence;

      // Category match bonus
      if (category && pattern.category && category.toLowerCase().includes(pattern.category.toLowerCase())) {
        confidence += 25;
      }

      // Amount-based hints for certain types
      if (amount) {
        if (type === 'subscriptions' && amount < 50) confidence += 10;
        if (type === 'utilities' && amount > 50 && amount < 500) confidence += 10;
        if (type === 'rent' && amount > 500) confidence += 15;
        if (type === 'mortgage' && amount > 1000) confidence += 15;
      }

      if (!bestMatch || confidence > bestMatch.confidence) {
        bestMatch = { pattern, confidence, type };
      }

      if (confidence > 30) {
        suggestions.push(`Consider marking as ${pattern.category || type} (${pattern.defaultFrequency})`);
      }
    }
  }

  return {
    pattern: bestMatch?.pattern || null,
    confidence: bestMatch?.confidence || 0,
    suggestions
  };
}
