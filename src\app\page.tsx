'use client';

import Link from 'next/link';

const features = [
  {
    title: 'Plan every paycheck',
    description: 'Track bills, subscriptions, and due dates so you always know what is coming next.'
  },
  {
    title: 'Stay on top of payments',
    description: 'Smart reminders and offline access keep you on schedule, even without a connection.'
  },
  {
    title: 'Install as an app',
    description: 'Add PayDay Pilot to your home screen for a fast, native-like experience on any device.'
  }
];

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50 to-blue-100 text-gray-900">
      <header className="mx-auto flex max-w-6xl flex-col items-center gap-10 px-6 pb-16 pt-24 text-center">
        <span className="rounded-full bg-blue-100 px-4 py-1 text-sm font-semibold text-blue-700">
          PayDay Pilot PWA
        </span>
        <h1 className="max-w-3xl text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
          Manage every bill with confidence and install it like a native app.
        </h1>
        <p className="max-w-2xl text-lg text-gray-600">
          PayDay Pilot helps you understand exactly when bills are due, what is already paid, and how much cash you
          have available. Optimised for offline mode, push notifications, and lightning-fast performance.
        </p>
        <div className="flex flex-col items-center gap-4 sm:flex-row">
          <Link
            href="/dashboard"
            className="inline-flex items-center rounded-full bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-lg transition hover:bg-blue-700"
          >
            Open the dashboard
          </Link>
          <Link
            href="/pwa"
            className="inline-flex items-center rounded-full border border-blue-600 px-6 py-3 text-base font-semibold text-blue-600 transition hover:bg-blue-50"
          >
            Install the app
          </Link>
        </div>
      </header>

      <main className="mx-auto flex max-w-5xl flex-col gap-12 px-6 pb-24">
        <section className="grid gap-6 md:grid-cols-3">
          {features.map((feature) => (
            <div key={feature.title} className="rounded-2xl bg-white p-6 shadow-md">
              <h2 className="text-xl font-semibold text-gray-900">{feature.title}</h2>
              <p className="mt-2 text-sm text-gray-600">{feature.description}</p>
            </div>
          ))}
        </section>

        <section className="rounded-3xl bg-white p-8 shadow-lg md:p-12">
          <h2 className="text-2xl font-bold text-gray-900">Built for reliability and speed</h2>
          <ul className="mt-4 space-y-2 text-sm text-gray-600">
            <li>- Works offline with an optimised service worker and caching strategy.</li>
            <li>- Push notifications keep you informed about upcoming bills.</li>
            <li>- Responsive layout tailored for mobile, tablet, and desktop usage.</li>
          </ul>
        </section>
      </main>
    </div>
  );
}
