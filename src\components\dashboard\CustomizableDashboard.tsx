'use client';
import './dashboard.mobile.grid.css';

import { useUserPreferences } from '@/stores/userPreferencesStore';
import { motion } from 'framer-motion';
import dynamic from 'next/dynamic';
import { useState } from 'react';

// Only import BillCalendarWidget
const BillCalendarWidget = dynamic(() => import('./widgets/BillCalendarWidget').then(mod => mod.default));

// Widget Registry maps widget ID to its component
const WIDGET_REGISTRY: Record<string, React.ComponentType<any>> = {
  // 'bill-calendar': BillCalendarWidget, // Removed permanently
};

export function CustomizableDashboard() {
  const { dashboardWidgets, toggleWidget, reorderWidgets, updateWidget } = useUserPreferences();
  const [isEditing, setIsEditing] = useState(false);
  const [draggedWidget, setDraggedWidget] = useState<string | null>(null);

  // Sort widgets by position
  const sortedWidgets = [...dashboardWidgets]
    .filter(widget => widget.enabled)
    .sort((a, b) => a.position - b.position);

  // Handle widget drag start
  const handleDragStart = (widgetId: string) => {
    if (!isEditing) return;
    setDraggedWidget(widgetId);
  };

  // Handle widget drag over
  const handleDragOver = (e: React.DragEvent, widgetId: string) => {
    e.preventDefault();
    if (!isEditing || !draggedWidget || draggedWidget === widgetId) return;

    // Find the positions of both widgets
    const draggedWidgetIndex = sortedWidgets.findIndex(w => w.id === draggedWidget);
    const targetWidgetIndex = sortedWidgets.findIndex(w => w.id === widgetId);

    if (draggedWidgetIndex === -1 || targetWidgetIndex === -1) return;

    // Create a new ordered array
    const newOrder = [...sortedWidgets];
    const [removed] = newOrder.splice(draggedWidgetIndex, 1);
    newOrder.splice(targetWidgetIndex, 0, removed);

    // Update positions
    reorderWidgets(newOrder);
  };

  // Handle widget drag end
  const handleDragEnd = () => {
    setDraggedWidget(null);
  };

  // Resize a widget
  const handleResize = (widgetId: string) => {
    if (!isEditing) return;

    const widget = dashboardWidgets.find(w => w.id === widgetId);
    if (!widget) return;

    const sizes: ('small' | 'medium' | 'large')[] = ['small', 'medium', 'large'];
    const currentSizeIndex = sizes.indexOf(widget.size);
    const nextSize = sizes[(currentSizeIndex + 1) % sizes.length];

    updateWidget(widgetId, { size: nextSize });
  };

  // Toggle widget visibility
  const handleToggleWidget = (widgetId: string) => {
    if (!isEditing) return;
    const widget = dashboardWidgets.find(w => w.id === widgetId);
    if (widget) {
      toggleWidget(widgetId, !widget.enabled);
    }
  };

  // Get size class for a widget
  const getWidgetSizeClass = (size: 'small' | 'medium' | 'large') => {
    switch (size) {
      case 'small':
        return 'col-span-1';
      case 'medium':
        return 'col-span-1 md:col-span-2';
      case 'large':
        return 'col-span-1 md:col-span-3';
      default:
        return 'col-span-1';
    }
  };

  return (
    <div className="space-y-6">
      <motion.div
        className="grid grid-cols-1 gap-4 p-2 sm:grid-cols-2 sm:gap-4 sm:p-2 md:grid-cols-3 md:gap-5 md:p-3 lg:grid-cols-4 lg:gap-6 lg:p-4 relative dashboard-mobile-grid"
        initial="hidden"
        animate="visible"
        variants={{
          hidden: {},
          visible: { transition: { staggerChildren: 0.08 } }
        }}
      >
        {sortedWidgets.map((widget) => {
          const WidgetComponent = WIDGET_REGISTRY[widget.id];
          if (!WidgetComponent) return null;

          return (
            <motion.div
              key={widget.id}
              layoutId={widget.id}
              initial={{ opacity: 0, y: 24 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ scale: 1.025, boxShadow: '0 6px 32px 0 rgba(56,128,255,0.12)' }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className={[
                getWidgetSizeClass(widget.size),
                'relative transition-all duration-300',
                isEditing ? 'cursor-grab ring-2 ring-blueprint-blue/50 ring-offset-2 ring-offset-cream dark:ring-offset-dark-sepia' : '',
                draggedWidget === widget.id ? 'opacity-50 scale-95 z-0' : 'z-10',
              ].join(' ')}
              draggable={isEditing}
              onDragStart={() => handleDragStart(widget.id)}
              onDragOver={(e) => handleDragOver(e, widget.id)}
              onDragEnd={handleDragEnd}
            >
              <div className="h-full relative z-10">
                {isEditing && (
                  <motion.div
                    className="absolute top-2 right-2 z-20 flex space-x-1"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ type: 'spring', stiffness: 260, damping: 18 }}
                  >
                    <button
                      onClick={() => handleResize(widget.id)}
                      className="p-1.5 bg-white/90 dark:bg-gray-800/90 rounded-lg shadow-sm hover:bg-blueprint-blue/10 dark:hover:bg-blueprint-blue/20 focus:outline-none focus-visible:ring-2 focus-visible:ring-blueprint-blue"
                      title="Resize widget"
                      aria-label="Resize widget"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                      </svg>
                    </button>
                    <button
                      onClick={() => handleToggleWidget(widget.id)}
                      className="p-1.5 bg-white/90 dark:bg-gray-800/90 rounded-lg shadow-sm hover:bg-red-100 dark:hover:bg-red-900/20 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500"
                      title="Remove widget"
                      aria-label="Remove widget"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </motion.div>
                )}
                <WidgetComponent />
              </div>
            </motion.div>
          );
        })}
      </motion.div>

      {isEditing && (
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-3">Add Widgets</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {dashboardWidgets
              .filter(widget => !widget.enabled)
              .map((widget) => (
                <button
                  key={widget.id}
                  className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 text-left"
                  onClick={() => toggleWidget(widget.id, true)}
                >
                  <div className="font-medium">{widget.name}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Click to add</div>
                </button>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}