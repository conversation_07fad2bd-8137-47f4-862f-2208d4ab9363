/**
 * Version management utilities for PayDay Pilot
 */

export interface VersionInfo {
  version: string;
  releaseDate: string;
  title: string;
  sections: {
    [key: string]: string[];
  };
}

/**
 * Get the current application version
 */
export function getCurrentVersion(): string {
  return process.env.NEXT_PUBLIC_APP_VERSION || '0.6.2';
}

/**
 * Compare two semantic versions
 * Returns: 1 if v1 > v2, -1 if v1 < v2, 0 if equal
 */
export function compareVersions(v1: string, v2: string): number {
  const parts1 = v1.split('.').map(Number);
  const parts2 = v2.split('.').map(Number);

  for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
    const part1 = parts1[i] || 0;
    const part2 = parts2[i] || 0;

    if (part1 > part2) return 1;
    if (part1 < part2) return -1;
  }

  return 0;
}

/**
 * Check if version1 is newer than version2
 */
export function isNewerVersion(v1: string, v2: string): boolean {
  return compareVersions(v1, v2) > 0;
}

/**
 * Parse changelog content and extract version information
 */
export function parseChangelog(content: string): VersionInfo[] {
  const versions: VersionInfo[] = [];
  const lines = content.split('\n');

  let currentVersion: VersionInfo | null = null;
  let currentSection = '';

  for (const line of lines) {
    // Match version headers like "## [0.6.1]" or "## 0.6.1"
    const versionMatch = line.match(/^##\s*\[?(\d+\.\d+\.\d+)\]?/);
    if (versionMatch) {
      if (currentVersion) {
        versions.push(currentVersion);
      }

      currentVersion = {
        version: versionMatch[1],
        releaseDate: '',
        title: '',
        sections: {}
      };
      continue;
    }

    // Match release date like "**Released: December 19, 2024**"
    const dateMatch = line.match(/\*\*Released:\s*([^*]+)\*\*/);
    if (dateMatch && currentVersion) {
      currentVersion.releaseDate = dateMatch[1].trim();
      continue;
    }

    // Match section headers like "### Improved"
    const sectionMatch = line.match(/^###\s+(.+)/);
    if (sectionMatch && currentVersion) {
      currentSection = sectionMatch[1];
      if (!currentVersion.sections[currentSection]) {
        currentVersion.sections[currentSection] = [];
      }
      continue;
    }

    // Match list items like "- **Dashboard Text Alignment**: ..."
    const itemMatch = line.match(/^-\s+(.+)/);
    if (itemMatch && currentVersion && currentSection) {
      currentVersion.sections[currentSection].push(itemMatch[1]);
    }
  }

  // Add the last version
  if (currentVersion) {
    versions.push(currentVersion);
  }

  return versions;
}

/**
 * Get the latest version from changelog
 */
export function getLatestVersionFromChangelog(content: string): string {
  const versions = parseChangelog(content);
  return versions.length > 0 ? versions[0].version : getCurrentVersion();
}

/**
 * Storage keys for version tracking
 */
export const VERSION_STORAGE_KEYS = {
  LAST_VIEWED_VERSION: 'payday_pilot_last_viewed_version',
  CHANGELOG_NOTIFICATION_DISMISSED: 'payday_pilot_changelog_dismissed',
  LAST_NOTIFICATION_CHECK: 'payday_pilot_last_notification_check'
} as const;

/**
 * Get the last viewed version from localStorage
 */
export function getLastViewedVersion(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(VERSION_STORAGE_KEYS.LAST_VIEWED_VERSION);
}

/**
 * Set the last viewed version in localStorage
 */
export function setLastViewedVersion(version: string): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem(VERSION_STORAGE_KEYS.LAST_VIEWED_VERSION, version);
}

/**
 * Check if there are unviewed changelog updates
 */
export function hasUnviewedUpdates(currentVersion: string, lastViewedVersion: string | null): boolean {
  if (!lastViewedVersion) return true;
  return isNewerVersion(currentVersion, lastViewedVersion);
}

/**
 * Format version for display
 */
export function formatVersionForDisplay(version: string): string {
  return `v${version}`;
}

/**
 * Format release date for display
 */
export function formatReleaseDateForDisplay(dateString: string): string {
  try {
    // Remove emoji and extra characters
    const cleanDate = dateString.replace(/[^\w\s,]/g, '').trim();
    const date = new Date(cleanDate);

    if (isNaN(date.getTime())) {
      return dateString; // Return original if parsing fails
    }

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch {
    return dateString;
  }
}
