// Mock Firebase implementation for tests
import type { Analytics } from 'firebase/analytics';
import type { FirebaseApp } from 'firebase/app';
import type { Auth, GoogleAuthProvider } from 'firebase/auth';

// Define types for Firebase services that don't export their types directly
type Firestore = any;
type Messaging = any;

// Mock firebase app
export const app = {} as FirebaseApp;

// Mock auth with necessary methods
export const auth = {
    currentUser: null,
    onAuthStateChanged: jest.fn((callback) => {
        callback(null);
        return jest.fn(); // unsubscribe function
    }),
    signInWithEmailAndPassword: jest.fn(),
    signOut: jest.fn(),
    createUserWithEmailAndPassword: jest.fn(),
} as unknown as Auth;

// Mock firestore
export const db = {} as Firestore;

// Mock Google provider
export const googleProvider = {
    addScope: jest.fn(),
} as unknown as GoogleAuthProvider;

// Mock async functions
export const getAuth = jest.fn().mockResolvedValue(auth);
export const getDb = jest.fn().mockResolvedValue(db);
export const getGoogleProvider = jest.fn().mockResolvedValue(googleProvider);
export const getAnalytics = jest.fn().mockResolvedValue({} as Analytics);
export const getMessaging = jest.fn().mockResolvedValue({} as Messaging);

// Mock core services
export const coreServices = Promise.resolve({ app, auth, db, googleProvider });
