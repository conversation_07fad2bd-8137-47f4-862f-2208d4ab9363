'use client';

import { useCallback, useEffect, useState } from 'react';

// Import version utilities

// Constants for localStorage keys (legacy support)
const LAST_VIEWED_CHANGELOG_KEY = 'last-viewed-changelog';
const CHANGELOG_VERSION_KEY = 'current-changelog-version';

export interface ChangelogNotificationState {
  hasNewUpdate: boolean;
  currentVersion: string;
  lastViewedVersion: string | null;
  isLoading: boolean;
  error: string | null;
}

/**
 * Custom hook to check if there are new unread changelog updates
 * Returns an object with notification state and control functions
 */
export function useChangelogNotification(): ChangelogNotificationState & {
  markChangelogAsRead: () => void;
  refreshNotificationState: () => Promise<void>;
} {
  const [state, setState] = useState<ChangelogNotificationState>({
    hasNewUpdate: false,
    currentVersion: process.env.NEXT_PUBLIC_APP_VERSION || '0.6.2',
    lastViewedVersion: null,
    isLoading: true,
    error: null
  });

  // Function to extract the version from the changelog content
  const extractVersionFromChangelog = useCallback((content: string): string => {
    // Look for version in format like "## [1.2.3]" at the start of a line
    const versionMatch = content.match(/^## \[([\d\.]+)\]/m);
    return versionMatch ? versionMatch[1] : '';
  }, []);

  // Function to update notification status
  const updateNotificationStatus = useCallback(async () => {
    try {
      // Skip this check during server-side rendering
      if (typeof window === 'undefined') return;

      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Fetch the changelog content
      const response = await fetch('/CHANGELOG.md');
      if (!response.ok) {
        throw new Error('Failed to fetch changelog');
      }

      const content = await response.text();
      const currentVersion = extractVersionFromChangelog(content);

      // If we couldn't extract a version or changelog is empty, use app version
      const versionToUse = currentVersion || process.env.NEXT_PUBLIC_APP_VERSION || '0.6.2';

      // Store current version in localStorage
      localStorage.setItem(CHANGELOG_VERSION_KEY, versionToUse);

      // Get the last viewed version from localStorage (check both new and legacy keys)
      const lastViewedVersion = localStorage.getItem('payday_pilot_last_viewed_version') ||
        localStorage.getItem(LAST_VIEWED_CHANGELOG_KEY);

      // Check if there are unviewed updates
      const hasUpdates = !lastViewedVersion || lastViewedVersion !== versionToUse;

      setState(prev => ({
        ...prev,
        hasNewUpdate: hasUpdates,
        currentVersion: versionToUse,
        lastViewedVersion,
        isLoading: false,
        error: null
      }));

    } catch (error) {
      console.error('Error checking changelog updates:', error);
      setState(prev => ({
        ...prev,
        hasNewUpdate: false,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }, [extractVersionFromChangelog]);

  // Mark changelog as read
  const markChangelogAsRead = useCallback(() => {
    try {
      const currentVersion = state.currentVersion;
      if (currentVersion) {
        // Store in both new and legacy keys for compatibility
        localStorage.setItem('payday_pilot_last_viewed_version', currentVersion);
        localStorage.setItem(LAST_VIEWED_CHANGELOG_KEY, currentVersion);

        setState(prev => ({
          ...prev,
          hasNewUpdate: false,
          lastViewedVersion: currentVersion
        }));
      }
    } catch (error) {
      console.error('Error marking changelog as read:', error);
    }
  }, [state.currentVersion]);

  // Refresh notification state manually
  const refreshNotificationState = useCallback(async () => {
    await updateNotificationStatus();
  }, [updateNotificationStatus]);

  useEffect(() => {
    // Check for updates when the component mounts
    updateNotificationStatus();

    // Re-check when storage changes (e.g., from another tab)
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === LAST_VIEWED_CHANGELOG_KEY || event.key === CHANGELOG_VERSION_KEY) {
        updateNotificationStatus();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Cleanup listener on unmount
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [updateNotificationStatus]);

  return {
    ...state,
    markChangelogAsRead,
    refreshNotificationState
  };
}
