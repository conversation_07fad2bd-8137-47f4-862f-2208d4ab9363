// webpack.optimization.js
// Webpack optimization utilities for faster compilation

const path = require('path');
const fs = require('fs');

// Optimize module resolution for faster builds
function optimizeModuleResolution(config) {
  config.resolve = {
    ...config.resolve,
    // Faster module resolution
    modules: [path.resolve(__dirname, 'node_modules'), 'node_modules'],
    symlinks: false,
    // Cache module resolution
    cache: true,
    // Optimize extensions (only include what we use)
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    // Alias for faster resolution
    alias: {
      ...config.resolve?.alias || {},
      '@': path.resolve(__dirname, 'src'),
      // Alias heavy imports in development to empty modules
      ...(process.env.NODE_ENV === 'development' ? {
        'firebase/analytics': false,
        'firebase/messaging': false,
        'firebase/functions': false,
        'googleapis': path.resolve(__dirname, 'src/__mocks__/googleapis.js'),
      } : {}),
    },
    // Fallbacks for problematic modules
    fallback: {
      ...config.resolve?.fallback || {},
      fs: false,
      path: false,
      crypto: false,
    },
  };
  return config;
}

// Optimize development builds
function optimizeForDevelopment(config) {
  // Use fastest source maps for development
  config.devtool = 'eval';

  // Disable optimizations that slow down development
  config.optimization = {
    ...config.optimization,
    minimize: false,
    removeAvailableModules: false,
    removeEmptyChunks: false,
    splitChunks: false,
    usedExports: false,
    sideEffects: false,
    concatenateModules: false,
    flagIncludedChunks: false,
    occurrenceOrder: false,
    providedExports: false,
    mangleExports: false,
    // Cache modules
    moduleIds: 'named',
    // Skip runtime checks that slow down builds
    checkWasmTypes: false,
    // Limit parallelism to avoid memory issues
    parallelism: 4,
  };

  // Faster file watching
  config.watchOptions = {
    ...config.watchOptions,
    poll: false, // Polling adds CPU overhead
    aggregateTimeout: 200,
    ignored: [
      '**/node_modules/**',
      '**/.git/**',
      '**/.next/**',
      '**/out/**',
      '**/dist/**',
      '**/.turbo/**',
      '**/coverage/**',
      '**/*.test.*',
      '**/*.spec.*',
      '**/public/**',
      '**/functions/**',
      '**/netlify/**',
    ],
  };

  // Disable non-essential loaders in development
  config.module.rules.forEach(rule => {
    // Skip expensive loaders for font files in development
    if (rule.test && rule.test.toString().includes('woff')) {
      rule.use = [{ loader: 'file-loader', options: { name: '[name].[ext]' } }];
    }

    // Skip expensive image optimizations in development
    if (rule.test && rule.test.toString().match(/\.(png|jpg|jpeg|gif|webp|avif|ico|bmp)$/i)) {
      rule.use = [{ loader: 'file-loader', options: { name: '[name].[ext]' } }];
    }
  });

  return config;
}

// Optimize production builds
function optimizeForProduction(config) {
  // Better code splitting
  config.optimization.splitChunks = {
    chunks: 'all',
    maxInitialRequests: 25,
    minSize: 20000,
    maxSize: 244000,
    cacheGroups: {
      // Firebase chunk
      firebase: {
        test: /[\\/]node_modules[\\/](firebase|@firebase)[\\/]/,
        name: 'firebase',
        priority: 30,
        chunks: 'all',
        enforce: true,
      },
      // Google APIs chunk
      googleapis: {
        test: /[\\/]node_modules[\\/]googleapis[\\/]/,
        name: 'googleapis',
        priority: 25,
        chunks: 'all',
        enforce: true,
      },
      // UI libraries chunk
      ui: {
        test: /[\\/]node_modules[\\/](@headlessui|@heroicons|framer-motion)[\\/]/,
        name: 'ui-libs',
        priority: 20,
        chunks: 'all',
      },
      // Chart libraries chunk
      charts: {
        test: /[\\/]node_modules[\\/](recharts|react-big-calendar)[\\/]/,
        name: 'chart-libs',
        priority: 15,
        chunks: 'all',
      },
      // React chunk
      react: {
        test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
        name: 'react',
        priority: 40,
        chunks: 'all',
        enforce: true,
      },
      // Default vendor chunk
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        priority: 10,
        chunks: 'all',
      },
    },
  };

  return config;
}

// Add caching for faster rebuilds
function addCaching(config) {
  config.cache = {
    type: 'filesystem',
    cacheDirectory: path.resolve(__dirname, '.next/cache/webpack'),
    buildDependencies: {
      config: [__filename],
    },
  };
  return config;
}

// Externalize heavy dependencies for server builds
function externalizeServerDependencies(config, isServer) {
  if (isServer) {
    config.externals = [
      ...config.externals,
      'firebase-admin',
      'googleapis',
      'sharp',
    ];
  }
  return config;
}

module.exports = {
  optimizeModuleResolution,
  optimizeForDevelopment,
  optimizeForProduction,
  addCaching,
  externalizeServerDependencies,
};
