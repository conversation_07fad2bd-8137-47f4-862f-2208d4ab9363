// Mock implementation of googleapis for development
// This reduces the overhead of loading the large googleapis package during development

const mockAuth = {
    getClient: () => Promise.resolve({}),
    getToken: () => Promise.resolve({ tokens: {} }),
};

const mockGmail = {
    users: {
        messages: {
            list: () => Promise.resolve({ data: { messages: [] } }),
            get: () => Promise.resolve({ data: { payload: { headers: [] } } }),
        },
        labels: {
            list: () => Promise.resolve({ data: { labels: [] } }),
        },
    },
};

const mockCalendar = {
    events: {
        list: () => Promise.resolve({ data: { items: [] } }),
        insert: () => Promise.resolve({ data: {} }),
    },
};

// Mock google.auth.OAuth2 class
class OAuth2 {
    constructor() { }
    setCredentials() { }
    getAccessToken() {
        return Promise.resolve({ token: 'mock-token' });
    }
}

// Mock the googleapis module
module.exports = {
    gmail: () => mockGmail,
    calendar: () => mockCalendar,
    auth: {
        OAuth2,
    },
    google: {
        auth: {
            OAuth2,
            getClient: mockAuth.getClient,
            getToken: mockAuth.getToken,
        },
        gmail: () => mockGmail,
        calendar: () => mockCalendar,
    },
};
