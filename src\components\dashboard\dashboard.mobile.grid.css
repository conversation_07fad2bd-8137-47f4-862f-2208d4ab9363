/* Mobile grid uniformity for dashboard cards */
@media (max-width: 640px) {
    .dashboard-mobile-grid {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 1.25rem !important;
        width: 100% !important;
        box-sizing: border-box !important;
        padding: 0.5rem 0.25rem !important;
    }

    .dashboard-card-mobile {
        width: 100% !important;
        min-width: 0 !important;
        box-sizing: border-box !important;
        margin-bottom: 0 !important;
        border-radius: 1.25rem !important;
        box-shadow: 0 2px 16px 0 rgba(56, 128, 255, 0.10);
        font-size: 1.08rem !important;
        padding: 1.25rem 0.75rem !important;
        text-align: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .dashboard-card-mobile h2 {
        font-size: 1.15rem !important;
        margin-bottom: 0.75rem !important;
        width: 100%;
        text-align: center;
    }
}