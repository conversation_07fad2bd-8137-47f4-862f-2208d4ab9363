// Enhanced service worker for PayDay Pilot PWA
// Unify push handling: import OneSignal SDK and Firebase Messaging here
try {
  importScripts('https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.sw.js');
} catch (e) {
  // OneSignal import optional; log for debugging only
  console.warn('[SW] OneSignal SDK import failed (optional):', e);
}

try {
  importScripts('https://www.gstatic.com/firebasejs/11.6.1/firebase-app-compat.js');
  importScripts('https://www.gstatic.com/firebasejs/11.6.1/firebase-messaging-compat.js');
  // Initialize Firebase Messaging using the same config as firebase-messaging-sw.js
  // NOTE: Keep in sync with your project settings
  if (typeof firebase !== 'undefined' && !firebase.apps?.length) {
    firebase.initializeApp({
      apiKey: "AIzaSyA2uCt4w6XMIjDAdCoUmxfrY9Q441XUfRo",
      authDomain: "payday-pilot.firebaseapp.com",
      projectId: "payday-pilot",
      storageBucket: "payday-pilot.firebasestorage.app",
      messagingSenderId: "122539156310",
      appId: "1:122539156310:web:33f3153bfceb0437792cf7",
      measurementId: "G-GN0V3SX78P"
    });
    const messaging = firebase.messaging();
    messaging.onBackgroundMessage(function (payload) {
      const notificationTitle = payload?.data?.title || payload?.notification?.title || 'PayDay Pilot';
      const notificationBody = payload?.data?.body || payload?.notification?.body || 'You have a new message.';
      const iconSource = typeof payload?.data?.icon === 'string' ? payload.data.icon : ICON_192;
      const badgeSource = typeof payload?.data?.badge === 'string' ? payload.data.badge : ICON_72;
      const notificationOptions = {
        body: notificationBody,
        icon: versionedPath(iconSource),
        badge: versionedPath(badgeSource),
        data: { ...(payload?.data || {}), url: payload?.data?.url || '/' },
        vibrate: [200, 100, 200],
        tag: 'payday-pilot-notification',
        renotify: true,
      };
      return self.registration.showNotification(notificationTitle, notificationOptions);
    });
  }
} catch (e) {
  // Firebase Messaging import optional; log for debugging only
  console.warn('[SW] Firebase Messaging import failed (optional):', e);
}
const DEFAULT_VERSION = '0.6.5';
const swVersion = (() => {
  try {
    const url = new URL(self.location.href);
    return url.searchParams.get('v') || DEFAULT_VERSION;
  } catch (error) {
    console.warn('[Service Worker] Unable to resolve version from scope, using default', error);
    return DEFAULT_VERSION;
  }
})();

const versionedPath = (path) => {
  if (typeof path !== 'string') return path;
  if (!path.startsWith('/')) return path;
  const url = new URL(path, self.location.origin);
  if (!url.searchParams.has('v')) {
    url.searchParams.set('v', swVersion);
  }
  return url.pathname + url.search;
};

const APP_SHELL_VERSION = swVersion;
const CACHE_NAME = `payday-pilot-cache-v${APP_SHELL_VERSION}`;
const OFFLINE_URL = versionedPath('/offline.html');
const MANIFEST_URL = versionedPath('/manifest.json');
const FAVICON_URL = versionedPath('/favicon.ico');
const ICON_192 = versionedPath('/icons/icon-192x192.png');
const ICON_512 = versionedPath('/icons/icon-512x512.svg');
const ICON_72 = versionedPath('/icons/icon-72x72.svg');
const ICON_PLACEHOLDER = versionedPath('/icons/icon-placeholder.svg');
const LOGO_IMAGE = versionedPath('/images/logo.webp');

// Assets to cache immediately on install
const PRECACHE_ASSETS = [
  '/',
  '/dashboard',
  '/bills',
  '/settings',
  MANIFEST_URL,
  FAVICON_URL,
  OFFLINE_URL, // Offline fallback page
  ICON_192,
  ICON_512,
  ICON_PLACEHOLDER,
  LOGO_IMAGE
];

// Assets to cache on first use (eager strategy)
const DYNAMIC_CACHE_ROUTES = [
  '/calendar',
  '/calculator',
  '/profile'
];

// URLs that should bypass the cache
const API_ROUTES = [
  '/api/bills',
  '/api/subscribe',
  '/api/financial-summary'
];

// Install event - precache key resources
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker v' + APP_SHELL_VERSION);

  // Skip waiting to ensure the new service worker activates immediately
  self.skipWaiting();

  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log('[Service Worker] Precaching app shell');
      return Promise.allSettled(
        PRECACHE_ASSETS.map(url =>
          fetch(new Request(url, { cache: 'reload' }))
            .then(response => {
              if (!response.ok) {
                console.warn(`[Service Worker] Failed to cache: ${url} (${response.status})`);
                return;
              }
              return cache.put(url, response);
            })
            .catch(err => {
              console.warn(`[Service Worker] Failed to fetch: ${url}`, err);
            })
        )
      );
    })
  );
});

// Activate event - clean up old caches and take control
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker v' + APP_SHELL_VERSION);

  // Claim clients to ensure the SW is in control immediately
  event.waitUntil(self.clients.claim());

  // Clean up old caches
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.filter(cacheName => {
          return cacheName.startsWith('payday-pilot-') && cacheName !== CACHE_NAME;
        }).map(cacheName => {
          console.log('[Service Worker] Removing old cache:', cacheName);
          return caches.delete(cacheName);
        })
      );
    })
  );
});

// Helper function to determine if a request should be cached
function shouldCache(url) {
  const reqUrl = new URL(url);

  // Don't cache API requests
  if (API_ROUTES.some(route => reqUrl.pathname.startsWith(route))) {
    return false;
  }

  // Don't cache URLs with cache-busting query parameters
  if (reqUrl.searchParams.has('no-cache')) {
    return false;
  }

  // Cache static assets and HTML routes
  return true;
}

// Network-first strategy with fallback to cache
async function networkFirstStrategy(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);

    // Cache the response if valid
    if (networkResponse.ok && shouldCache(request.url)) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // Fallback to cache
    const cachedResponse = await caches.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    // For navigation requests, return the offline page
    if (request.mode === 'navigate') {
      return caches.match(OFFLINE_URL);
    }

    // For image requests, return placeholder
    if (request.destination === 'image') {
      return caches.match(ICON_PLACEHOLDER);
    }

    // Otherwise return an empty response
    return new Response('Network error occurred', {
      status: 408,
      headers: { 'Content-Type': 'text/plain' }
    });
  }
}

// Cache-first strategy with background update
async function cacheFirstStrategy(request) {
  const cache = await caches.open(CACHE_NAME);
  const cachedResponse = await cache.match(request);

  if (cachedResponse) {
    // Return cached response immediately
    // Update cache in background if online
    if (navigator.onLine) {
      fetch(request)
        .then(networkResponse => {
          if (networkResponse.ok) {
            cache.put(request, networkResponse);
          }
        })
        .catch(error => console.log('[Service Worker] Background fetch failed:', error));
    }
    return cachedResponse;
  }

  // If not in cache, fetch from network and cache
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok && shouldCache(request.url)) {
      await cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // For images, return a placeholder
    if (request.destination === 'image') {
      return cache.match(ICON_PLACEHOLDER);
    }

    // For other resources, return error response
    return new Response('Network error occurred', {
      status: 408,
      headers: { 'Content-Type': 'text/plain' }
    });
  }
}

// Stale-while-revalidate strategy
async function staleWhileRevalidateStrategy(request) {
  const cache = await caches.open(CACHE_NAME);
  const cachedResponse = await cache.match(request);

  const fetchPromise = fetch(request)
    .then(networkResponse => {
      if (networkResponse.ok && shouldCache(request.url)) {
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    })
    .catch(error => {
      console.log('[Service Worker] Fetch failed:', error);
      return null;
    });

  return cachedResponse || fetchPromise || (await fetchPromise);
}

// Fetch event - handle different caching strategies
self.addEventListener('fetch', (event) => {
  const request = event.request;

  // Skip cross-origin requests to reduce complexity
  if (!request.url.startsWith(self.location.origin)) return;

  // Skip if method isn't GET
  if (request.method !== 'GET') return;

  // For API requests, use network-only
  if (API_ROUTES.some(route => request.url.includes(route))) {
    return;
  }

  // For HTML navigation requests - use network-first strategy
  if (request.mode === 'navigate') {
    event.respondWith(networkFirstStrategy(request));
    return;
  }

  // For static assets (css, js, fonts), use stale-while-revalidate
  if (request.destination === 'style' ||
    request.destination === 'script' ||
    request.destination === 'font') {
    event.respondWith(staleWhileRevalidateStrategy(request));
    return;
  }

  // For images, use cache-first
  if (request.destination === 'image') {
    event.respondWith(cacheFirstStrategy(request));
    return;
  }

  // For all other requests
  event.respondWith(cacheFirstStrategy(request));
});

// Handle messages from clients
self.addEventListener('message', (event) => {
  // Check if the event has data and a response port
  if (!event.data) return;

  const responsePort = event.ports && event.ports[0];

  switch (event.data.type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      if (responsePort) {
        responsePort.postMessage({ success: true });
      }
      break;

    case 'CACHE_URLS':
      // Handle request to cache additional URLs
      if (Array.isArray(event.data.urls) && event.data.urls.length > 0) {
        event.waitUntil(
          caches.open(CACHE_NAME).then(cache => {
            return Promise.all(
              event.data.urls.map(url => {
                return fetch(url)
                  .then(response => {
                    if (response.ok) {
                      return cache.put(url, response);
                    }
                    console.warn(`[SW] Failed to cache URL: ${url}`);
                    return null;
                  })
                  .catch(error => {
                    console.error(`[SW] Error caching ${url}:`, error);
                    return null;
                  });
              })
            );
          }).then(() => {
            if (responsePort) {
              responsePort.postMessage({ success: true, cached: event.data.urls.length });
            }
          })
        );
      }
      break;

    case 'CLEAR_CACHE':
      // Handle request to clear the cache
      event.waitUntil(
        caches.delete(CACHE_NAME).then(() => {
          if (responsePort) {
            responsePort.postMessage({ success: true });
          }
        })
      );
      break;

    case 'GET_VERSION':
      // Return the current service worker version
      if (responsePort) {
        responsePort.postMessage({ version: APP_SHELL_VERSION });
      }
      break;

    default:
      // For any other message types, send a default response if port is available
      if (responsePort) {
        responsePort.postMessage({ received: true });
      }
  }
});

// Background sync handler for offline operations
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-bills') {
    event.waitUntil(syncBillsData());
  }
});

// Function to sync bills data when back online
async function syncBillsData() {
  try {
    const dbOpenRequest = indexedDB.open('payday-pilot-offline-db', 1);

    dbOpenRequest.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('offlineBills')) {
        db.createObjectStore('offlineBills', { keyPath: 'id' });
      }
    };

    return new Promise((resolve, reject) => {
      dbOpenRequest.onsuccess = async (event) => {
        try {
          const db = event.target.result;
          const transaction = db.transaction('offlineBills', 'readwrite');
          const store = transaction.objectStore('offlineBills');

          const pendingBills = await new Promise(resolve => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => resolve([]);
          });

          if (pendingBills && pendingBills.length) {
            console.log(`[Service Worker] Syncing ${pendingBills.length} offline bills`);

            // Process each bill with the server
            for (const bill of pendingBills) {
              try {
                const response = await fetch('/api/bills', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify(bill)
                });

                if (response.ok) {
                  const clearRequest = store.delete(bill.id);
                  await new Promise(resolve => {
                    clearRequest.onsuccess = resolve;
                    clearRequest.onerror = resolve; // Continue even if delete fails
                  });
                }
              } catch (error) {
                console.error('[Service Worker] Failed to sync bill:', error);
              }
            }

            // Attempt to notify the app that sync is complete
            const clients = await self.clients.matchAll();
            clients.forEach(client => {
              client.postMessage({
                type: 'SYNC_COMPLETE',
                success: true
              });
            });
          }

          resolve();
        } catch (error) {
          console.error('[Service Worker] Error during sync:', error);
          reject(error);
        }
      };

      dbOpenRequest.onerror = reject;
    });
  } catch (error) {
    console.error('[Service Worker] Sync operation failed:', error);
  }
}

// Periodic background sync (if supported)
self.addEventListener('periodicsync', (event) => {
  if (event.tag === 'update-cache') {
    event.waitUntil(
      caches.open(CACHE_NAME).then((cache) => {
        return Promise.allSettled(
          [...PRECACHE_ASSETS, ...DYNAMIC_CACHE_ROUTES].map(url =>
            fetch(url, { cache: 'reload' })
              .then(response => {
                if (!response.ok) {
                  console.warn(`[Service Worker] Failed to cache: ${url} (${response.status})`);
                  return;
                }
                return cache.put(url, response);
              })
              .catch(err => {
                console.warn(`[Service Worker] Failed to fetch: ${url}`, err);
              })
          )
        );
      })
    );
  }
});

// Push Notification Handler
self.addEventListener('push', (event) => {
  console.log('[Service Worker] Push Received.');

  let notificationData = {
    title: 'PayDay Pilot',
    body: 'You have an upcoming bill payment.',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.svg',
    tag: 'bill-reminder',
    vibrate: [100, 50, 100, 50, 100],
    data: {
      url: '/bills'
    }
  };

  if (event.data) {
    try {
      const dataText = event.data.text();
      const parsedData = JSON.parse(dataText);
      // Merge parsed data with defaults, prioritizing parsed data
      notificationData = { ...notificationData, ...parsedData };
      console.log('[Service Worker] Push data:', notificationData);
    } catch (e) {
      console.error('[Service Worker] Push event couldn\'t parse data:', e);
      // Use default body if parsing fails but data exists
      notificationData.body = event.data.text();
    }
  }

  // Define notification actions
  const options = {
    body: notificationData.body,
    icon: notificationData.icon,
    badge: notificationData.badge,
    vibrate: notificationData.vibrate || [200, 100, 200],
    tag: notificationData.tag,
    renotify: notificationData.renotify !== false,
    data: notificationData.data || {},
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'View Details'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification(notificationData.title, options)
  );
});

// Notification Click Handler
self.addEventListener('notificationclick', (event) => {
  console.log('[Service Worker] Notification click received:', event.notification.tag);

  // Close the notification
  event.notification.close();

  // Get the action (if any)
  const action = event.action;
  const notification = event.notification;
  const notificationData = notification.data || {};
  const urlToOpen = notificationData.url || '/';

  // Handle specific actions
  if (action === 'dismiss') {
    console.log('[Service Worker] Notification dismissed');
    return;
  }

  // For "view" action or default click behavior
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        // Custom handling for specific notification types
        if (notification.tag && notification.tag.startsWith('bill-reminder-')) {
          const billId = notification.tag.replace('bill-reminder-', '');
          if (billId) {
            // For specific bill reminders, open that bill's detail page
            const billDetailUrl = `/bills/${billId}`;

            // Try to focus an existing window with this bill
            for (const client of clientList) {
              const url = new URL(client.url);
              if (url.pathname === billDetailUrl) {
                return client.focus();
              }
            }

            // If no window is open with this bill, open a new one
            if (clients.openWindow) {
              return clients.openWindow(billDetailUrl);
            }
          }
        }

        // General case handling
        for (let i = 0; i < clientList.length; i++) {
          const client = clientList[i];
          // If a window is already open, focus it
          if (client.url === urlToOpen || client.url.endsWith(urlToOpen)) {
            return client.focus();
          }
        }

        // If no window is open, open a new one
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen);
        }
      })
  );
});

// Handle notification close event
self.addEventListener('notificationclose', (event) => {
  console.log('[Service Worker] Notification was closed', event.notification.tag);

  // You could track notification dismissals here if needed
  // Using IndexedDB or sending analytics
});

// Push subscription change handler
self.addEventListener('pushsubscriptionchange', (event) => {
  console.log('[Service Worker] Push subscription changed');

  const vapidPublicKey = self.VAPID_PUBLIC_KEY ||
    'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U';

  event.waitUntil(
    self.registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
    })
      .then(newSubscription => {
        // Send the new subscription to your server
        return fetch('/api/subscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ subscription: newSubscription })
        });
      })
  );
});

// Helper function to convert base64 VAPID key to Uint8Array
function urlBase64ToUint8Array(base64String) {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}
