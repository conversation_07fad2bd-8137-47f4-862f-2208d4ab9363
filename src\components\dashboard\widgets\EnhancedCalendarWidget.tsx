'use client';

import { useBillsStore } from '@/stores/billsStore';
import { useUserPreferences } from '@/stores/userPreferencesStore';
import { parseLocalDateString } from '@/utils/date';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface BillEvent {
  id: string;
  name: string;
  amount: number;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue';
  category: string;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  bills: BillEvent[];
}

export default function EnhancedCalendarWidget() {
  const [loading, setLoading] = useState(true);
  const { trackAction } = useUserPreferences();
  const router = useRouter();
  const { bills, isLoading } = useBillsStore();
  const [events, setEvents] = useState<BillEvent[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');

  useEffect(() => {
    if (!isLoading && bills) {
      setEvents(
        bills.map((bill) => {
          const dueDateObj = bill.dueDate ? parseLocalDateString(bill.dueDate) : null;
          return {
            id: bill.id,
            name: bill.name,
            amount: bill.amount,
            dueDate: bill.dueDate,
            status: bill.paidDate ? 'paid' : (dueDateObj && dueDateObj < new Date() ? 'overdue' : 'pending'),
            category: bill.category
          };
        })
      );
      setLoading(false);
    }
  }, [bills, isLoading]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getDayName = (dateString: string) => {
    const dateObj = parseLocalDateString(dateString);
    return dateObj ? dateObj.toLocaleDateString('en-US', { weekday: 'short' }) : '';
  };

  const getDayNumber = (dateString: string) => {
    const dateObj = parseLocalDateString(dateString);
    return dateObj ? dateObj.getDate() : '';
  };

  const getMonthName = (dateString: string) => {
    const dateObj = parseLocalDateString(dateString);
    return dateObj ? dateObj.toLocaleDateString('en-US', { month: 'short' }) : '';
  };

  const getStatusColor = (status: BillEvent['status']) => {
    switch (status) {
      case 'paid':
        return 'text-green-600 dark:text-green-400';
      case 'overdue':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-blue-600 dark:text-blue-400';
    }
  };

  // Generate calendar days for the current month
  const generateCalendarDays = (): CalendarDay[] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const firstDayOfMonth = new Date(year, month, 1);
    const startDate = new Date(firstDayOfMonth);
    startDate.setDate(startDate.getDate() - firstDayOfMonth.getDay());

    const days: CalendarDay[] = [];
    const currentDay = new Date(startDate);

    // Generate 42 days (6 weeks)
    for (let i = 0; i < 42; i++) {
      const isCurrentMonth = currentDay.getMonth() === month;
      const isToday = currentDay.getTime() === today.getTime();

      const dayBills = events.filter(event => {
        const eventDate = parseLocalDateString(event.dueDate);
        return eventDate &&
          eventDate.getDate() === currentDay.getDate() &&
          eventDate.getMonth() === currentDay.getMonth() &&
          eventDate.getFullYear() === currentDay.getFullYear();
      });

      days.push({
        date: new Date(currentDay),
        isCurrentMonth,
        isToday,
        bills: dayBills
      });

      currentDay.setDate(currentDay.getDate() + 1);
    }

    return days;
  };

  const calendarDays = generateCalendarDays();
  const monthName = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  return (
    <div className="enhanced-calendar-widget w-full h-full flex flex-col bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600 p-2 transition-all duration-200 overflow-hidden relative">
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
        </div>
      ) : viewMode === 'calendar' ? (
        // Mini Calendar View
        <div className="flex flex-col h-full">
          {/* Calendar Header */}
          <div className="flex items-center justify-between mb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-1">
            <button
              onClick={() => navigateMonth('prev')}
              className="p-1 rounded hover:bg-white dark:hover:bg-gray-700 transition-all duration-200 touch-manipulation min-h-[32px] min-w-[32px] flex items-center justify-center"
              aria-label="Previous month"
            >
              <ChevronLeftIcon className="w-3 h-3 text-blue-600 dark:text-blue-400" />
            </button>

            <h3 className="text-xs font-bold text-gray-900 dark:text-gray-100 text-center flex-1 bg-white dark:bg-gray-800 rounded py-1 px-2">
              {monthName}
            </h3>

            <button
              onClick={() => navigateMonth('next')}
              className="p-1 rounded hover:bg-white dark:hover:bg-gray-700 transition-all duration-200 touch-manipulation min-h-[32px] min-w-[32px] flex items-center justify-center"
              aria-label="Next month"
            >
              <ChevronRightIcon className="w-3 h-3 text-blue-600 dark:text-blue-400" />
            </button>
          </div>

          {/* Weekday Headers */}
          <div className="grid grid-cols-7 gap-0.5 mb-1">
            {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
              <div key={index} className="text-xs font-medium text-gray-600 dark:text-gray-400 text-center py-1">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-0.5 flex-1">
            {calendarDays.slice(0, 35).map((day, index) => (
              <div
                key={index}
                className={`
                  relative aspect-square flex flex-col items-center justify-center text-xs rounded transition-all duration-200 cursor-pointer touch-manipulation min-h-[24px]
                  ${day.isCurrentMonth
                    ? 'text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                    : 'text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-gray-800'
                  }
                  ${day.isToday
                    ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white font-bold'
                    : ''
                  }
                `}
                onClick={() => trackAction('calendar_date_click')}
              >
                <span className={`text-xs ${day.isToday ? 'text-white' : ''}`}>{day.date.getDate()}</span>

                {/* Bill indicators */}
                {day.bills.length > 0 && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex gap-0.5">
                    {day.bills.slice(0, 2).map((bill, billIndex) => (
                      <div
                        key={billIndex}
                        className={`w-1 h-1 rounded-full ${bill.status === 'paid'
                          ? 'bg-green-500'
                          : bill.status === 'overdue'
                            ? 'bg-red-500'
                            : 'bg-blue-500'
                          }`}
                        title={`${bill.name}: ${formatCurrency(bill.amount)}`}
                      />
                    ))}
                    {day.bills.length > 2 && (
                      <div className="w-1 h-1 rounded-full bg-gray-400" title={`+${day.bills.length - 2} more`} />
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* View Toggle & Action Button */}
          <div className="flex items-center justify-between mt-2 pt-1 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded p-1">
            <button
              onClick={() => setViewMode('list')}
              className="text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors touch-manipulation min-h-[28px] px-2 py-1 bg-white dark:bg-gray-700 rounded"
            >
              📋
            </button>
            <button
              onClick={() => {
                trackAction('view_full_calendar');
                router.push('/calendar');
              }}
              className="text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors touch-manipulation min-h-[28px] px-2 py-1 bg-white dark:bg-gray-700 rounded"
            >
              🔍
            </button>
          </div>
        </div>
      ) : (
        // List View
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between mb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-1">
            <h3 className="text-xs font-bold text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 rounded py-1 px-2">
              📋 Bills
            </h3>
            <button
              onClick={() => setViewMode('calendar')}
              className="text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors touch-manipulation min-h-[28px] px-2 py-1 bg-white dark:bg-gray-700 rounded"
            >
              📅
            </button>
          </div>

          <div className="space-y-1 flex-1 overflow-y-auto">
            {events.slice(0, 3).map((event) => (
              <div
                key={event.id}
                className="flex gap-2 p-2 bg-white dark:bg-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-all duration-200 cursor-pointer touch-manipulation min-h-[32px]"
                onClick={() => trackAction('view_bill_details')}
              >
                <div className="w-8 h-8 rounded bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 flex flex-col items-center justify-center text-center flex-shrink-0">
                  <div className="text-xs font-bold text-blue-600 dark:text-blue-400">
                    {getDayNumber(event.dueDate)}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-center">
                    <div className="min-w-0 flex-1">
                      <div className="text-xs font-semibold truncate text-gray-900 dark:text-gray-100">{event.name}</div>
                    </div>
                    <div className={`text-xs font-bold ml-1 ${getStatusColor(event.status)}`}>
                      {formatCurrency(event.amount)}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {events.length === 0 && (
              <div className="text-center py-3 text-gray-500 dark:text-gray-400 text-xs bg-white dark:bg-gray-700 rounded">
                <div className="text-lg mb-1">📅</div>
                <div className="font-medium">No bills</div>
              </div>
            )}
          </div>

          <button
            className="w-full mt-2 py-2 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded transition-colors touch-manipulation min-h-[28px]"
            onClick={() => {
              trackAction('view_full_calendar');
              router.push('/calendar');
            }}
          >
            🔍 Full
          </button>
        </div>
      )}
    </div>
  );
}
