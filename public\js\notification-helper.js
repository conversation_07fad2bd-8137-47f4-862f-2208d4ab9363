// Notification permission helper with user-friendly approach
const NotificationHelper = {
  // Check if notifications are supported and permission status
  checkPermission: async function() {
    if (!('Notification' in window)) {
      console.log('This browser does not support notifications');
      return false;
    }
    
    return Notification.permission;
  },
  
  // Request permission with context and only after user interaction
  requestPermission: async function(reason) {
    if (!('Notification' in window)) {
      console.log('This browser does not support notifications');
      return false;
    }
    
    // Only request if not already granted or denied
    if (Notification.permission === 'default') {
      try {
        // Log the reason for analytics
        console.log(`Requesting notification permission for: ${reason}`);
        const permission = await Notification.requestPermission();
        return permission === 'granted';
      } catch (error) {
        console.error('Error requesting notification permission:', error);
        return false;
      }
    }
    
    return Notification.permission === 'granted';
  },
  
  // Send a notification if permission is granted
  sendNotification: async function(title, options = {}) {
    const hasPermission = await this.checkPermission();
    
    if (hasPermission === 'granted') {
      try {
        const notification = new Notification(title, {
          badge: '/icons/icon-72x72.png',
          icon: '/icons/icon-192x192.png',
          ...options
        });
        
        // Handle notification click
        notification.onclick = function(event) {
          event.preventDefault();
          if (options.url) {
            window.open(options.url, '_blank');
          }
          notification.close();
        };
        
        return notification;
      } catch (error) {
        console.error('Error sending notification:', error);
        return null;
      }
    }
    
    return null;
  }
};

// Make available globally
window.NotificationHelper = NotificationHelper;
