/**
 * Utility functions for date manipulation and formatting
 */

/**
 * Safely parses a date string (YYYY-MM-DD, MM/DD/YYYY, or MM-DD-YYYY) into a local Date object.
 * Avoids timezone issues inherent in `new Date(string)`.
 */
export function parseLocalDateString(dateStr: string): Date | undefined {
  if (!dateStr || typeof dateStr !== 'string') return undefined;

  // Remove any whitespace
  const cleanStr = dateStr.trim();
  if (!cleanStr) return undefined;

  let year: number, month: number, day: number;

  // Handle YYYY-MM-DD format (ISO format)
  if (cleanStr.includes('-') && cleanStr.length === 10) {
    const parts = cleanStr.split('-').map(Number);
    if (parts.length === 3 && parts.every(p => !isNaN(p) && isFinite(p))) {
      [year, month, day] = parts;
      // Validate year is reasonable (between 1900 and 2100)
      if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
        const date = new Date(year, month - 1, day);
        // Verify the date is valid (handles invalid dates like Feb 30)
        if (date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day) {
          return date;
        }
      }
    }
  }

  // Handle MM/DD/YYYY or MM-DD-YYYY format
  if ((cleanStr.includes('/') || cleanStr.includes('-')) && cleanStr.length >= 8) {
    const separator = cleanStr.includes('/') ? '/' : '-';
    const parts = cleanStr.split(separator).map(Number);
    if (parts.length === 3 && parts.every(p => !isNaN(p) && isFinite(p))) {
      [month, day, year] = parts;
      // Validate components
      if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
        const date = new Date(year, month - 1, day);
        // Verify the date is valid
        if (date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day) {
          return date;
        }
      }
    }
  }

  // If all parsing attempts fail, log warning and return undefined
  console.warn(`Failed to parse date string: "${dateStr}"`);
  return undefined;
}

/**
 * Format a date into a human-readable string
 */
export function formatDate(date: Date | string | null | undefined, format: string = 'medium'): string {
  if (!date) return '';

  // Use safe parsing if input is a string
  const dateObj = typeof date === 'string' ? parseLocalDateString(date) : date;

  // Check if valid date after potential parsing
  if (!dateObj || isNaN(dateObj.getTime())) {
    return ''; // Return empty if parsing failed or date is invalid
  }

  switch (format) {
    case 'short':
      return dateObj.toLocaleDateString();
    case 'medium':
      return dateObj.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    case 'long':
      return dateObj.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    case 'relative':
      return getRelativeTimeString(dateObj);
    default:
      return dateObj.toLocaleDateString();
  }
}

/**
 * Get a relative time string (e.g., "2 days ago", "in 3 days")
 */
export function getRelativeTimeString(date: Date | string | null | undefined): string {
  if (!date) return '';
  const dateObj = typeof date === 'string' ? parseLocalDateString(date) : date;
  const now = new Date();

  if (!dateObj || isNaN(dateObj.getTime())) return ''; // Check validity

  const diffTime = dateObj.getTime() - now.getTime();
  const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Tomorrow';
  } else if (diffDays === -1) {
    return 'Yesterday';
  } else if (diffDays > 1 && diffDays < 7) {
    return `In ${diffDays} days`;
  } else if (diffDays < 0 && diffDays > -7) {
    return `${Math.abs(diffDays)} days ago`;
  } else {
    return formatDate(dateObj, 'medium');
  }
}

/**
 * Get the date range for a given time period
 */
export function getDateRange(period: 'week' | 'month' | 'quarter' | 'year'): { start: Date; end: Date } {
  const now = new Date();
  let end = new Date(now);
  let start = new Date(now);

  switch (period) {
    case 'week':
      start.setDate(now.getDate() - now.getDay());
      end.setDate(start.getDate() + 6);
      break;
    case 'month':
      start = new Date(now.getFullYear(), now.getMonth(), 1);
      end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      break;
    case 'quarter':
      const quarter = Math.floor(now.getMonth() / 3);
      start = new Date(now.getFullYear(), quarter * 3, 1);
      end = new Date(now.getFullYear(), (quarter + 1) * 3, 0);
      break;
    case 'year':
      start = new Date(now.getFullYear(), 0, 1);
      end = new Date(now.getFullYear(), 11, 31);
      break;
  }

  return { start, end };
}

/**
 * Add a specified number of days to a date
 */
export function addDays(date: Date | string | null | undefined, days: number): Date | undefined {
  if (!date) return undefined;
  if (typeof days !== 'number' || !isFinite(days)) {
    // console.warn('addDays: days parameter must be a finite number. Received:', days);
    return undefined;
  }
  const dateObj = typeof date === 'string' ? parseLocalDateString(date) : (date instanceof Date ? new Date(date) : undefined);

  if (!dateObj || isNaN(dateObj.getTime())) return undefined; // Check validity

  dateObj.setDate(dateObj.getDate() + days);
  // Check if date became invalid after adding days (e.g. if 'days' was extremely large, though unlikely for typical use)
  if (isNaN(dateObj.getTime())) {
    // console.warn('addDays: date became invalid after adding days.');
    return undefined;
  }
  return dateObj;
}

/**
 * Check if a date is in the past
 */
export function isPastDate(date: Date | string | null | undefined): boolean {
  if (!date) return false;
  const dateObj = typeof date === 'string' ? parseLocalDateString(date) : date;

  if (!dateObj || isNaN(dateObj.getTime())) return false; // Check validity

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return dateObj < today;
}

/**
 * Check if a date is today
 */
export function isToday(date: Date | string | null | undefined): boolean {
  if (!date) return false;
  const dateObj = typeof date === 'string' ? parseLocalDateString(date) : date;

  if (!dateObj || isNaN(dateObj.getTime())) return false; // Check validity

  const today = new Date();

  return (
    dateObj.getDate() === today.getDate() &&
    dateObj.getMonth() === today.getMonth() &&
    dateObj.getFullYear() === today.getFullYear()
  );
}

/**
 * Calculate the next occurrence of a recurring bill
 */
export function getNextOccurrence(
  date: Date | string | null | undefined,
  frequency: 'weekly' | 'biweekly' | 'monthly' | 'quarterly' | 'yearly'
): Date | undefined {
  if (!date) return undefined;
  const dateObj = typeof date === 'string' ? parseLocalDateString(date) : (date instanceof Date ? new Date(date) : undefined);

  if (!dateObj || isNaN(dateObj.getTime())) return undefined; // Check validity

  switch (frequency) {
    case 'weekly':
      return addDays(dateObj, 7);
    case 'biweekly':
      return addDays(dateObj, 14);
    case 'monthly':
      const newMonth = dateObj.getMonth() + 1;
      return new Date(dateObj.getFullYear(), newMonth, dateObj.getDate());
    case 'quarterly':
      const newQuarterMonth = dateObj.getMonth() + 3;
      return new Date(dateObj.getFullYear(), newQuarterMonth, dateObj.getDate());
    case 'yearly':
      const newYear = dateObj.getFullYear() + 1;
      return new Date(newYear, dateObj.getMonth(), dateObj.getDate());
    default:
      // console.warn(`getNextOccurrence: Unknown frequency '${frequency as string}'. Returning undefined.`);
      return undefined;
  }
}

/**
 * Check if a date is within a specified number of days from another date
 */
export function isWithinDays(
  date: Date | string | null | undefined,
  fromDate: Date | string | null | undefined,
  days: number
): boolean {
  if (!date || !fromDate) return false;
  if (typeof days !== 'number' || !isFinite(days) || days < 0) {
    // console.warn('isWithinDays: days parameter must be a non-negative finite number. Received:', days);
    return false;
  }

  const dateObj = typeof date === 'string' ? parseLocalDateString(date) : date;
  const fromDateObj = typeof fromDate === 'string' ? parseLocalDateString(fromDate) : fromDate;

  if (!dateObj || isNaN(dateObj.getTime()) || !fromDateObj || isNaN(fromDateObj.getTime())) {
    return false; // Check validity of both dates
  }

  // Reset time to midnight for accurate day comparison
  const dateWithoutTime = new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate());
  const fromDateWithoutTime = new Date(fromDateObj.getFullYear(), fromDateObj.getMonth(), fromDateObj.getDate());

  // Calculate difference in milliseconds and convert to days
  const diffTime = Math.abs(dateWithoutTime.getTime() - fromDateWithoutTime.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays <= days;
}
