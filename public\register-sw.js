const DEFAULT_VERSION = '0.6.5';

function resolveScriptContext() {
  if (typeof document === 'undefined') {
    return { version: DEFAULT_VERSION, swPath: `/custom-sw.js?v=${DEFAULT_VERSION}` };
  }

  /** @type {HTMLScriptElement | null} */
  const scriptEl = document.currentScript || document.querySelector('script[data-controller]');
  const fallbackSw = `/custom-sw.js?v=${DEFAULT_VERSION}`;

  if (!scriptEl) {
    return { version: DEFAULT_VERSION, swPath: fallbackSw };
  }

  let versionFromSrc = null;
  if (scriptEl.src) {
    try {
      const url = new URL(scriptEl.src, window.location.href);
      versionFromSrc = url.searchParams.get('v');
    } catch (error) {
      console.warn('[SW register] Unable to parse script URL for version', error);
    }
  }

  const version = scriptEl.dataset?.version || versionFromSrc || DEFAULT_VERSION;
  const swPath = scriptEl.dataset?.controller || `/custom-sw.js?v=${version}`;

  return { version, swPath };
}

const { version: SCRIPT_VERSION, swPath: SW_PATH } = resolveScriptContext();

if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    // Allow the main thread to settle before registering
    setTimeout(async () => {
      try {
        const registration = await registerServiceWorker(SW_PATH);
        console.log('Service worker registered with scope:', registration.scope);
        setupPeriodicUpdates(registration);
      } catch (error) {
        console.error('Service worker registration failed:', error);
      }
    }, 300);
  });

  navigator.serviceWorker.addEventListener('controllerchange', () => {
    console.log('Service worker controller changed - refreshing for latest assets');
  });
}

async function registerServiceWorker(swPath) {
  const scriptAvailable = await checkScriptAvailability(swPath);

  if (!scriptAvailable) {
    console.warn('Service worker script missing or offline - attempting cached registration');
  }

  return navigator.serviceWorker.register(swPath, {
    scope: '/',
    updateViaCache: 'none'
  });
}

async function checkScriptAvailability(path) {
  try {
    const response = await fetch(path, { method: 'HEAD', cache: 'no-store' });
    return response.ok;
  } catch (error) {
    console.warn('Unable to verify service worker script availability:', error);
    return false;
  }
}

function setupPeriodicUpdates(registration) {
  // Initial update a few seconds after install
  setTimeout(() => {
    try {
      registration.update().catch((error) => {
        console.warn('Initial service worker update failed:', error);
      });
    } catch (error) {
      console.warn('Unable to trigger initial service worker update:', error);
    }
  }, 5000);

  const interval = setInterval(() => {
    if (!navigator.onLine) {
      return;
    }

    registration.update().catch((error) => {
      console.warn('Service worker update check failed:', error);
      if (error && error.name === 'InvalidStateError') {
        clearInterval(interval);
      }
    });
  }, 60 * 60 * 1000);

  if (registration.waiting) {
    sendSkipWaitingMessage(registration.waiting);
  }

  registration.addEventListener('updatefound', () => {
    const newWorker = registration.installing;

    if (!newWorker) return;

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
        sendSkipWaitingMessage(newWorker);
      }
    });
  });
}

function sendSkipWaitingMessage(worker) {
  const messageChannel = new MessageChannel();

  messageChannel.port1.onmessage = (event) => {
    if (event.data && event.data.success) {
      window.dispatchEvent(new Event('sw-update-available'));
    }
  };

  worker.postMessage({ type: 'SKIP_WAITING', version: SCRIPT_VERSION }, [messageChannel.port2]);
}
