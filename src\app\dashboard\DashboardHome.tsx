import AddBillForm from '@/components/bills/BillForm';
import { BillsOverview } from '@/components/bills/BillsOverview';
import { DashboardCard } from '@/components/dashboard/DashboardCard';
import {
  AddBillCardContent,
  AIAssistantCardContent,
  BillsOverviewCardContent,
  CalculatorCardContent,
  CalendarCardContent,
  NotificationsCardContent,
  SettingsCardContent
} from '@/components/dashboard/DashboardCardContent';
import { DashboardCardModal } from '@/components/dashboard/DashboardCardModal';
import { MobileDashboard } from '@/components/dashboard/MobileDashboard';
import { useModal } from '@/components/ui/Modal';
import { useChangelogNotification } from '@/hooks/useChangelogNotification';
import { useBillsStore } from '@/stores/billsStore';
import { BillFormData } from '@/types/bill';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { memo, useCallback, useMemo } from 'react';

const SettingsPanel = dynamic(() => import('@/app/settings/page').then(mod => mod.default), { ssr: false });
const NotificationsModal = dynamic(() => import('@/components/notifications/NotificationsModal').then(mod => mod.default), { ssr: false });
const NotificationPreview = dynamic(() => import('@/components/notifications/NotificationPreview').then(mod => mod.default), { ssr: false });
// Dynamically import BillCalendarApp for dashboard use
const BillCalendarApp = dynamic(() => import('@/components/calendar/BillCalendar.new'), { ssr: false });
// Dynamically import FinancialCalculator for dashboard use
const FinancialCalculator = dynamic(
  () => import('@/components/calculator/FinancialCalculator').then(mod => mod.FinancialCalculator),
  { ssr: false }
);
// Dynamically import AI Assistant page for dashboard use
const AiPage = dynamic(() => import('@/app/ai/page').then(mod => mod.default), { ssr: false });

// Memoized ClickableCard component to prevent unnecessary re-renders
const ClickableCard = memo(function ClickableCard({
  title,
  children,
  previewContent,
  modalSize = 'xl',
  showUpdateIndicator = false,
  onModalOpen,
}: {
  title: string;
  children: React.ReactNode | ((args: { close: () => void }) => React.ReactNode);
  previewContent?: React.ReactNode;
  modalSize?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showUpdateIndicator?: boolean;
  onModalOpen?: () => void;
}) {
  const { isOpen, openModal, closeModal } = useModal();
  const openStartRef = React.useRef<number | null>(null);

  const renderedChildren = useMemo(() => {
    if (typeof children === 'function') {
      return (children as (args: { close: () => void }) => React.ReactNode)({ close: closeModal });
    }
    return children;
  }, [children, closeModal]);

  const previewSlot = previewContent ?? renderedChildren;

  const handleOpenModal = useCallback(() => {
    openStartRef.current = performance.now();
    openModal();
    onModalOpen?.();
  }, [openModal, onModalOpen]);

  React.useEffect(() => {
    if (isOpen && openStartRef.current != null && process.env.NODE_ENV !== 'production') {
      const dt = performance.now() - openStartRef.current;
      // eslint-disable-next-line no-console
      console.log(`[perf] ${title} modal open: ${Math.round(dt)}ms`);
      openStartRef.current = null;
    }
  }, [isOpen, title]);

  return (
    <>
      <DashboardCard data-testid={`card-${title.toLowerCase().replace(/\s+/g, '-')}`} title={title} className="flex flex-col h-auto relative aspect-square sm:aspect-auto" onClick={handleOpenModal}>
        {/* Update indicator */}
        {showUpdateIndicator && (
          <div className="absolute top-1 right-1 h-3 w-3 bg-red-500 rounded-full border-2 border-white dark:border-gray-900 z-10">
            <div className="absolute inset-0 bg-red-500 rounded-full animate-ping" />
          </div>
        )}

        <div className="flex-1 relative rounded-xl mb-3 sm:mb-4 min-h-0">{previewSlot}</div>
        <div className="text-center flex-shrink-0">
          <span className="inline-flex items-center justify-center gap-2 text-primary-600 dark:text-primary-400 font-medium body-sm hover:text-primary-700 dark:hover:text-primary-300 transition-colors action-text">
            View {title}
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </span>
        </div>
      </DashboardCard>

      <DashboardCardModal isOpen={isOpen} onClose={closeModal} title={title} size={modalSize}>
        {renderedChildren}
      </DashboardCardModal>
    </>
  );
});

export default function DashboardHome() {
  const { bills, addBill } = useBillsStore();
  const router = useRouter();

  // Use the hook directly - Next.js handles SSR properly for client components
  const changelogNotification = useChangelogNotification();

  // Memoized handlers to prevent unnecessary re-renders
  const handleAddBill = useCallback(
    (billData: BillFormData) => {
      addBill(billData);
      router.push('/dashboard');
    },
    [addBill, router]
  );

  // Memoized bills array to prevent unnecessary re-renders
  const memoizedBills = useMemo(() => bills || [], [bills]);

  return (
    <div className="bg-neutral-50 dark:bg-neutral-900 min-h-0">
      <div className="sm:hidden">
        <MobileDashboard />
      </div>

      <div className="hidden sm:block p-6 lg:p-8">
        <div className="max-w-7xl mx-auto dashboard-cards-container flex-1 flex flex-col space-y-4">
          {/* Mobile brand header with app icon */}
          <div className="sm:hidden flex items-center justify-between mb-2">
            <div className="flex items-center gap-2 relative">
              <div className="relative inline-block">
                <Image src="/icons/icon-48x48.png" alt="PaydayPilot" width={24} height={24} className="h-6 w-6 rounded-md" priority />
                {changelogNotification.hasNewUpdate && (
                  <span className="absolute -top-1 -right-1 h-2.5 w-2.5 bg-red-500 rounded-full ring-2 ring-white dark:ring-gray-900">
                    <span className="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-60"></span>
                  </span>
                )}
              </div>
              <span className="font-semibold text-neutral-900 dark:text-white">PaydayPilot</span>
            </div>
          </div>
          {/* Dashboard Cards Grid - Responsive layout with consistent spacing */}
          <div id="main-content" className="grid dashboard-cards-grid gap-4 sm:gap-6 auto-rows-fr
                          grid-cols-2
                          sm:grid-cols-2
                          lg:grid-cols-3
                          xl:grid-cols-3
                          2xl:grid-cols-4
                          content-start">
            <ClickableCard
              title="Add Bill"
              modalSize="lg"
              previewContent={<AddBillCardContent />}
              className="card-focus-enhanced"
            >
              <AddBillForm onSave={handleAddBill} onCancel={() => { }} allBills={memoizedBills} />
            </ClickableCard>

            {/* Enhanced Calendar Card */}
            <ClickableCard
              title="Calendar"
              modalSize="full"
              previewContent={<CalendarCardContent />}
              className="card-focus-enhanced"
            >
              <div className="h-[80vh]">
                <BillCalendarApp compact={false} />
              </div>
            </ClickableCard>

            {/* Settings and Notifications as clickable cards */}
            <ClickableCard
              title="Settings"
              modalSize="xl"
              showUpdateIndicator={changelogNotification.hasNewUpdate}
              onModalOpen={() => changelogNotification.markChangelogAsRead()}
              previewContent={<SettingsCardContent hasUpdate={changelogNotification.hasNewUpdate} />}
              className="card-focus-enhanced"
            >
              <div className="p-0">
                <SettingsPanel />
              </div>
            </ClickableCard>

            <ClickableCard title="Notifications" modalSize="lg" previewContent={<NotificationsCardContent />} className="card-focus-enhanced">
              <NotificationsModal />
            </ClickableCard>

            {/* AI Assistant Card */}
            <ClickableCard
              title="AI Assistant"
              modalSize="xl"
              previewContent={<AIAssistantCardContent />}
              className="card-focus-enhanced"
            >
              <div className="dashboard-modal-ai-assistant">
                <AiPage />
              </div>
            </ClickableCard>

            {/* Bills Overview Card */}
            <ClickableCard title="Bills Overview" modalSize="xl" previewContent={<BillsOverviewCardContent />} className="card-focus-enhanced">
              <BillsOverview bills={memoizedBills} />
            </ClickableCard>

            {/* Calculator Card */}
            <ClickableCard
              title="Financial Calculator"
              modalSize="lg"
              previewContent={<CalculatorCardContent />}
              className="card-focus-enhanced"
            >
              <div className="dashboard-modal-calculator">
                <FinancialCalculator />
              </div>
            </ClickableCard>
          </div>
        </div>
      </div>
    </div>
  );
}


