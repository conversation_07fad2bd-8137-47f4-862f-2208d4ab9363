﻿'use client';

import { useBillsStore } from '@/stores/billsStore';
import { Bill } from '@/types/bill';
import { formatDate, isPastDate, parseLocalDateString } from '@/utils/date';
import {
  CalendarIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { useMemo, useState } from 'react';

interface BillsOverviewProps {
  bills: Bill[] | null | undefined;
}

interface BillFilters {
  search: string;
  status: 'all' | 'overdue' | 'due-soon' | 'upcoming' | 'paid';
  category: string;
  sortBy: 'dueDate' | 'amount' | 'name' | 'status';
  sortOrder: 'asc' | 'desc';
}

export function BillsOverview({ bills }: BillsOverviewProps) {
  const { markBillPaid, markBillUnpaid, deleteBill, processAutomaticBillRenewal, processBatchBillRenewal } = useBillsStore();
  const [filters, setFilters] = useState<BillFilters>({
    search: '',
    status: 'all',
    category: '',
    sortBy: 'dueDate',
    sortOrder: 'asc'
  });

  const [renewalState, setRenewalState] = useState({
    isProcessing: false,
    showRenewalPanel: false,
    selectedBills: new Set<string>(),
    lastRenewalResults: null as any
  });

  // Calculate bill statistics
  const billStats = useMemo(() => {
    if (!bills || bills.length === 0) {
      return {
        total: 0,
        overdue: 0,
        dueSoon: 0,
        upcoming: 0,
        paid: 0,
        totalAmount: 0,
        overdueAmount: 0,
        dueSoonAmount: 0
      };
    }

    const today = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(today.getDate() + 3);

    let overdue = 0, dueSoon = 0, upcoming = 0, paid = 0;
    let totalAmount = 0, overdueAmount = 0, dueSoonAmount = 0;

    bills.forEach(bill => {
      totalAmount += bill.amount;

      if (bill.paidDate) {
        paid++;
      } else {
        const dueDate = parseLocalDateString(bill.dueDate);
        if (dueDate) {
          if (isPastDate(bill.dueDate)) {
            overdue++;
            overdueAmount += bill.amount;
          } else if (dueDate <= threeDaysFromNow) {
            dueSoon++;
            dueSoonAmount += bill.amount;
          } else {
            upcoming++;
          }
        }
      }
    });

    return {
      total: bills.length,
      overdue,
      dueSoon,
      upcoming,
      paid,
      totalAmount,
      overdueAmount,
      dueSoonAmount
    };
  }, [bills]);

  // Filter and sort bills
  const filteredAndSortedBills = useMemo(() => {
    if (!bills) return [];

    let filtered = bills.filter(bill => {
      // Search filter
      if (filters.search && !bill.name.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }

      // Status filter
      if (filters.status !== 'all') {
        const dueDate = parseLocalDateString(bill.dueDate);
        const today = new Date();
        const threeDaysFromNow = new Date();
        threeDaysFromNow.setDate(today.getDate() + 3);

        switch (filters.status) {
          case 'overdue':
            return !bill.paidDate && dueDate && isPastDate(bill.dueDate);
          case 'due-soon':
            return !bill.paidDate && dueDate && !isPastDate(bill.dueDate) && dueDate <= threeDaysFromNow;
          case 'upcoming':
            return !bill.paidDate && dueDate && dueDate > threeDaysFromNow;
          case 'paid':
            return !!bill.paidDate;
        }
      }

      // Category filter
      if (filters.category && bill.category !== filters.category) {
        return false;
      }

      return true;
    });

    // Sort bills
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (filters.sortBy) {
        case 'dueDate':
          const dateA = parseLocalDateString(a.dueDate);
          const dateB = parseLocalDateString(b.dueDate);
          if (dateA && dateB) {
            comparison = dateA.getTime() - dateB.getTime();
          }
          break;
        case 'amount':
          comparison = a.amount - b.amount;
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'status':
          const getStatusPriority = (bill: Bill) => {
            if (bill.paidDate) return 4;
            if (isPastDate(bill.dueDate)) return 1;
            const dueDate = parseLocalDateString(bill.dueDate);
            if (dueDate && dueDate <= new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)) return 2;
            return 3;
          };
          comparison = getStatusPriority(a) - getStatusPriority(b);
          break;
      }

      return filters.sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [bills, filters]);

  // Renewal functions
  const handleRenewBill = async (billId: string) => {
    setRenewalState(prev => ({ ...prev, isProcessing: true }));
    try {
      const result = await processAutomaticBillRenewal(billId);
      if (result.success) {
        alert(`Bill renewed successfully for ${result.renewedBill?.dueDate}`);
      } else {
        alert(`Failed to renew bill: ${result.error || result.reason}`);
      }
    } catch (error) {
      alert('Error renewing bill');
    } finally {
      setRenewalState(prev => ({ ...prev, isProcessing: false }));
    }
  };

  const handleBatchRenewal = async () => {
    const billIds = Array.from(renewalState.selectedBills);
    if (billIds.length === 0) {
      alert('Please select bills to renew');
      return;
    }

    setRenewalState(prev => ({ ...prev, isProcessing: true }));
    try {
      const results = await processBatchBillRenewal(billIds);
      setRenewalState(prev => ({
        ...prev,
        lastRenewalResults: results,
        selectedBills: new Set(),
        showRenewalPanel: true
      }));
    } catch (error) {
      alert('Error processing batch renewal');
    } finally {
      setRenewalState(prev => ({ ...prev, isProcessing: false }));
    }
  };

  const toggleBillSelection = (billId: string) => {
    setRenewalState(prev => {
      const newSelected = new Set(prev.selectedBills);
      if (newSelected.has(billId)) {
        newSelected.delete(billId);
      } else {
        newSelected.add(billId);
      }
      return { ...prev, selectedBills: newSelected };
    });
  };

  // Get bill status info
  const getBillStatus = (bill: Bill) => {
    if (bill.paidDate) {
      return {
        label: 'Paid',
        color: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/20',
        icon: CheckCircleIcon
      };
    }

    if (isPastDate(bill.dueDate)) {
      return {
        label: 'Overdue',
        color: 'text-red-600 dark:text-red-400',
        bgColor: 'bg-red-50 dark:bg-red-900/20',
        icon: ExclamationTriangleIcon
      };
    }

    const dueDate = parseLocalDateString(bill.dueDate);
    if (dueDate) {
      const today = new Date();
      const diffTime = dueDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays <= 3) {
        return {
          label: 'Due Soon',
          color: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          icon: ClockIcon
        };
      }
    }

    return {
      label: 'Upcoming',
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      icon: CalendarIcon
    };
  };

  // Get unique categories
  const categories = useMemo(() => {
    if (!bills) return [];
    const uniqueCategories = [...new Set(bills.map(bill => bill.category).filter(Boolean))];
    return uniqueCategories.sort();
  }, [bills]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 2
    }).format(amount);
  };

  if (!bills || bills.length === 0) {
    return (
      <div className="p-6 text-center">
        <div className="text-gray-400 dark:text-gray-500 mb-4">
          <CurrencyDollarIcon className="w-16 h-16 mx-auto mb-4" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No Bills Yet
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Start by adding your first bill to track your payments and due dates.
        </p>
      </div>
    );
  }

  return (
    <div className="p-0">
      {/* Header with Statistics */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Bills Overview
        </h3>

        {/* Statistics Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Bills</div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">{billStats.total}</div>
          </div>
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
            <div className="text-sm text-red-600 dark:text-red-400">Overdue</div>
            <div className="text-2xl font-bold text-red-700 dark:text-red-400">{billStats.overdue}</div>
            <div className="text-xs text-red-600 dark:text-red-400">{formatCurrency(billStats.overdueAmount)}</div>
          </div>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
            <div className="text-sm text-yellow-600 dark:text-yellow-400">Due Soon</div>
            <div className="text-2xl font-bold text-yellow-700 dark:text-yellow-400">{billStats.dueSoon}</div>
            <div className="text-xs text-yellow-600 dark:text-yellow-400">{formatCurrency(billStats.dueSoonAmount)}</div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <div className="text-sm text-green-600 dark:text-green-400">Paid</div>
            <div className="text-2xl font-bold text-green-700 dark:text-green-400">{billStats.paid}</div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search bills..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as any }))}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="overdue">Overdue</option>
            <option value="due-soon">Due Soon</option>
            <option value="upcoming">Upcoming</option>
            <option value="paid">Paid</option>
          </select>

          {/* Category Filter */}
          {categories.length > 0 && (
            <select
              value={filters.category}
              onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          )}

          {/* Sort Controls */}
          <div className="flex gap-2">
            <select
              value={filters.sortBy}
              onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="dueDate">Due Date</option>
              <option value="amount">Amount</option>
              <option value="name">Name</option>
              <option value="status">Status</option>
            </select>
            <button
              onClick={() => setFilters(prev => ({ ...prev, sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc' }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {filters.sortOrder === 'asc' ? 'â†‘' : 'â†“'}
            </button>
          </div>
        </div>

        {/* Renewal Controls */}
        <div className="mt-4 flex flex-wrap gap-2 items-center">
          <button
            onClick={handleBatchRenewal}
            disabled={renewalState.selectedBills.size === 0 || renewalState.isProcessing}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white text-sm rounded-lg transition-colors flex items-center gap-2"
          >
            {renewalState.isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Processing...
              </>
            ) : (
              <>
                <span>🔁</span>
                Renew Selected ({renewalState.selectedBills.size})
              </>
            )}
          </button>

          <button
            onClick={() => setRenewalState(prev => ({ ...prev, selectedBills: new Set() }))}
            disabled={renewalState.selectedBills.size === 0}
            className="px-3 py-2 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white text-sm rounded-lg transition-colors"
          >
            Clear Selection
          </button>

          {renewalState.selectedBills.size > 0 && (
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {renewalState.selectedBills.size} bill{renewalState.selectedBills.size !== 1 ? 's' : ''} selected
            </span>
          )}
        </div>
      </div>

      {/* Bills List */}
      <div className="p-6">
        {filteredAndSortedBills.length === 0 ? (
          <div className="text-center py-8">
            <FunnelIcon className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              No bills match your current filters.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredAndSortedBills.map((bill) => {
              const status = getBillStatus(bill);
              const StatusIcon = status.icon;

              return (
                <div
                  key={bill.id}
                  className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    {/* Selection checkbox */}
                    <div className="flex items-start gap-3 flex-1 min-w-0">
                      <input
                        type="checkbox"
                        checked={renewalState.selectedBills.has(bill.id)}
                        onChange={() => toggleBillSelection(bill.id)}
                        className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-2">
                          <StatusIcon className={`w-5 h-5 ${status.color} flex-shrink-0`} />
                          <h4 className="font-semibold text-gray-900 dark:text-white truncate">
                            {bill.name}
                          </h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${status.bgColor} ${status.color}`}>
                            {status.label}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-1">
                            <CurrencyDollarIcon className="w-4 h-4" />
                            <span className="font-medium">{formatCurrency(bill.amount)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <CalendarIcon className="w-4 h-4" />
                            <span>Due {formatDate(bill.dueDate, 'short')}</span>
                          </div>
                          {bill.category && (
                            <div className="text-gray-500 dark:text-gray-400">
                              {bill.category}
                            </div>
                          )}
                        </div>

                        {bill.notes && (
                          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                            {bill.notes}
                          </p>
                        )}
                      </div>

                      {/* Quick Actions */}
                      <div className="flex flex-col gap-2 ml-4">
                        {!bill.paidDate ? (
                          <button
                            onClick={() => markBillPaid(bill.id)}
                            className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded-lg transition-colors min-h-[44px] sm:min-h-0"
                          >
                            Mark Paid
                          </button>
                        ) : (
                          <button
                            onClick={() => markBillUnpaid(bill.id)}
                            className="px-3 py-1 bg-amber-600 hover:bg-amber-700 text-white text-xs rounded-lg transition-colors min-h-[44px] sm:min-h-0"
                          >
                            Mark Unpaid
                          </button>
                        )}
                        {bill.isRecurring && !bill.isRenewedOriginal && (
                          <button
                            onClick={() => handleRenewBill(bill.id)}
                            disabled={renewalState.isProcessing}
                            className="px-3 py-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white text-xs rounded-lg transition-colors min-h-[44px] sm:min-h-0 flex items-center gap-1"
                          >
                            <span>🔁</span>
                            Renew
                          </button>
                        )}
                        <button
                          onClick={() => window.location.href = `/bills/${bill.id}/edit`}
                          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-lg transition-colors min-h-[44px] sm:min-h-0"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => window.location.href = `/bills/${bill.id}`}
                          className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded-lg transition-colors min-h-[44px] sm:min-h-0"
                        >
                          View
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

// Preview component for the dashboard card
export function BillsOverviewPreview({ bills }: BillsOverviewProps) {
  const stats = useMemo(() => {
    if (!bills || bills.length === 0) {
      return { total: 0, overdue: 0, totalAmount: 0 };
    }

    let overdue = 0;
    let totalAmount = 0;

    bills.forEach(bill => {
      totalAmount += bill.amount;
      if (!bill.paidDate && isPastDate(bill.dueDate)) {
        overdue++;
      }
    });

    return { total: bills.length, overdue, totalAmount };
  }, [bills]);

  return (
    <div className="p-4 sm:p-5 text-center text-gray-500 dark:text-gray-400 h-full flex flex-col justify-center items-center">
      <ChartBarIcon className="w-10 h-10 sm:w-12 sm:h-12 mb-2 text-blue-500" />
      <p className="text-sm sm:text-base font-medium leading-relaxed mb-2">Bills Overview</p>
      <div className="text-sm sm:text-base font-semibold text-blue-600 dark:text-blue-400 mb-1">
        {stats.total} bills
      </div>
      {stats.overdue > 0 && (
        <div className="text-xs sm:text-sm font-medium text-red-600 dark:text-red-400">
          {stats.overdue} overdue
        </div>
      )}
    </div>
  );
}


