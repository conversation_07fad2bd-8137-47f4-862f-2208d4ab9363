'use client';

import { DynamicThemeColor } from '@/components/theme/DynamicThemeColor';
import { useAuth } from '@/hooks/useAuth';
import { useServiceWorkerUpdate } from '@/hooks/useServiceWorkerUpdate';
import { safeThemeColorUpdate } from '@/utils/browserCompat';
import dynamic from 'next/dynamic';
import { usePathname } from 'next/navigation';
import { ReactNode, useEffect, useState } from 'react';
import Footer from './Footer';
import Header from './Header';
import { SafeCustomElements } from './SafeCustomElements';

// Import OnboardingFlow
const OnboardingFlow = dynamic(() =>
  import('@/components/onboarding/OnboardingFlow').then(mod => mod.OnboardingFlow),
  { ssr: false }
);

const InstallPrompt = dynamic(() =>
  import('@/components/pwa/InstallPrompt').then(mod => mod.InstallPrompt),
  { ssr: false }
);

/**
 * Component to reset theme color meta tags for the login page
 * This ensures the login page uses a neutral status bar color
 */
function LoginTheme() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Set a neutral color for the login page status bar using safe method
    safeThemeColorUpdate('#ffffff'); // White background for light mode

    // Also update the msapplication-TileColor
    let tileColorMetaTag = document.querySelector('meta[name="msapplication-TileColor"]');
    if (tileColorMetaTag) {
      tileColorMetaTag.setAttribute('content', '#ffffff');
    } else {
      tileColorMetaTag = document.createElement('meta');
      tileColorMetaTag.setAttribute('name', 'msapplication-TileColor');
      tileColorMetaTag.setAttribute('content', '#ffffff');
      document.head.appendChild(tileColorMetaTag);
    }

    // Clean up function to restore theme color when navigating away
    return () => {
      // No need to restore - the DynamicThemeColor component will handle that
    };
  }, []);

  return null;
}

interface AppLayoutProps {
  children: ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const pathname = usePathname();

  const { loading } = useAuth();
  const updateAvailable = useServiceWorkerUpdate();
  const [dismissed, setDismissed] = useState(false);

  // Firebase messaging is disabled in local-only mode

  // Routes that should not show the top navigation header for immersive dashboard
  // Add defensive checks for pathname
  const isLoginPage = pathname.startsWith('/login');
  const isDashboard = pathname === '/' || pathname.startsWith('/dashboard');

  // If this is the login page, return children directly without app layout wrapper
  if (isLoginPage) {
    return (
      <>
        <LoginTheme />
        {children}
      </>
    );
  }

  // Show loading spinner while auth state is loading
  if (loading) {
    return <div className="min-h-screen flex items-center justify-center text-2xl">Loading...</div>;
  }

  // If not authenticated, show the landing page
  // if (!isAuthenticated) {
  //   return <LandingPage />;
  // }

  // For all other pages, apply the app layout
  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      {/* Add DynamicThemeColor for all non-login pages to apply the theme color */}
      <DynamicThemeColor />

      {/* Patch customElements.define to ignore duplicate registrations from extensions */}
      <SafeCustomElements />

      {/* Header - hide on dashboard to maximize space per user preference */}
      {!isDashboard && <Header />}

      {/* Main content with padding for fixed header; remove padding on dashboard */}
      <main id="main-content" className={`flex-grow ${isDashboard ? '' : 'pt-20'}`}>
        {children}

        {/* Show update notification only if there's an update and it hasn't been dismissed */}
        {updateAvailable && !dismissed && (
          <div className="fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50">
            <p className="font-medium">App update available!</p>
            <p className="text-sm mb-2">Refresh to get the latest version</p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setDismissed(true)}
                className="px-3 py-1 text-sm bg-blue-700 rounded hover:bg-blue-800"
              >
                Later
              </button>
              <button
                onClick={() => window.location.reload()}
                className="px-3 py-1 text-sm bg-white text-blue-600 rounded hover:bg-gray-100"
              >
                Update now
              </button>
            </div>
          </div>
        )}

        {/* Conditionally render Onboarding Flow */}
        <OnboardingFlow />

        {/* Install PWA Prompt */}
        <InstallPrompt />
      </main>

      {/* Footer - positioned appropriately for different layouts */}
      <div className="hidden sm:block">
        <Footer />
      </div>
      {/* Mobile footer - only show on non-dashboard pages */}
      {!isDashboard && (
        <div className="sm:hidden">
          <Footer />
        </div>
      )}
    </div>
  );
}


