'use client';

// BillsProvider and useBillsStore provide a unified interface for bill storage and management.
// - If the user is authenticated, bills are stored via secure API endpoints (cloud sync).
// - If the user is not authenticated (guest/offline), bills are stored in localStorage.
// All bill CRUD operations in the app should go through useBillsStore for consistency and offline/online support.
// This dual-mode logic enables seamless offline-first and cloud-synced experiences.

import { useAuth } from '@/hooks/useAuth';
import { auth } from '@/lib/firebase';
import { Bill, BillFormData, PaymentFrequency } from '@/types/bill';
import { suggestCategory, suggestTags } from '@/utils/billIntelligence';
import { createContext, ReactNode, useCallback, useContext, useEffect, useState } from 'react';
import { UserRule } from '@/types/rules';
import {
  BillRenewalOptions,
  calculateNextDueDate,
  DEFAULT_RENEWAL_OPTIONS,
  processAutomaticRenewal,
  RenewalResult
} from '@/utils/billRenewal';
import { useUserPreferences } from './userPreferencesStore';
import SuggestionToast from '@/components/ui/SuggestionToast';
import { v4 as uuidv4 } from 'uuid';

// Helper to get auth token
const getAuthToken = async () => {
  const user = auth.currentUser;
  if (user) {
    return await user.getIdToken();
  }
  return null;
};

// Helper for authenticated fetch
const authedFetch = async (url: string, options: RequestInit = {}) => {
  const token = await getAuthToken();
  const headers = {
    ...options.headers,
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
  return fetch(url, { ...options, headers });
};

interface BillsContextValue {
  bills: Bill[] | undefined;
  isLoading: boolean;
  error: string | null;
  addBill: (billData: Omit<BillFormData, 'id'>) => Promise<void>;
  updateBill: (id: string, billData: Partial<Bill>) => Promise<void>;
  deleteBill: (id: string) => Promise<void>;
  markBillPaid: (id: string) => Promise<void>;
  markBillUnpaid: (id: string) => Promise<void>;
  renewBill: (originalBillId: string, newDueDate: string, newAmount?: number, newNotes?: string) => Promise<Bill | undefined>;
  processAutomaticBillRenewal: (billId: string, options?: Partial<BillRenewalOptions>) => Promise<RenewalResult>;
  processBatchBillRenewal: (billIds?: string[], options?: Partial<BillRenewalOptions>) => Promise<{ results: RenewalResult[]; summary: { total: number; renewed: number; skipped: number; errors: number } }>;
  detectBillTypeForBill: (billId: string) => { pattern: any; confidence: number; suggestions: string[] } | null;
  calculateNextDueDateForBill: (billId: string, frequency?: PaymentFrequency) => string | null;
}

export const BillsContext = createContext<BillsContextValue>({
  bills: [],
  isLoading: false,
  error: null,
  addBill: async () => { },
  updateBill: async () => { },
  deleteBill: async () => { },
  markBillPaid: async () => { },
  markBillUnpaid: async () => { },
  renewBill: async () => undefined,
  processAutomaticBillRenewal: async () => ({ success: false, error: 'Not implemented' }),
  processBatchBillRenewal: async () => ({ results: [], summary: { total: 0, renewed: 0, skipped: 0, errors: 0 } }),
  detectBillTypeForBill: () => null,
  calculateNextDueDateForBill: () => null,
});

interface CategoryChangeTrack {
  category: string;
  count: number;
  lastBillIdProcessed: string;
}

interface RuleSuggestion {
  billName: string;
  suggestedCategory: string;
}

export function BillsProvider({ children }: { children: ReactNode }) {
  const [bills, setBills] = useState<Bill[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [categoryChangeTracker, setCategoryChangeTracker] = useState<Record<string, CategoryChangeTrack>>({});
  const [suggestedRule, setSuggestedRule] = useState<RuleSuggestion | null>(null);
  const { addRule } = useUserPreferences();

  // Local storage for offline mode
  const getLocalBills = useCallback(() => {
    try {
      const storedBills = localStorage.getItem('bills');
      return storedBills ? JSON.parse(storedBills) : [];
    } catch (error) {
      console.error('Error loading bills from localStorage:', error);
      return [];
    }
  }, []);

  const saveLocalBills = useCallback((updatedBills: Bill[]) => {
    try {
      localStorage.setItem('bills', JSON.stringify(updatedBills));
    } catch (error) {
      console.error('Error saving bills to localStorage:', error);
    }
  }, []);

  // Load category tracker from localStorage
  useEffect(() => {
    const storedTracker = localStorage.getItem('categoryChangeTracker');
    if (storedTracker) {
      setCategoryChangeTracker(JSON.parse(storedTracker));
    }
  }, []);

  // Save category tracker to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('categoryChangeTracker', JSON.stringify(categoryChangeTracker));
  }, [categoryChangeTracker]);

  // Drive userId from centralized auth store
  const { user } = useAuth();
  
  useEffect(() => {
    setUserId(user?.uid ?? null);
  }, [user]);

  const fetchBills = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (userId) {
        // User is authenticated, fetch from API
        const response = await authedFetch('/api/bills');
        if (!response.ok) {
          throw new Error('Failed to fetch bills');
        }
        const fetchedBills = await response.json();
        setBills(fetchedBills);
      } else {
        // User is not authenticated, use local storage
        const localBills = getLocalBills();
        setBills(localBills);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching bills:', err);
      
      // Fallback to local storage if API fails
      if (userId) {
        const localBills = getLocalBills();
        setBills(localBills);
      }
    } finally {
      setIsLoading(false);
    }
  }, [userId, getLocalBills]);

  // Fetch bills whenever userId changes
  useEffect(() => {
    fetchBills();
  }, [userId, fetchBills]);

  // Function to add a new bill
  const addBill = useCallback(async (billData: Omit<BillFormData, 'id'>) => {
    setIsLoading(true);
    setError(null);

    try {
      const now = new Date().toISOString();
      const newBill: Bill = {
        ...billData,
        id: uuidv4(), // Temporary ID for local storage
        userId: userId || 'guest',
        createdAt: now,
        updatedAt: now,
      };

      if (userId) {
        // User is authenticated, use API
        const response = await authedFetch('/api/bills', {
          method: 'POST',
          body: JSON.stringify(billData),
        });

        if (!response.ok) {
          throw new Error('Failed to add bill');
        }

        const addedBill = await response.json();
        setBills(prev => [...prev, addedBill]);
      } else {
        // User is not authenticated, use local storage
        const updatedBills = [...bills, newBill];
        setBills(updatedBills);
        saveLocalBills(updatedBills);
      }

      // Suggest category if applicable
      if (billData.name) {
        const suggestion = suggestCategory(billData.name);
        if (suggestion && suggestion !== billData.category) {
          // Track category changes for potential rule suggestions
          const key = billData.name.toLowerCase();
          const tracker = categoryChangeTracker[key] || { 
            category: suggestion, 
            count: 0,
            lastBillIdProcessed: newBill.id
          };
          
          setCategoryChangeTracker(prev => ({
            ...prev,
            [key]: {
              ...tracker,
              count: tracker.count + 1,
              lastBillIdProcessed: newBill.id
            }
          }));

          // If we've seen this pattern multiple times, suggest a rule
          if (tracker.count >= 2) {
            setSuggestedRule({
              billName: billData.name,
              suggestedCategory: suggestion
            });
          }
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error adding bill:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bills, userId, categoryChangeTracker, saveLocalBills]);

  // Function to update an existing bill
  const updateBill = useCallback(async (id: string, billData: Partial<Bill>) => {
    setIsLoading(true);
    setError(null);

    try {
      if (userId) {
        // User is authenticated, use API
        const response = await authedFetch(`/api/bills/${id}`, {
          method: 'PATCH',
          body: JSON.stringify(billData),
        });

        if (!response.ok) {
          throw new Error('Failed to update bill');
        }

        const updatedBill = await response.json();
        setBills(prev => prev.map(bill => bill.id === id ? updatedBill : bill));
      } else {
        // User is not authenticated, use local storage
        const updatedBills = bills.map(bill => {
          if (bill.id === id) {
            return { 
              ...bill, 
              ...billData, 
              updatedAt: new Date().toISOString() 
            };
          }
          return bill;
        });
        
        setBills(updatedBills);
        saveLocalBills(updatedBills);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error updating bill:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bills, userId, saveLocalBills]);

  // Function to delete a bill
  const deleteBill = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      if (userId) {
        // User is authenticated, use API
        const response = await authedFetch(`/api/bills/${id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to delete bill');
        }

        setBills(prev => prev.filter(bill => bill.id !== id));
      } else {
        // User is not authenticated, use local storage
        const updatedBills = bills.filter(bill => bill.id !== id);
        setBills(updatedBills);
        saveLocalBills(updatedBills);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error deleting bill:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bills, userId, saveLocalBills]);

  // Function to mark a bill as paid
  const markBillPaid = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const now = new Date().toISOString();
      const paidData = { 
        paidDate: now,
        updatedAt: now 
      };

      if (userId) {
        // User is authenticated, use API
        const response = await authedFetch(`/api/bills/${id}`, {
          method: 'PATCH',
          body: JSON.stringify(paidData),
        });

        if (!response.ok) {
          throw new Error('Failed to mark bill as paid');
        }

        const updatedBill = await response.json();
        setBills(prev => prev.map(bill => bill.id === id ? updatedBill : bill));
      } else {
        // User is not authenticated, use local storage
        const updatedBills = bills.map(bill => {
          if (bill.id === id) {
            return { ...bill, ...paidData };
          }
          return bill;
        });
        
        setBills(updatedBills);
        saveLocalBills(updatedBills);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error marking bill as paid:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bills, userId, saveLocalBills]);

  // Function to mark a bill as unpaid
  const markBillUnpaid = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const now = new Date().toISOString();
      const unpaidData = { 
        paidDate: undefined,
        updatedAt: now 
      };

      if (userId) {
        // User is authenticated, use API
        const response = await authedFetch(`/api/bills/${id}`, {
          method: 'PATCH',
          body: JSON.stringify(unpaidData),
        });

        if (!response.ok) {
          throw new Error('Failed to mark bill as unpaid');
        }

        const updatedBill = await response.json();
        setBills(prev => prev.map(bill => bill.id === id ? updatedBill : bill));
      } else {
        // User is not authenticated, use local storage
        const updatedBills = bills.map(bill => {
          if (bill.id === id) {
            const newBill = { ...bill, updatedAt: now };
            delete newBill.paidDate;
            return newBill;
          }
          return bill;
        });
        
        setBills(updatedBills);
        saveLocalBills(updatedBills);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error marking bill as unpaid:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bills, userId, saveLocalBills]);

  // Function to renew a bill
  const renewBill = useCallback(async (originalBillId: string, newDueDate: string, newAmount?: number, newNotes?: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const originalBill = bills.find(bill => bill.id === originalBillId);
      if (!originalBill) {
        throw new Error('Original bill not found');
      }

      const now = new Date().toISOString();
      const newBillData: Omit<BillFormData, 'id'> = {
        name: originalBill.name,
        amount: newAmount !== undefined ? newAmount : originalBill.amount,
        dueDate: newDueDate,
        category: originalBill.category,
        isRecurring: originalBill.isRecurring,
        recurringFrequency: originalBill.recurringFrequency,
        reminderDays: originalBill.reminderDays,
        notes: newNotes !== undefined ? newNotes : originalBill.notes,
        billType: originalBill.billType,
        relatedBillId: originalBillId
      };

      // Add the new bill
      if (userId) {
        // User is authenticated, use API
        const response = await authedFetch('/api/bills/renew', {
          method: 'POST',
          body: JSON.stringify({
            originalBillId,
            newDueDate,
            newAmount,
            newNotes
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to renew bill');
        }

        const renewedBill = await response.json();
        setBills(prev => [...prev, renewedBill]);
        return renewedBill;
      } else {
        // User is not authenticated, use local storage
        const newBill: Bill = {
          ...newBillData,
          id: uuidv4(),
          userId: 'guest',
          createdAt: now,
          updatedAt: now,
        };
        
        const updatedBills = [...bills, newBill];
        setBills(updatedBills);
        saveLocalBills(updatedBills);
        return newBill;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error renewing bill:', err);
      return undefined;
    } finally {
      setIsLoading(false);
    }
  }, [bills, userId, saveLocalBills]);

  // Function to process automatic bill renewal
  const processAutomaticBillRenewal = useCallback(async (billId: string, options?: Partial<BillRenewalOptions>): Promise<RenewalResult> => {
    try {
      const bill = bills.find(b => b.id === billId);
      if (!bill) {
        return { success: false, error: 'Bill not found', billId };
      }

      const renewalOptions: BillRenewalOptions = {
        ...DEFAULT_RENEWAL_OPTIONS,
        ...options
      };

      const result = processAutomaticRenewal(bill, renewalOptions);
      if (result.success && result.newDueDate) {
        const renewedBill = await renewBill(
          billId, 
          result.newDueDate, 
          result.newAmount, 
          result.newNotes
        );
        
        return {
          ...result,
          renewedBillId: renewedBill?.id
        };
      }
      
      return result;
    } catch (error) {
      console.error('Error in processAutomaticBillRenewal:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        billId 
      };
    }
  }, [bills, renewBill]);

  // Function to process batch bill renewal
  const processBatchBillRenewal = useCallback(async (billIds?: string[], options?: Partial<BillRenewalOptions>) => {
    const targetBillIds = billIds || bills.map(bill => bill.id);
    const results: RenewalResult[] = [];
    let renewed = 0;
    let skipped = 0;
    let errors = 0;
    
    for (const billId of targetBillIds) {
      const result = await processAutomaticBillRenewal(billId, options);
      results.push(result);
      
      if (result.success && result.renewedBillId) {
        renewed++;
      } else if (result.success) {
        skipped++;
      } else {
        errors++;
      }
    }
    
    return {
      results,
      summary: {
        total: targetBillIds.length,
        renewed,
        skipped,
        errors
      }
    };
  }, [bills, processAutomaticBillRenewal]);

  // Function to detect bill type for a bill
  const detectBillTypeForBill = useCallback((billId: string) => {
    const bill = bills.find(b => b.id === billId);
    if (!bill || !bill.name) {
      return null;
    }
    
    // Use the bill intelligence utility
    return suggestTags(bill.name);
  }, [bills]);

  // Function to calculate next due date for a bill
  const calculateNextDueDateForBill = useCallback((billId: string, frequency?: PaymentFrequency) => {
    const bill = bills.find(b => b.id === billId);
    if (!bill || !bill.dueDate) {
      return null;
    }
    
    return calculateNextDueDate(
      bill.dueDate,
      frequency || bill.recurringFrequency
    );
  }, [bills]);

  // Accept the rule suggestion and add it
  const acceptRuleSuggestion = useCallback(() => {
    if (!suggestedRule) return;
    
    const { billName, suggestedCategory } = suggestedRule;
    const newRule: UserRule = {
      id: uuidv4(),
      type: 'category',
      condition: {
        field: 'name',
        operator: 'contains',
        value: billName
      },
      action: {
        field: 'category',
        value: suggestedCategory
      },
      createdAt: new Date().toISOString(),
      active: true
    };
    
    addRule(newRule);
    setSuggestedRule(null);
  }, [suggestedRule, addRule]);

  // Dismiss the rule suggestion
  const dismissRuleSuggestion = useCallback(() => {
    setSuggestedRule(null);
  }, []);

  const contextValue = {
    bills,
    isLoading,
    error,
    addBill,
    updateBill,
    deleteBill,
    markBillPaid,
    markBillUnpaid,
    renewBill,
    processAutomaticBillRenewal,
    processBatchBillRenewal,
    detectBillTypeForBill,
    calculateNextDueDateForBill,
  };

  return (
    <BillsContext.Provider value={contextValue}>
      {children}
      {suggestedRule && (
        <SuggestionToast
          message={`Create a rule to always categorize "${suggestedRule.billName}" as "${suggestedRule.suggestedCategory}"?`}
          onAccept={acceptRuleSuggestion}
          onDismiss={dismissRuleSuggestion}
        />
      )}
    </BillsContext.Provider>
  );
}

export const useBillsStore = () => useContext(BillsContext);
