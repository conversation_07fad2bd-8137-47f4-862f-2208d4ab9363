'use client';

import { useLocalStorage } from '@/utils/useLocalStorage';
import { motion } from 'framer-motion';
import { useCallback, useEffect, useState } from 'react';

interface CalculationHistoryItem {
  id: string;
  expression: string;
  result: string;
  timestamp: number;
}

interface MemoryState {
  value: number;
  hasValue: boolean;
}

export function FinancialCalculator() {
  const [display, setDisplay] = useState('0');
  const [expression, setExpression] = useState('');
  const [history, setHistory] = useLocalStorage<CalculationHistoryItem[]>('financial_calculator_history', []);
  const [showHistory, setShowHistory] = useState(false);
  const [lastOperation, setLastOperation] = useState('');
  const [hasCalculated, setHasCalculated] = useState(false);
  const [memory, setMemory] = useState<MemoryState>({ value: 0, hasValue: false });
  const [pendingPercentage, setPendingPercentage] = useState(false);

  // Format currency display
  const formatCurrency = useCallback((value: string): string => {
    const num = parseFloat(value);
    if (isNaN(num)) return value;

    // For large numbers, show with commas
    if (Math.abs(num) >= 1000) {
      return num.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    }

    // For smaller numbers, show up to 2 decimal places
    return num.toFixed(2).replace(/\.?0+$/, '');
  }, []);

  // Handle number input
  const handleNumberPress = useCallback((num: string) => {
    if (display === '0' || hasCalculated || display === 'Error') {
      setDisplay(num);
      if (hasCalculated) {
        setExpression('');
        setHasCalculated(false);
      }
    } else {
      // Limit display length to prevent overflow
      if (display.length < 12) {
        setDisplay(prev => prev + num);
      }
    }
    setPendingPercentage(false);
  }, [display, hasCalculated]);

  // Handle operation input
  const handleOperationPress = useCallback((operation: string) => {
    setHasCalculated(false);
    setPendingPercentage(false);

    if (lastOperation && display === '0') {
      // Replace the last operation
      setExpression(prev => prev.slice(0, -2) + operation + ' ');
    } else {
      setExpression(prev => prev + display + ' ' + operation + ' ');
    }
    setDisplay('0');
    setLastOperation(operation);
  }, [display, lastOperation]);

  // Handle decimal point
  const handleDecimalPress = useCallback(() => {
    if (hasCalculated || display === 'Error') {
      setDisplay('0.');
      setExpression('');
      setHasCalculated(false);
    } else if (!display.includes('.')) {
      setDisplay(prev => prev + '.');
    }
    setPendingPercentage(false);
  }, [display, hasCalculated]);

  // Handle clear
  const handleClearPress = useCallback(() => {
    setDisplay('0');
    setExpression('');
    setLastOperation('');
    setHasCalculated(false);
    setPendingPercentage(false);
  }, []);

  // Handle backspace
  const handleBackspacePress = useCallback(() => {
    if (display.length > 1 && display !== 'Error') {
      setDisplay(prev => prev.slice(0, -1));
    } else {
      setDisplay('0');
    }
    setPendingPercentage(false);
  }, [display]);

  // Handle percentage calculations
  const handlePercentagePress = useCallback(() => {
    const currentValue = parseFloat(display);
    if (isNaN(currentValue)) return;

    if (expression && lastOperation) {
      // Calculate percentage of the base value
      const baseExpression = expression.slice(0, -2); // Remove operation
      const baseValue = parseFloat(baseExpression);

      if (!isNaN(baseValue)) {
        const percentageValue = (baseValue * currentValue) / 100;
        setDisplay(percentageValue.toString());
        setPendingPercentage(true);
      }
    } else {
      // Simple percentage (divide by 100)
      const result = currentValue / 100;
      setDisplay(result.toString());
      setHasCalculated(true);
    }
  }, [display, expression, lastOperation]);

  // Memory functions
  const handleMemoryAdd = useCallback(() => {
    const currentValue = parseFloat(display);
    if (!isNaN(currentValue)) {
      setMemory(prev => ({
        value: prev.value + currentValue,
        hasValue: true
      }));
    }
  }, [display]);

  const handleMemorySubtract = useCallback(() => {
    const currentValue = parseFloat(display);
    if (!isNaN(currentValue)) {
      setMemory(prev => ({
        value: prev.value - currentValue,
        hasValue: true
      }));
    }
  }, [display]);

  const handleMemoryRecall = useCallback(() => {
    if (memory.hasValue) {
      setDisplay(memory.value.toString());
      setHasCalculated(true);
    }
  }, [memory]);

  const handleMemoryClear = useCallback(() => {
    setMemory({ value: 0, hasValue: false });
  }, []);

  // Secure mathematical expression evaluator
  const evaluateExpression = useCallback((expr: string): number => {
    // Remove whitespace and validate characters
    const cleanExpr = expr.replace(/\s/g, '');

    // Only allow numbers, operators, parentheses, and decimal points
    if (!/^[0-9+\-*/.()]+$/.test(cleanExpr)) {
      throw new Error('Invalid characters in expression');
    }

    // Check for balanced parentheses
    let parenCount = 0;
    for (const char of cleanExpr) {
      if (char === '(') parenCount++;
      if (char === ')') parenCount--;
      if (parenCount < 0) throw new Error('Unbalanced parentheses');
    }
    if (parenCount !== 0) throw new Error('Unbalanced parentheses');

    // Parse and evaluate using a safe recursive descent parser
    let index = 0;

    const parseNumber = (): number => {
      let numStr = '';
      while (index < cleanExpr.length && /[0-9.]/.test(cleanExpr[index])) {
        numStr += cleanExpr[index++];
      }
      const num = parseFloat(numStr);
      if (isNaN(num)) throw new Error('Invalid number');
      return num;
    };

    const parseFactor = (): number => {
      if (index >= cleanExpr.length) throw new Error('Unexpected end of expression');

      if (cleanExpr[index] === '(') {
        index++; // skip '('
        const result = parseExpression();
        if (index >= cleanExpr.length || cleanExpr[index] !== ')') {
          throw new Error('Missing closing parenthesis');
        }
        index++; // skip ')'
        return result;
      }

      if (cleanExpr[index] === '-') {
        index++; // skip '-'
        return -parseFactor();
      }

      if (cleanExpr[index] === '+') {
        index++; // skip '+'
        return parseFactor();
      }

      return parseNumber();
    };

    const parseTerm = (): number => {
      let result = parseFactor();

      while (index < cleanExpr.length && (cleanExpr[index] === '*' || cleanExpr[index] === '/')) {
        const op = cleanExpr[index++];
        const right = parseFactor();

        if (op === '*') {
          result *= right;
        } else {
          if (right === 0) throw new Error('Division by zero');
          result /= right;
        }
      }

      return result;
    };

    const parseExpression = (): number => {
      let result = parseTerm();

      while (index < cleanExpr.length && (cleanExpr[index] === '+' || cleanExpr[index] === '-')) {
        const op = cleanExpr[index++];
        const right = parseTerm();

        if (op === '+') {
          result += right;
        } else {
          result -= right;
        }
      }

      return result;
    };

    const result = parseExpression();

    if (index < cleanExpr.length) {
      throw new Error('Unexpected characters at end of expression');
    }

    return result;
  }, []);

  // Handle equals/calculation
  const handleEqualsPress = useCallback(() => {
    try {
      const fullExpression = expression + display;

      if (!fullExpression.trim()) return;

      // Replace symbols for evaluation
      const evalExpression = fullExpression
        .replace(/×/g, '*')
        .replace(/÷/g, '/')
        .replace(/,/g, ''); // Remove commas from numbers

      // Evaluate the expression using secure parser
      const result = evaluateExpression(evalExpression);

      if (!isFinite(result)) {
        setDisplay('Error');
        setTimeout(() => setDisplay('0'), 2000);
        return;
      }

      const resultString = result.toString();

      // Add to history
      const historyItem: CalculationHistoryItem = {
        id: Date.now().toString(),
        expression: fullExpression,
        result: resultString,
        timestamp: Date.now(),
      };

      setHistory(prev => [historyItem, ...prev.slice(0, 49)]);

      setDisplay(resultString);
      setExpression('');
      setLastOperation('');
      setHasCalculated(true);
      setPendingPercentage(false);
    } catch (error) {
      console.error('Calculation error:', error);
      setDisplay('Error');
      setTimeout(() => setDisplay('0'), 2000);
    }
  }, [display, expression, setHistory, evaluateExpression]);

  // Copy to clipboard
  const handleCopyToClipboard = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(display);
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  }, [display]);

  // Keyboard support - optimized to prevent memory leaks
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      const { key } = event;

      // Prevent default for calculator keys
      if (/[0-9+\-*/.=]/.test(key) || key === 'Enter' || key === 'Backspace' || key === 'Escape') {
        event.preventDefault();
      }

      if (/[0-9]/.test(key)) {
        handleNumberPress(key);
      } else if (key === '+') {
        handleOperationPress('+');
      } else if (key === '-') {
        handleOperationPress('-');
      } else if (key === '*') {
        handleOperationPress('×');
      } else if (key === '/') {
        handleOperationPress('÷');
      } else if (key === '.' || key === ',') {
        handleDecimalPress();
      } else if (key === '=' || key === 'Enter') {
        handleEqualsPress();
      } else if (key === 'Backspace') {
        handleBackspacePress();
      } else if (key === 'Escape') {
        handleClearPress();
      } else if (key === '%') {
        handlePercentagePress();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [
    handleNumberPress,
    handleOperationPress,
    handleDecimalPress,
    handleEqualsPress,
    handleBackspacePress,
    handleClearPress,
    handlePercentagePress,
  ]);

  const handleHistoryItemPress = useCallback((item: CalculationHistoryItem) => {
    setDisplay(item.result);
    setExpression('');
    setHasCalculated(true);
    setShowHistory(false);
  }, []);

  const handleClearHistory = useCallback(() => {
    setHistory([]);
  }, [setHistory]);

  return (
    <div className="financial-calculator w-full max-w-md mx-auto p-4">
      <div className="calculator-container bg-gray-50 dark:bg-gray-900 rounded-2xl p-6 shadow-lg">
        {/* Display */}
        <div className="display-container bg-white dark:bg-gray-800 rounded-xl p-4 mb-6 shadow-sm">
          <div className="expression text-gray-500 dark:text-gray-400 text-sm min-h-5 mb-2 text-right">
            {expression || '\u00A0'}
          </div>
          <div className="result-container flex items-center justify-between">
            <div data-testid="calculator-display" className="result text-2xl font-bold text-gray-900 dark:text-gray-100 text-right flex-1 break-all">
              {formatCurrency(display)}
            </div>
            <button
              onClick={handleCopyToClipboard}
              className="copy-btn ml-3 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              title="Copy to clipboard"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>
          </div>
          {memory.hasValue && (
            <div className="memory-indicator text-xs text-blue-600 dark:text-blue-400 text-right mt-1">
              M: {formatCurrency(memory.value.toString())}
            </div>
          )}
        </div>

        {/* Button Grid */}
        <div className="buttons-grid grid grid-cols-4 gap-3">
          {/* Row 1: Memory and Clear functions */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleMemoryClear}
            className="btn memory-btn bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300 p-4 rounded-xl font-semibold text-sm transition-colors hover:bg-orange-200 dark:hover:bg-orange-800"
            disabled={!memory.hasValue}
          >
            MC
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleMemoryRecall}
            className="btn memory-btn bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300 p-4 rounded-xl font-semibold text-sm transition-colors hover:bg-orange-200 dark:hover:bg-orange-800"
            disabled={!memory.hasValue}
          >
            MR
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleMemoryAdd}
            className="btn memory-btn bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300 p-4 rounded-xl font-semibold text-sm transition-colors hover:bg-orange-200 dark:hover:bg-orange-800"
          >
            M+
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleMemorySubtract}
            className="btn memory-btn bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300 p-4 rounded-xl font-semibold text-sm transition-colors hover:bg-orange-200 dark:hover:bg-orange-800"
          >
            M-
          </motion.button>

          {/* Row 2: Clear, Backspace, Percentage, Division */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleClearPress}
            className="btn clear-btn bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 p-4 rounded-xl font-semibold transition-colors hover:bg-red-200 dark:hover:bg-red-800"
          >
            AC
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleBackspacePress}
            className="btn backspace-btn bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 p-4 rounded-xl font-semibold transition-colors hover:bg-gray-300 dark:hover:bg-gray-600"
          >
            <svg className="w-5 h-5 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
            </svg>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handlePercentagePress}
            className="btn percentage-btn bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 p-4 rounded-xl font-semibold transition-colors hover:bg-blue-200 dark:hover:bg-blue-800"
          >
            %
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleOperationPress('÷')}
            className="btn operation-btn bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-400 dark:hover:bg-gray-500"
          >
            ÷
          </motion.button>

          {/* Row 3: 7, 8, 9, × */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('7')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            7
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('8')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            8
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('9')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            9
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleOperationPress('×')}
            className="btn operation-btn bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-400 dark:hover:bg-gray-500"
          >
            ×
          </motion.button>

          {/* Row 4: 4, 5, 6, - */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('4')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            4
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('5')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            5
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('6')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            6
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleOperationPress('-')}
            className="btn operation-btn bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-400 dark:hover:bg-gray-500"
          >
            -
          </motion.button>

          {/* Row 5: 1, 2, 3, + */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('1')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            1
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('2')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            2
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('3')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            3
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleOperationPress('+')}
            className="btn operation-btn bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-400 dark:hover:bg-gray-500"
          >
            +
          </motion.button>

          {/* Row 6: 0, ., =, History */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleNumberPress('0')}
            className="btn number-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm col-span-2"
          >
            0
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleDecimalPress}
            className="btn decimal-btn bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm"
          >
            .
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleEqualsPress}
            className="btn equals-btn bg-green-500 dark:bg-green-600 text-white p-4 rounded-xl font-semibold text-xl transition-colors hover:bg-green-600 dark:hover:bg-green-700"
          >
            =
          </motion.button>
        </div>

        {/* History Toggle Button */}
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowHistory(!showHistory)}
          className="history-toggle-btn w-full mt-4 p-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium transition-colors hover:bg-gray-300 dark:hover:bg-gray-600 flex items-center justify-center gap-2"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {showHistory ? 'Hide History' : `Show History ${history.length > 0 ? `(${history.length})` : ''}`}
        </motion.button>
      </div>

      {/* History Panel */}
      {showHistory && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          className="history-panel mt-4 bg-white dark:bg-gray-800 rounded-2xl p-4 shadow-lg max-h-64 overflow-y-auto"
        >
          <div className="history-header flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Calculation History
            </h3>
            {history.length > 0 && (
              <button
                onClick={handleClearHistory}
                className="clear-history-btn text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium transition-colors"
              >
                Clear All
              </button>
            )}
          </div>

          <div className="history-list space-y-2">
            {history.length > 0 ? (
              history.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  onClick={() => handleHistoryItemPress(item)}
                  className="history-item p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors group"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="expression text-gray-600 dark:text-gray-400 text-sm truncate">
                        {item.expression}
                      </div>
                      <div className="result text-gray-900 dark:text-gray-100 font-semibold">
                        = {formatCurrency(item.result)}
                      </div>
                    </div>
                    <div className="timestamp text-xs text-gray-500 dark:text-gray-400 ml-2 flex-shrink-0">
                      {new Date(item.timestamp).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="no-history text-center text-gray-500 dark:text-gray-400 py-8">
                <svg className="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <p className="text-sm">No calculations yet</p>
                <p className="text-xs mt-1">Your calculation history will appear here</p>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
}
