const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Create the icons directory if it doesn't exist
const iconsDir = path.join(__dirname, 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Define the icon sizes needed for PWA
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Generate PNG icons from the SVG for each size
sizes.forEach(size => {
  try {
    // Create a simple colored square with text as a placeholder icon
    const svgContent = `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 ${size} ${size}">
      <rect width="${size}" height="${size}" fill="#4f46e5" rx="${size/4}" ry="${size/4}"/>
      <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="${size/3}" font-weight="bold" fill="#ffffff">PDP</text>
    </svg>`;
    
    const tempSvgPath = path.join(iconsDir, `temp-${size}.svg`);
    fs.writeFileSync(tempSvgPath, svgContent);
    
    // Use svg-app-icon to convert SVG to PNG
    const outputPath = path.join(iconsDir, `icon-${size}x${size}.png`);
    
    // For simplicity, we'll use a different approach since svg-app-icon might not be available
    // This is a simple placeholder approach - in a real app, you'd want to use proper image conversion
    fs.copyFileSync(tempSvgPath, outputPath.replace('.png', '.svg'));
    
    console.log(`Created icon: ${outputPath.replace('.png', '.svg')}`);
    
    // Clean up temp file
    fs.unlinkSync(tempSvgPath);
  } catch (error) {
    console.error(`Error generating icon size ${size}:`, error);
  }
});

console.log('Icon generation complete!');
