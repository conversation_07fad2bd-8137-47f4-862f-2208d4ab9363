# Intelligent Bill Renewal System

## Overview

The Intelligent Bill Renewal System is a comprehensive solution that automatically generates new bill instances for recurring bills with smart due date calculation, bill type detection, and seamless integration with the existing dashboard UI system.

## Features

### 1. Automatic Bill Renewal Logic
- **Smart Due Date Calculation**: Handles monthly, quarterly, annual, weekly, and bi-weekly frequencies
- **Edge Case Handling**: <PERSON><PERSON><PERSON> manages month-end dates (Jan 31 → Feb 28/29), leap years, and February 29th scenarios
- **Business Day Adjustment**: Optional feature to move weekend due dates to the next Monday

### 2. Bill Type Detection
- **Pattern Recognition**: Automatically identifies bill types based on name and category keywords
- **Confidence Scoring**: Provides confidence levels for bill type detection
- **Frequency Suggestions**: Recommends appropriate payment frequencies based on bill type

### 3. Smart Integration Points
- **Add Bills Modal**: Enhanced with recurring bill controls and intelligent suggestions
- **Bills Overview**: Shows upcoming renewed bills with renewal management controls
- **Calendar Integration**: Displays future bill instances with special styling
- **Notification System**: Alerts for both current and upcoming bills

### 4. User Control
- **Recurring Bill Settings**: Users can mark bills as recurring/non-recurring
- **Renewal Preferences**: Configurable settings for advance generation and auto-detection
- **Manual Override**: Option to modify or skip specific renewal cycles

## Implementation Details

### Core Files

#### `src/utils/billRenewal.ts`
The main utility file containing:
- `calculateNextDueDate()`: Smart due date calculation with edge case handling
- `detectBillType()`: Bill type pattern matching
- `processAutomaticRenewal()`: Single bill renewal processing
- `processBatchRenewal()`: Batch renewal processing
- `BILL_TYPE_PATTERNS`: Comprehensive bill type definitions

#### `src/stores/billsStore.tsx`
Enhanced with renewal functions:
- `processAutomaticBillRenewal()`: Process single bill renewal
- `processBatchBillRenewal()`: Process multiple bill renewals
- `detectBillTypeForBill()`: Get bill type suggestions for existing bills
- `calculateNextDueDateForBill()`: Calculate next due date for existing bills

#### `src/components/bills/BillForm.tsx`
Enhanced with:
- Recurring bill controls (checkbox, frequency selector, reminder days)
- Intelligent bill type detection in AI hints
- Auto-suggestions for recurring status and frequency

#### `src/components/bills/BillsOverview.tsx`
Enhanced with:
- Batch renewal controls
- Individual bill renewal buttons
- Selection checkboxes for bulk operations
- Renewal status indicators

#### `src/components/calendar/BillCalendar.tsx`
Enhanced with:
- Future bill instance generation
- Special styling for projected bills
- Calendar integration for upcoming renewals

#### `src/components/settings/BillRenewalSettings.tsx`
New settings component for:
- Advance generation preferences
- Auto-detection settings
- Business day adjustment options

### Bill Type Patterns

The system recognizes these bill types:

1. **Utilities**: Electric, gas, water, power, energy (Monthly)
2. **Housing**: Rent, lease, mortgage (Monthly)
3. **Insurance**: All insurance types (Monthly)
4. **Subscriptions**: Netflix, Spotify, streaming services (Monthly)
5. **Communication**: Phone, internet, mobile (Monthly)
6. **Credit Cards**: All card payments (Monthly)
7. **Loans**: Auto loans, student loans (Monthly)
8. **Quarterly**: Tax payments, quarterly bills (Quarterly)
9. **Annual**: Registration, licenses, memberships (Annually)

### Smart Due Date Calculation

#### Monthly Bills
- Handles month-end dates: Jan 31 → Feb 28/29
- Respects leap years
- Maintains day-of-month consistency when possible

#### Quarterly Bills
- Adds 3 months to current due date
- Handles month-end edge cases
- Maintains quarterly schedule

#### Annual Bills
- Adds 1 year to current due date
- Handles February 29th on non-leap years
- Maintains annual schedule

#### Weekly/Bi-weekly Bills
- Simple date arithmetic
- Optional business day adjustment

### User Interface Enhancements

#### Add Bills Modal
- **Recurring Bill Section**: Checkbox to mark bills as recurring
- **Frequency Selector**: Dropdown with all supported frequencies
- **Reminder Days**: Configurable reminder period
- **AI Hints**: Intelligent suggestions based on bill name and category

#### Bills Overview
- **Selection Checkboxes**: Bulk selection for batch operations
- **Renewal Buttons**: Individual renewal buttons for recurring bills
- **Batch Controls**: Process multiple renewals simultaneously
- **Status Indicators**: Visual indicators for renewed bills

#### Calendar Integration
- **Future Instances**: Shows projected bill occurrences
- **Special Styling**: Purple dashed borders for future bills
- **Projected Labels**: Clear indication of projected vs. actual bills

#### Settings Panel
- **Advance Generation**: Control how far ahead to generate bills
- **Auto-Detection**: Enable/disable automatic bill type detection
- **Business Days**: Option to respect business days
- **One-Time Bills**: Control whether to skip non-recurring bills

## Usage Examples

### Basic Renewal
```typescript
// Renew a single bill
const result = await processAutomaticBillRenewal(billId);
if (result.success) {
  console.log(`Bill renewed for ${result.renewedBill?.dueDate}`);
}
```

### Batch Renewal
```typescript
// Renew multiple bills
const results = await processBatchBillRenewal([billId1, billId2]);
console.log(`Renewed ${results.summary.renewed} bills`);
```

### Bill Type Detection
```typescript
// Detect bill type with confidence
const detection = detectBillTypeWithConfidence('Electric Bill', 'Utilities', 150);
if (detection.confidence > 70) {
  console.log(`Detected as ${detection.pattern?.category} with ${detection.confidence}% confidence`);
}
```

### Custom Renewal Options
```typescript
const options: BillRenewalOptions = {
  advanceMonths: 2,
  respectBusinessDays: true,
  autoDetectRecurring: true,
  skipOneTime: true
};

const result = await processAutomaticBillRenewal(billId, options);
```

## Testing

Comprehensive test suite in `src/utils/__tests__/billRenewal.test.ts` covers:
- Date calculation edge cases
- Bill type detection accuracy
- Confidence scoring
- Renewal logic
- Batch processing
- Error handling

Run tests with:
```bash
npm test src/utils/__tests__/billRenewal.test.ts
```

## Configuration

### Default Settings
```typescript
export const DEFAULT_RENEWAL_OPTIONS: BillRenewalOptions = {
  advanceMonths: 1,
  respectBusinessDays: false,
  autoDetectRecurring: true,
  skipOneTime: true
};
```

### Customization
Users can customize renewal behavior through the settings panel or by passing options to renewal functions.

## Future Enhancements

1. **Machine Learning**: Improve bill type detection with usage patterns
2. **Smart Scheduling**: Optimize due dates based on cash flow
3. **Integration**: Connect with bank APIs for automatic payment detection
4. **Analytics**: Provide insights on spending patterns and bill trends
5. **Notifications**: Enhanced notification system with customizable alerts

## Troubleshooting

### Common Issues
1. **Date Calculation Errors**: Check date format and timezone settings
2. **Type Detection Failures**: Verify bill name contains recognizable keywords
3. **Renewal Skipping**: Check if bill is marked as paid and recurring
4. **Calendar Display**: Ensure future bill generation is enabled

### Debug Mode
Enable debug logging by setting `DEBUG_BILL_RENEWAL=true` in environment variables.
