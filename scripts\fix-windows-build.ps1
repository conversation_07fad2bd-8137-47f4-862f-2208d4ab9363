# PowerShell script to fix Windows EPERM build errors for Next.js
# Run as Administrator for best results

Write-Host "🔧 Windows Next.js Build Fix" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan
Write-Host ""

# Function to safely remove items
function Remove-ItemSafely {
    param([string]$Path)
    
    if (Test-Path $Path) {
        Write-Host "🗑️  Removing $Path..." -ForegroundColor Yellow
        try {
            # Remove read-only attributes first
            Get-ChildItem $Path -Recurse -Force | ForEach-Object {
                $_.Attributes = $_.Attributes -band (-bnot [System.IO.FileAttributes]::ReadOnly)
            }
            
            Remove-Item $Path -Recurse -Force -ErrorAction Stop
            Write-Host "✅ Removed $Path" -ForegroundColor Green
            return $true
        }
        catch {
            Write-Host "⚠️  Could not remove $Path`: $($_.Exception.Message)" -ForegroundColor Red
            
            # Try alternative removal
            try {
                Write-Host "🔄 Trying alternative removal..." -ForegroundColor Yellow
                if (Test-Path $Path -PathType Container) {
                    cmd /c "rmdir /s /q `"$Path`""
                } else {
                    cmd /c "del /f /q `"$Path`""
                }
                Write-Host "✅ Alternative removal succeeded" -ForegroundColor Green
                return $true
            }
            catch {
                Write-Host "❌ All removal methods failed" -ForegroundColor Red
                return $false
            }
        }
    }
    return $true
}

# Function to fix OneDrive issues
function Fix-OneDriveSync {
    Write-Host "🔄 Checking OneDrive sync status..." -ForegroundColor Yellow
    
    $currentDir = Get-Location
    if ($currentDir.Path -like "*OneDrive*") {
        Write-Host "📁 Detected OneDrive folder - applying OneDrive-specific fixes..." -ForegroundColor Yellow
        
        # Stop OneDrive process
        try {
            Write-Host "⏸️  Stopping OneDrive sync..." -ForegroundColor Yellow
            Get-Process OneDrive -ErrorAction SilentlyContinue | Stop-Process -Force
            Write-Host "✅ OneDrive process stopped" -ForegroundColor Green
            Start-Sleep -Seconds 2
            return $true
        }
        catch {
            Write-Host "ℹ️  OneDrive process not running or couldn't be stopped" -ForegroundColor Blue
        }
    }
    return $false
}

# Function to fix permissions
function Fix-Permissions {
    param([string]$Path)
    
    Write-Host "🔐 Fixing permissions for $Path..." -ForegroundColor Yellow
    
    try {
        # Remove read-only attributes
        cmd /c "attrib -R `"$Path`" /S /D" 2>$null
        
        # Take ownership (requires admin)
        try {
            cmd /c "takeown /f `"$Path`" /r /d y" 2>$null
            Write-Host "✅ Took ownership of $Path" -ForegroundColor Green
        }
        catch {
            Write-Host "ℹ️  Could not take ownership (may require admin rights)" -ForegroundColor Blue
        }
        
        # Grant full control
        try {
            $username = $env:USERNAME
            cmd /c "icacls `"$Path`" /grant `"$username`":F /T" 2>$null
            Write-Host "✅ Granted full control to $username" -ForegroundColor Green
        }
        catch {
            Write-Host "ℹ️  Could not modify ACL" -ForegroundColor Blue
        }
        
        return $true
    }
    catch {
        Write-Host "⚠️  Permission fix failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
try {
    # Check if running as administrator
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    if (-not $isAdmin) {
        Write-Host "⚠️  Not running as Administrator - some operations may fail" -ForegroundColor Yellow
        Write-Host "💡 For best results, run PowerShell as Administrator" -ForegroundColor Blue
        Write-Host ""
    }
    
    # Step 1: Fix OneDrive issues
    $isOneDrive = Fix-OneDriveSync
    
    # Step 2: Clean build artifacts
    Write-Host ""
    Write-Host "📁 Cleaning build artifacts..." -ForegroundColor Cyan
    $itemsToRemove = @(".next", ".turbo", "node_modules\.cache", "build-out")
    
    foreach ($item in $itemsToRemove) {
        Remove-ItemSafely $item
    }
    
    # Step 3: Fix permissions
    Write-Host ""
    Write-Host "🔐 Fixing directory permissions..." -ForegroundColor Cyan
    Fix-Permissions "."
    
    # Step 4: Clear npm cache
    Write-Host ""
    Write-Host "🧹 Clearing npm cache..." -ForegroundColor Cyan
    try {
        npm cache clean --force
        Write-Host "✅ npm cache cleared" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  Could not clear npm cache: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Step 5: Reinstall dependencies
    Write-Host ""
    Write-Host "📦 Reinstalling dependencies..." -ForegroundColor Cyan
    try {
        npm install
        Write-Host "✅ Dependencies reinstalled" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to reinstall dependencies: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
    
    # Step 6: Restart OneDrive
    if ($isOneDrive) {
        Write-Host ""
        Write-Host "🔄 Restarting OneDrive..." -ForegroundColor Cyan
        try {
            Start-Process "$env:LOCALAPPDATA\Microsoft\OneDrive\OneDrive.exe"
            Write-Host "✅ OneDrive restarted" -ForegroundColor Green
        }
        catch {
            Write-Host "ℹ️  Could not restart OneDrive automatically - please restart manually" -ForegroundColor Blue
        }
    }
    
    Write-Host ""
    Write-Host "🎉 Windows build fix completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📝 Next steps:" -ForegroundColor Cyan
    Write-Host "   1. Wait 30 seconds for OneDrive to sync" -ForegroundColor White
    Write-Host "   2. Run: npm run build" -ForegroundColor White
    Write-Host "   3. If issues persist, run this script as Administrator" -ForegroundColor White
}
catch {
    Write-Host ""
    Write-Host "💥 Build fix failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Manual steps to try:" -ForegroundColor Cyan
    Write-Host "   1. Run PowerShell as Administrator" -ForegroundColor White
    Write-Host "   2. Move project outside OneDrive folder" -ForegroundColor White
    Write-Host "   3. Disable OneDrive sync for this folder" -ForegroundColor White
    Write-Host "   4. Use WSL2 for development instead" -ForegroundColor White
    exit 1
}
