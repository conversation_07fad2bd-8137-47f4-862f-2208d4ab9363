'use client';

import React, { useState, useEffect } from 'react';
import { BILL_CATEGORIES } from '@/types/bill'; // Assuming categories are needed for dropdown
import { UserRule } from '@/types/rules'; // Import UserRule
import { loadRulesFromStorage, saveRulesToStorage } from '@/utils/storage'; // Import storage functions

const DEFAULT_NEW_RULE: Omit<UserRule, 'id'> = {
  condition: { field: 'name', operator: 'contains', value: '' },
  action: { type: 'setCategory', value: '' },
  priority: 0,
};

export function RuleManager() {
  const [rules, setRules] = useState<UserRule[]>([]);
  const [newRule, setNewRule] = useState<Omit<UserRule, 'id'>>(DEFAULT_NEW_RULE);
  const [isAdding, setIsAdding] = useState(false); // To show/hide add form

  useEffect(() => {
    const loadedRules = loadRulesFromStorage();
    setRules(loadedRules);
  }, []);

  const updateAndSaveRules = (updatedRules: UserRule[]) => {
    setRules(updatedRules);
    saveRulesToStorage(updatedRules);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    part: 'condition' | 'action',
    field: keyof UserRule['condition'] | keyof UserRule['action'] | 'priority'
  ) => {
    const { value } = e.target;
    setNewRule(prev => {
      if (field === 'priority') {
        return { ...prev, priority: parseInt(value, 10) || 0 };
      }
      if (part === 'condition' || part === 'action') {
        const key = field as keyof UserRule[typeof part];
        return {
          ...prev,
          [part]: {
            ...prev[part],
            [key]: value,
          },
        };
      }
      return prev;
    });

    if (part === 'action' && field === 'type') {
      setNewRule(prev => ({
        ...prev,
        action: { ...prev.action, value: '' }
      }));
    }
  };

  const handleAddRule = () => {
    if (!newRule.condition.value || !newRule.action.value) {
      alert('Please fill in condition and action values.');
      return;
    }
    if (newRule.action.type === 'setCategory' && !BILL_CATEGORIES.includes(newRule.action.value)) {
      alert(`Invalid category: ${newRule.action.value}. Please select a valid category.`);
      return;
    }

    const ruleToAdd: UserRule = {
      ...newRule,
      id: Date.now().toString(),
    };
    const updatedRules = [...rules, ruleToAdd];
    updateAndSaveRules(updatedRules);
    setNewRule(DEFAULT_NEW_RULE);
    setIsAdding(false);
  };

  const handleDeleteRule = (id: string) => {
    const updatedRules = rules.filter(rule => rule.id !== id);
    updateAndSaveRules(updatedRules);
  };

  return (
    <div className="p-4 md:p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Automation Rules</h2>

      <div className="mb-6 space-y-3">
        {rules.length === 0 && <p className="text-gray-600 dark:text-gray-400">No rules defined yet.</p>}
        {rules.map((rule) => (
          <div key={rule.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
            <span className="text-sm text-gray-700 dark:text-gray-300">
              IF <strong>{rule.condition.field}</strong> {rule.condition.operator} &quot;<em>{rule.condition.value}</em>&quot; THEN <strong>{rule.action.type === 'setCategory' ? 'Set Category' : 'Add Tag'}</strong> to &quot;<em>{rule.action.value}</em>&quot; {rule.priority ? `(Prio: ${rule.priority})` : ''}
            </span>
            <button
              onClick={() => handleDeleteRule(rule.id)}
              className="text-red-500 hover:text-red-700 text-sm font-medium"
              aria-label={`Delete rule ${rule.id}`}
            >
              Delete
            </button>
          </div>
        ))}
      </div>

      {!isAdding && (
        <button
          onClick={() => setIsAdding(true)}
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark text-sm font-medium"
        >
          + Add New Rule
        </button>
      )}

      {isAdding && (
        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-700">
          <h3 className="text-lg font-medium mb-3 text-gray-800 dark:text-gray-200">Create New Rule</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">IF Field</label>
              <select
                value={newRule.condition.field}
                onChange={(e) => handleInputChange(e, 'condition', 'field')}
                className="w-full text-sm p-2 border rounded dark:bg-gray-600 dark:border-gray-500 dark:text-white"
              >
                <option value="name">Name</option>
                <option value="description">Description</option>
              </select>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Operator</label>
              <select
                value={newRule.condition.operator}
                onChange={(e) => handleInputChange(e, 'condition', 'operator')}
                className="w-full text-sm p-2 border rounded dark:bg-gray-600 dark:border-gray-500 dark:text-white"
              >
                <option value="contains">Contains</option>
                <option value="equals">Equals</option>
                <option value="startsWith">Starts With</option>
                <option value="endsWith">Ends With</option>
              </select>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Value (text)</label>
              <input
                type="text"
                value={newRule.condition.value}
                onChange={(e) => handleInputChange(e, 'condition', 'value')}
                placeholder="e.g., Netflix"
                className="w-full text-sm p-2 border rounded dark:bg-gray-600 dark:border-gray-500 dark:text-white"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">THEN Action</label>
              <select
                value={newRule.action.type}
                onChange={(e) => handleInputChange(e, 'action', 'type')}
                className="w-full text-sm p-2 border rounded dark:bg-gray-600 dark:border-gray-500 dark:text-white"
              >
                <option value="setCategory">Set Category</option>
                <option value="addTag">Add Tag</option>
              </select>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">To Value</label>
              {newRule.action.type === 'setCategory' ? (
                <select
                  value={newRule.action.value}
                  onChange={(e) => handleInputChange(e, 'action', 'value')}
                  className="w-full text-sm p-2 border rounded dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                >
                  <option value="">-- Select Category --</option>
                  {BILL_CATEGORIES.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                </select>
              ) : (
                <input
                  type="text"
                  value={newRule.action.value}
                  onChange={(e) => handleInputChange(e, 'action', 'value')}
                  placeholder="e.g., subscription"
                  className="w-full text-sm p-2 border rounded dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                />
              )}
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Priority (Optional)</label>
              <input
                type="number"
                value={newRule.priority}
                onChange={(e) => handleInputChange(e, 'condition', 'priority')} 
                placeholder="0"
                className="w-full text-sm p-2 border rounded dark:bg-gray-600 dark:border-gray-500 dark:text-white"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => { setIsAdding(false); setNewRule(DEFAULT_NEW_RULE); }}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-500 text-gray-800 dark:text-gray-100 rounded hover:bg-gray-300 dark:hover:bg-gray-600 text-sm font-medium"
            >
              Cancel
            </button>
            <button
              onClick={handleAddRule}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark text-sm font-medium"
            >
              Save Rule
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
