#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Starting enhanced build fix process...');

// Function to safely remove directory with Windows-specific handling
function removeDir(dirPath) {
  try {
    if (fs.existsSync(dirPath)) {
      console.log(`🗑️  Removing ${dirPath}...`);

      // Windows-specific: Remove read-only attributes first
      if (process.platform === 'win32') {
        try {
          execSync(`attrib -R "${dirPath}\\*.*" /S /D`, { stdio: 'pipe' });
        } catch (e) {
          // Ignore if attrib fails
        }
      }

      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ Removed ${dirPath}`);
    }
  } catch (error) {
    console.warn(`⚠️  Could not remove ${dirPath}:`, error.message);

    // Try Windows-specific removal as fallback
    if (process.platform === 'win32') {
      try {
        console.log(`🔄 Trying Windows-specific removal for ${dirPath}...`);
        execSync(`rmdir /s /q "${dirPath}"`, { stdio: 'pipe' });
        console.log(`✅ Windows removal succeeded for ${dirPath}`);
      } catch (winError) {
        console.error(`❌ All removal methods failed for ${dirPath}`);
      }
    }
  }
}

// Function to run command safely
function runCommand(command, description) {
  try {
    console.log(`🔄 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    throw error;
  }
}

try {
  // Step 1: Clean build artifacts
  console.log('\n📁 Cleaning build artifacts...');
  removeDir('.next');
  removeDir('.turbo');
  removeDir('node_modules/.cache');

  // Step 2: Clear npm cache
  console.log('\n🧹 Clearing npm cache...');
  try {
    execSync('npm cache clean --force', { stdio: 'inherit' });
  } catch (error) {
    console.warn('⚠️  Could not clear npm cache:', error.message);
  }

  // Step 3: Install dependencies
  console.log('\n📦 Installing dependencies...');
  runCommand('npm install', 'Installing dependencies');

  // Step 4: Verify critical dependencies
  console.log('\n🔍 Verifying critical dependencies...');
  const criticalDeps = ['next', 'react', 'react-dom', 'critters'];

  for (const dep of criticalDeps) {
    try {
      require.resolve(dep);
      console.log(`✅ ${dep} is available`);
    } catch (error) {
      console.error(`❌ ${dep} is missing`);
      throw new Error(`Critical dependency ${dep} is missing`);
    }
  }

  console.log('\n🎉 Build fix process completed successfully!');
  console.log('\n📝 Next steps:');
  console.log('   1. Run: npm run dev');
  console.log('   2. Check for any remaining errors');
  console.log('   3. Clear browser cache if needed');

} catch (error) {
  console.error('\n💥 Build fix process failed:', error.message);
  console.log('\n🔧 Manual steps to try:');
  console.log('   1. Delete node_modules and package-lock.json');
  console.log('   2. Run: npm install');
  console.log('   3. Run: npm run dev:clean');
  process.exit(1);
}
