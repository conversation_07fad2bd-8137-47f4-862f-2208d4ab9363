'use client';

import { useState, useCallback } from 'react';
import { useLocalStorage } from '@/utils/useLocalStorage';

interface CalculationHistoryItem {
  id: string;
  expression: string;
  result: string;
  timestamp: number;
}

export default function CalculatorPage() {
  const [display, setDisplay] = useState('0');
  const [expression, setExpression] = useState('');
  const [history, setHistory] = useLocalStorage<CalculationHistoryItem[]>('calculator_history', []);
  const [showHistory, setShowHistory] = useState(false);
  const [lastOperation, setLastOperation] = useState('');
  const [hasCalculated, setHasCalculated] = useState(false);

  const handleNumberPress = useCallback((num: string) => {
    if (display === '0' || hasCalculated) {
      setDisplay(num);
      if (hasCalculated) {
        setExpression('');
        setHasCalculated(false);
      }
    } else {
      setDisplay(prev => prev + num);
    }
  }, [display, hasCalculated]);

  const handleOperationPress = useCallback((operation: string) => {
    setHasCalculated(false);
    
    if (lastOperation && display === '0') {
      // Replace the last operation
      setExpression(prev => prev.slice(0, -2) + operation + ' ');
    } else {
      setExpression(prev => prev + display + ' ' + operation + ' ');
    }
    setDisplay('0');
    setLastOperation(operation);
  }, [display, lastOperation]);

  const handleDecimalPress = useCallback(() => {
    if (hasCalculated) {
      setDisplay('0.');
      setExpression('');
      setHasCalculated(false);
    } else if (!display.includes('.')) {
      setDisplay(prev => prev + '.');
    }
  }, [display, hasCalculated]);

  const handleClearPress = useCallback(() => {
    setDisplay('0');
    setExpression('');
    setLastOperation('');
    setHasCalculated(false);
  }, []);

  const handleBackspacePress = useCallback(() => {
    if (display.length > 1) {
      setDisplay(prev => prev.slice(0, -1));
    } else {
      setDisplay('0');
    }
  }, [display]);

  const handleEqualsPress = useCallback(() => {
    try {
      const fullExpression = expression + display;
      
      // Replace × with * and ÷ with / for evaluation
      const evalExpression = fullExpression.replace(/×/g, '*').replace(/÷/g, '/');
      
      // Evaluate the expression
      const result = eval(evalExpression).toString();
      
      // Add to history
      const historyItem: CalculationHistoryItem = {
        id: Date.now().toString(),
        expression: fullExpression,
        result,
        timestamp: Date.now(),
      };
      
      setHistory(prev => [historyItem, ...prev.slice(0, 49)]); // Keep only the last 50 items
      
      setDisplay(result);
      setExpression('');
      setLastOperation('');
      setHasCalculated(true);
    } catch {
      setDisplay('Error');
      setTimeout(() => {
        setDisplay('0');
      }, 1000);
    }
  }, [display, expression, setHistory]);

  const handleHistoryItemPress = useCallback((item: CalculationHistoryItem) => {
    setDisplay(item.result);
    setExpression('');
    setHasCalculated(true);
    setShowHistory(false);
  }, []);

  const handleClearHistory = useCallback(() => {
    setHistory([]);
  }, [setHistory]);

  return (
    <div className="max-w-md mx-auto">
      <div className="calculator flex flex-col h-full relative">
        <div className="display bg-white dark:bg-gray-800 p-5 rounded-xl shadow-sm mb-5 text-right">
          <div className="expression text-gray-500 dark:text-gray-400 text-sm min-h-6 mb-2">
            {expression || '\u00A0'}
          </div>
          <div className="result text-3xl font-bold">{display}</div>
        </div>

        <div className="btn-container flex flex-col gap-3">
          <div className="row flex gap-3">
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600 font-medium"
              onClick={handleClearPress}
            >
              AC
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={handleBackspacePress}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
              </svg>
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-300 dark:hover:bg-gray-500"
              onClick={() => handleOperationPress('÷')}
            >
              ÷
            </button>
          </div>
          
          <div className="row flex gap-3">
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('7')}
            >
              7
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('8')}
            >
              8
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('9')}
            >
              9
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-300 dark:hover:bg-gray-500"
              onClick={() => handleOperationPress('×')}
            >
              ×
            </button>
          </div>
          
          <div className="row flex gap-3">
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('4')}
            >
              4
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('5')}
            >
              5
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('6')}
            >
              6
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-300 dark:hover:bg-gray-500"
              onClick={() => handleOperationPress('-')}
            >
              -
            </button>
          </div>
          
          <div className="row flex gap-3">
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('1')}
            >
              1
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('2')}
            >
              2
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('3')}
            >
              3
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-300 dark:hover:bg-gray-500"
              onClick={() => handleOperationPress('+')}
            >
              +
            </button>
          </div>
          
          <div className="row flex gap-3">
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => handleNumberPress('0')}
            >
              0
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xl flex items-center justify-center transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={handleDecimalPress}
            >
              .
            </button>
            <button 
              className="btn flex-1 p-5 border-none rounded-full bg-primary text-white text-xl flex items-center justify-center transition-colors hover:bg-primary-dark"
              onClick={handleEqualsPress}
            >
              =
            </button>
          </div>
        </div>

        <button 
          className="history-btn absolute top-2 right-2 p-2 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center cursor-pointer"
          onClick={() => setShowHistory(!showHistory)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>

        {showHistory && (
          <div className="history-panel absolute inset-0 bg-white dark:bg-gray-800 z-10 p-5 rounded-xl overflow-y-auto">
            <div className="history-header flex justify-between items-center mb-5">
              <h3 className="text-lg font-medium">History</h3>
              <button 
                className="clear-history border-none bg-transparent text-red-500 cursor-pointer"
                onClick={handleClearHistory}
              >
                Clear
              </button>
            </div>
            <div className="history-list flex flex-col gap-4">
              {history.length > 0 ? (
                history.map(item => (
                  <div 
                    key={item.id} 
                    className="history-item p-3 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                    onClick={() => handleHistoryItemPress(item)}
                  >
                    <div className="expression text-gray-500 dark:text-gray-400 text-sm">{item.expression}</div>
                    <div className="result text-lg font-medium">{item.result}</div>
                  </div>
                ))
              ) : (
                <div className="no-history text-center text-gray-500 dark:text-gray-400 py-5">
                  No history yet
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
