<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>PayDay Pilot - Offline</title>
  <meta name="theme-color" content="#3880ff">
  <link rel="manifest" href="/manifest.json?v=0.6.5">
  <link rel="icon" href="/favicon.ico">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
  <style>
    :root {
      color-scheme: light dark;
      --primary: #3880ff;
      --surface: #ffffff;
      --surface-muted: #f1f5f9;
      --text: #0f172a;
      --text-muted: #475569;
    }
    * {
      box-sizing: border-box;
    }
    body {
      margin: 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(180deg, #f8fafc 0%, #e0f2fe 100%);
      color: var(--text);
      padding: 2rem 1.5rem;
      text-align: center;
    }
    .card {
      background: var(--surface);
      border-radius: 24px;
      padding: 2.5rem 2rem;
      box-shadow: 0 20px 60px rgba(56, 128, 255, 0.12);
      max-width: 460px;
      width: 100%;
    }
    h1 {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.75rem;
    }
    p {
      margin: 0;
      color: var(--text-muted);
      line-height: 1.6;
    }
    .status {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      background: rgba(56, 128, 255, 0.1);
      color: var(--primary);
      padding: 0.4rem 0.75rem;
      border-radius: 999px;
      font-weight: 600;
      font-size: 0.9rem;
      margin-bottom: 1.5rem;
    }
    .tips {
      margin: 1.75rem 0 2.5rem;
      padding: 1.5rem;
      border-radius: 16px;
      background: var(--surface-muted);
      text-align: left;
    }
    .tips h2 {
      margin: 0 0 0.75rem;
      font-size: 1rem;
      font-weight: 600;
      color: var(--text);
    }
    .tips ul {
      margin: 0;
      padding-left: 1.1rem;
      color: var(--text-muted);
      line-height: 1.5;
      font-size: 0.95rem;
    }
    .button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.75rem 1.75rem;
      border-radius: 999px;
      font-weight: 600;
      background: var(--primary);
      color: #ffffff;
      border: none;
      cursor: pointer;
      transition: transform 0.15s ease, box-shadow 0.15s ease;
    }
    .button:hover {
      transform: translateY(-1px);
      box-shadow: 0 12px 24px rgba(56, 128, 255, 0.25);
    }
    footer {
      margin-top: 2rem;
      font-size: 0.8rem;
      color: var(--text-muted);
    }
    @media (prefers-color-scheme: dark) {
      body {
        background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
        color: #e2e8f0;
      }
      .card {
        background: #111827;
      }
      .tips {
        background: #1e293b;
      }
      .status {
        background: rgba(56, 128, 255, 0.15);
        color: #93c5fd;
      }
      .button {
        background: #60a5fa;
      }
    }
  </style>
</head>
<body>
  <main class="card">
    <div class="status">Offline mode</div>
    <h1>You are currently offline</h1>
    <p>PayDay Pilot will keep any notes or changes you made locally and sync them once you reconnect.</p>

    <div class="tips">
      <h2>What you can do now</h2>
      <ul>
        <li>Review bills and reminders that were already cached.</li>
        <li>Capture quick notes and they will sync when your connection returns.</li>
        <li>Refresh the page after you are back online to reload live data.</li>
      </ul>
    </div>

    <button class="button" onclick="window.location.href='/'">Try again</button>
  </main>
  <footer>&copy; 2025 PayDay Pilot</footer>

  <script>
    window.addEventListener('online', () => {
      window.location.reload();
    });
  </script>
</body>
</html>
