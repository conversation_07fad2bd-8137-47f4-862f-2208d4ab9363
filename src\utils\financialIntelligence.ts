import { Bill } from '@/types/bill';
// Add Transaction and FinancialSummary types
import { FinancialSummary, Transaction } from '@/types/financial';
import { addDays, differenceInDays, format, parse } from 'date-fns';
// Always use parseLocalDateString for YYYY-MM-DD date strings to avoid timezone issues
import { parseLocalDateString } from './date';

// Define interfaces for bill intelligence
export interface BillPattern {
  // Updated frequency to match detectBillFrequency return types
  frequency: 'monthly' | 'weekly' | 'bi-weekly' | 'quarterly' | 'annual' | 'irregular';
  typicalAmount: number;
  typicalDueDay: number;
  variancePercent: number;
  seasonalFactors?: { [month: string]: number };
}

export interface BillPrediction {
  billId: string;
  billName: string;
  predictedAmount: number;
  confidence: 'high' | 'medium' | 'low';
  reason: string;
}

export interface PaymentRecommendation {
  billId: string;
  billName: string;
  recommendedPayDate: string;
  priority: 'high' | 'medium' | 'low';
  reason: string;
}

// Define a minimal PaymentHistoryEntry type if not already defined
interface PaymentHistoryEntry {
  date: string;
  amount: number;
}

/**
 * Analyzes bill payment history to detect patterns and predict future bills
 */
export function analyzeBillPatterns(bills: Bill[]): { [billId: string]: BillPattern } {
  const patterns: { [billId: string]: BillPattern } = {};

  bills.forEach((bill: Bill) => {
    const history: PaymentHistoryEntry[] = (bill.paymentHistory as PaymentHistoryEntry[]) || [];
    if (!history || history.length < 2) return;

    // Calculate average amount and variance
    const amounts: number[] = history.map((h: PaymentHistoryEntry) => h.amount);
    const avgAmount = amounts.reduce((a: number, b: number) => a + b, 0) / amounts.length;
    const variance = Math.max(...amounts.map((a: number) => Math.abs(a - avgAmount))) / avgAmount;

    // Detect payment frequency
    const frequency = detectBillFrequency(history);

    // Find typical due day
    const dueDays: number[] = history.map((h: PaymentHistoryEntry) => parse(h.date, 'yyyy-MM-dd', new Date()).getDate());
    const typicalDueDay = mode(dueDays);

    patterns[bill.id] = {
      frequency,
      typicalAmount: Math.round(avgAmount * 100) / 100,
      typicalDueDay,
      variancePercent: Math.round(variance * 100),
      seasonalFactors: detectSeasonalPatterns(history)
    };
  });

  return patterns;
}

/**
 * Predicts upcoming bill amounts based on historical patterns
 */
export function predictUpcomingBills(
  bills: Bill[],
  patterns: { [billId: string]: BillPattern }
): BillPrediction[] {
  const predictions: BillPrediction[] = [];

  bills.forEach((bill: Bill) => {
    const pattern = patterns[bill.id];
    if (!pattern) return;

    const nextDueDate = parseLocalDateString(bill.dueDate);
    if (!nextDueDate) return;
    const month = nextDueDate.getMonth();

    let predictedAmount = pattern.typicalAmount;
    let confidence: 'high' | 'medium' | 'low' = 'high';
    let reason = 'Based on consistent payment history';

    // Apply seasonal factors if they exist
    if (pattern.seasonalFactors && pattern.seasonalFactors[month]) {
      predictedAmount *= pattern.seasonalFactors[month];
      confidence = 'medium';
      reason = 'Adjusted for seasonal patterns';
    }

    // Lower confidence if high variance
    if (pattern.variancePercent > 20) {
      confidence = 'low';
      reason = 'Bill amount varies significantly month to month';
    }

    predictions.push({
      billId: bill.id,
      billName: bill.name,
      predictedAmount: Math.round(predictedAmount * 100) / 100,
      confidence,
      reason
    });
  });

  return predictions;
}

/**
 * Generates smart payment scheduling recommendations
 */
export function generatePaymentRecommendations(
  bills: Bill[],
  incomeSchedule: { amount: number, date: string }[]
): PaymentRecommendation[] {
  const recommendations: PaymentRecommendation[] = [];
  const today = new Date();

  // Sort bills by due date
  const sortedBills = [...bills].sort((a: Bill, b: Bill) => {
    const dateA = parseLocalDateString(a.dueDate);
    const dateB = parseLocalDateString(b.dueDate);
    if (!dateA || !dateB) return 0;
    return dateA.getTime() - dateB.getTime();
  });

  let availableFunds = 0;
  let nextIncomeIndex = 0;

  sortedBills.forEach((bill: Bill) => {
    const dueDate = parseLocalDateString(bill.dueDate);
    if (!dueDate) return;

    // Add any income that comes before this bill
    while (nextIncomeIndex < incomeSchedule.length) {
      const incomeDate = parseLocalDateString(incomeSchedule[nextIncomeIndex].date);
      if (!incomeDate || incomeDate > dueDate) break;
      availableFunds += incomeSchedule[nextIncomeIndex].amount;
      nextIncomeIndex++;
    }

    // Determine priority and recommended pay date
    let priority: 'high' | 'medium' | 'low' = 'medium';
    let reason = '';
    let recommendedPayDate = bill.dueDate;

    if (availableFunds < bill.amount) {
      priority = 'high';
      reason = 'Insufficient funds projected - pay as soon as possible';
      // Find next income date that would cover this bill
      const neededIncome = incomeSchedule.find((inc: { amount: number; date: string }) => {
        const incDate = parseLocalDateString(inc.date);
        return incDate && incDate < dueDate && inc.amount >= bill.amount;
      });
      if (neededIncome) {
        recommendedPayDate = neededIncome.date;
      }
    } else if (differenceInDays(dueDate, today) <= 3) {
      priority = 'high';
      reason = 'Due date approaching';
    } else {
      // Look for bill clustering
      const nearbyBills = sortedBills.filter((b: Bill) => {
        const bDue = parseLocalDateString(b.dueDate);
        return bDue && Math.abs(differenceInDays(bDue, dueDate)) <= 3;
      });
      if (nearbyBills.length > 2) {
        priority = 'high';
        reason = 'Multiple bills due around the same time';
        // Recommend paying early if possible
        recommendedPayDate = format(addDays(today, 1), 'yyyy-MM-dd');
      }
    }

    availableFunds -= bill.amount;

    recommendations.push({
      billId: bill.id,
      billName: bill.name,
      recommendedPayDate,
      priority,
      reason
    });
  });

  return recommendations;
}

// Helper functions

/**
 * Detects the most likely frequency of bill payments based on historical dates.
 * @param history Array of payment history entries with dates.
 * @returns The detected frequency as a string.
 */
// Add export keyword
export function detectBillFrequency(history: PaymentHistoryEntry[]): 'monthly' | 'weekly' | 'bi-weekly' | 'quarterly' | 'annual' | 'irregular' {
  if (history.length < 2) return 'irregular';

  // Sort history by date just in case
  const sortedHistory = [...history].sort((a, b) =>
    parseLocalDateString(a.date)!.getTime() - parseLocalDateString(b.date)!.getTime()
  );

  const intervals: number[] = [];
  for (let i = 1; i < sortedHistory.length; i++) {
    const date1 = parseLocalDateString(sortedHistory[i - 1].date);
    const date2 = parseLocalDateString(sortedHistory[i].date);
    if (date1 && date2) {
      intervals.push(differenceInDays(date2, date1));
    }
  }

  if (intervals.length === 0) return 'irregular';

  // Calculate the mode (most frequent interval)
  const intervalCounts = new Map<number, number>();
  let maxCount = 0;
  let modeInterval = -1;

  intervals.forEach(interval => {
    // Allow for slight variations (e.g., 28-31 days for monthly)
    let roundedInterval = interval;
    if (interval >= 28 && interval <= 31) roundedInterval = 30; // Group monthly
    else if (interval >= 13 && interval <= 15) roundedInterval = 14; // Group bi-weekly
    else if (interval >= 6 && interval <= 8) roundedInterval = 7; // Group weekly
    else if (interval >= 88 && interval <= 92) roundedInterval = 90; // Group quarterly
    else if (interval >= 360 && interval <= 370) roundedInterval = 365; // Group annual

    const count = (intervalCounts.get(roundedInterval) || 0) + 1;
    intervalCounts.set(roundedInterval, count);
    if (count > maxCount) {
      maxCount = count;
      modeInterval = roundedInterval;
    }
  });

  // Determine frequency based on the mode interval
  // Require a certain consistency (e.g., mode appears > 50% of the time)
  const consistencyThreshold = 0.5;
  if (maxCount / intervals.length < consistencyThreshold) {
    return 'irregular';
  }

  switch (modeInterval) {
    case 7: return 'weekly';
    case 14: return 'bi-weekly';
    case 30: return 'monthly';
    case 90: return 'quarterly';
    case 365: return 'annual';
    default: return 'irregular';
  }
}

function detectSeasonalPatterns(history: PaymentHistoryEntry[]): { [month: string]: number } | undefined {
  // Implementation would look for consistent seasonal variations
  // and return multipliers for each month if found
  return undefined; // Simplified for now
}

function mode(numbers: number[]): number {
  const counts = new Map<number, number>();
  let maxCount = 0;
  let maxValue = numbers[0];

  numbers.forEach((num: number) => {
    const count = (counts.get(num) || 0) + 1;
    counts.set(num, count);
    if (count > maxCount) {
      maxCount = count;
      maxValue = num;
    }
  });

  return maxValue;
}

/**
 * Calculates the financial summary from a list of transactions.
 * @param txs Array of transactions.
 * @returns The calculated financial summary.
 */
export function calculateSummary(txs: Transaction[]): FinancialSummary {
  const income = txs
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const expenses = txs
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const cashFlow = income - expenses;
  const balance = cashFlow; // Simplified balance calculation

  return {
    income,
    expenses,
    cashFlow,
    balance,
  };
}
