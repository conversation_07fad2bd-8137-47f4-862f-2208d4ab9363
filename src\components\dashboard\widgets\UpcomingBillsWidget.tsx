'use client';

import { useUserPreferences } from '@/stores/userPreferencesStore';
import { useEffect, useState } from 'react';
import { BaseWidget } from './BaseWidget';

interface Bill {
  id: string;
  name: string;
  amount: number;
  dueDate: string;
  category: string;
  isPaid: boolean;
}

export default function UpcomingBillsWidget() {
  const [bills, setBills] = useState<Bill[]>([]);
  const [loading, setLoading] = useState(true);
  const { trackAction } = useUserPreferences();

  useEffect(() => {
    const timer = setTimeout(() => {
      // This would be an API call in production
      setBills([
        {
          id: '1',
          name: 'Rent',
          amount: 1200,
          dueDate: '2025-05-01',
          category: 'Housing',
          isPaid: false
        },
        {
          id: '2',
          name: 'Internet',
          amount: 75,
          dueDate: '2025-05-05',
          category: 'Utilities',
          isPaid: false
        },
        {
          id: '3',
          name: 'Phone Bill',
          amount: 45,
          dueDate: '2025-05-10',
          category: 'Utilities',
          isPaid: true
        }
      ]);
      setLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getDaysUntilDue = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getDueDateStatus = (dueDate: string, isPaid: boolean) => {
    if (isPaid) return { text: 'Paid', color: 'text-green-600 dark:text-green-400' };

    const daysUntilDue = getDaysUntilDue(dueDate);
    if (daysUntilDue < 0) return { text: 'Overdue', color: 'text-red-600 dark:text-red-400' };
    if (daysUntilDue === 0) return { text: 'Due today', color: 'text-yellow-600 dark:text-yellow-400' };
    if (daysUntilDue === 1) return { text: 'Due tomorrow', color: 'text-yellow-600 dark:text-yellow-400' };
    return { text: `Due in ${daysUntilDue} days`, color: 'text-blue-600 dark:text-blue-400' };
  };

  // Sort bills: overdue first, then due soon, then others, all by due date ascending
  const sortedBills = [...bills].sort((a, b) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const aDue = new Date(a.dueDate);
    const bDue = new Date(b.dueDate);
    const aOverdue = !a.isPaid && aDue < today;
    const bOverdue = !b.isPaid && bDue < today;
    if (aOverdue && !bOverdue) return -1;
    if (!aOverdue && bOverdue) return 1;
    // If both are overdue or both not overdue, sort by due date ascending
    return aDue.getTime() - bDue.getTime();
  });

  return (
    <BaseWidget
      title="Upcoming Bills"
      icon={
        <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      }
    >
      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
              <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {sortedBills.map((bill) => {
            const status = getDueDateStatus(bill.dueDate, bill.isPaid);

            return (
              <div
                key={bill.id}
                className="p-3 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-lg transition-colors cursor-pointer"
                onClick={() => trackAction('view_bill_details')}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-medium">{bill.name}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {bill.category}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">
                      {formatCurrency(bill.amount)}
                    </div>
                    <div className={`text-sm ${status.color}`}>
                      {status.text}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}

          <button
            className="w-full mt-2 py-2 text-sm text-blue-600 dark:text-blue-400 hover:underline"
            onClick={() => trackAction('view_all_bills')}
          >
            View all bills
          </button>
        </div>
      )}
    </BaseWidget>
  );
}