'use client';

import { useEffect, useState } from 'react';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export function InstallPrompt() {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [showManualInstructions, setShowManualInstructions] = useState(false);
  const [isDismissedInSession, setIsDismissedInSession] = useState(false);
  const [isMounted, setIsMounted] = useState(false); // Add mounted state

  useEffect(() => {
    setIsMounted(true); // Set mounted to true on client side
    console.log('[InstallPrompt Effect] Running effect...');

    // Early return for server-side rendering
    if (typeof window === 'undefined') return;

    // Check if dismissed in current session
    const dismissed = sessionStorage.getItem('pwaInstallDismissed');
    console.log('[InstallPrompt Effect] Checked sessionStorage:', dismissed);
    if (dismissed === 'true') {
      console.log('[InstallPrompt Effect] Already dismissed in session. Setting state and returning.');
      setIsDismissedInSession(true);
      setIsVisible(false); // Ensure it's not visible if dismissed
      return; // Don't proceed if dismissed
    }

    // Check if the app is already installed
    const checkInstalled = () => {
      // Check if running in standalone mode (PWA installed)
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      // @ts-ignore - Safari-specific property
      const isSafariStandalone = (window.navigator as any).standalone === true;

      if (isStandalone || isSafariStandalone) {
        setIsInstalled(true);
        return true;
      }
      return false;
    };

    // Don't show if already installed
    if (checkInstalled()) return;

    // Store the beforeinstallprompt event for later use
    let deferredPrompt: BeforeInstallPromptEvent | null = null;

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 76+ from automatically showing the prompt
      e.preventDefault();
      console.log('[InstallPrompt Event] beforeinstallprompt fired.');
      // Store the event for later use
      deferredPrompt = e as BeforeInstallPromptEvent;
      setInstallPrompt(deferredPrompt);
      // Show our custom install button ONLY if not dismissed
      if (!sessionStorage.getItem('pwaInstallDismissed')) {
        console.log('[InstallPrompt Event] Not dismissed, setting isVisible to true.');
        setIsVisible(true);
      }
      console.log('beforeinstallprompt event fired and captured');
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // If no install prompt event after 3 seconds, show manual instructions for iOS
    const timer = setTimeout(() => {
      console.log('[InstallPrompt Timer] Timer fired.');
      // Only show if not dismissed in session
      if (sessionStorage.getItem('pwaInstallDismissed') === 'true') {
        console.log('[InstallPrompt Timer] Dismissed in session, returning.');
        return;
      }

      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) ||
        (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);

      const isCurrentlyInstalled = checkInstalled();

      if ((isIOS || isSafari) && !isCurrentlyInstalled) {
        setShowManualInstructions(true);
        setIsVisible(true);
      } else if (deferredPrompt) {
        // We have the install prompt, make sure it's visible
        setIsVisible(true);
      } else {
        // Force show a basic install button after delay if nothing else worked
        // This helps on some Android browsers that don't fire the event properly
        if (!isCurrentlyInstalled) { // Added check to prevent showing if installed during delay
          setIsVisible(true);
        }
      }
      // We're not using isVisible or showManualInstructions in the callback, just setting them
      // So it's safer to log using the current state from the closure than the potentially stale props
      console.log('[InstallPrompt Timer] Timer finished logic.');
    }, 3000);

    // Check for appinstalled event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsVisible(false);
      console.log('App was installed');
    };

    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      clearTimeout(timer);
    };
  }, []); // We don't need isVisible or showManualInstructions as dependencies since we're only setting them

  const handleInstallClick = async () => {
    if (installPrompt) {
      // We have the install prompt event stored, use it
      try {
        // Show the install prompt
        await installPrompt.prompt();

        // Wait for the user to respond to the prompt
        const choiceResult = await installPrompt.userChoice;

        if (choiceResult.outcome === 'accepted') {
          console.log('User accepted the install prompt');
          setIsInstalled(true);
        } else {
          console.log('User dismissed the install prompt');
        }

        // Clear the saved prompt since it can't be used again
        setInstallPrompt(null);
      } catch (err) {
        console.error('Error during installation:', err);
        // If prompt fails, show manual instructions
        setShowManualInstructions(true);
      }
    } else {
      // No install prompt event, show manual instructions
      setShowManualInstructions(true);
    }
  };

  const handleDismiss = () => {
    console.log('[InstallPrompt Dismiss] Dismiss handler called.');
    setIsVisible(false);
    setShowManualInstructions(false);
    // Remember dismissal for this session
    if (typeof window !== 'undefined') {
      console.log('[InstallPrompt Dismiss] Setting sessionStorage pwaInstallDismissed to true.');
      sessionStorage.setItem('pwaInstallDismissed', 'true');
      setIsDismissedInSession(true);
    }
  };

  console.log('[InstallPrompt Render] Rendering component. isVisible:', isVisible, 'isInstalled:', isInstalled, 'isDismissedInSession:', isDismissedInSession, 'isMounted:', isMounted);
  // Add !isMounted check here
  if (!isMounted || !isVisible || isInstalled || isDismissedInSession) {
    console.log('[InstallPrompt Render] Condition met (or not mounted), returning null.');
    return null;
  }

  return (
    // Added a data attribute for easier selection if needed
    <div data-testid="install-prompt-banner" className="fixed bottom-16 left-0 right-0 mx-auto max-w-md p-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-40 m-4 border border-gray-200 dark:border-gray-700">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Install PayDay Pilot</h3>

          {showManualInstructions ? (
            <div className="mt-2">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                To install this app on your device:
              </p>
              <ul className="list-disc pl-5 mt-2 text-sm text-gray-500 dark:text-gray-400">
                <li><strong>iOS:</strong> Tap the share icon and then &quot;Add to Home Screen&quot;</li>
                <li><strong>Android:</strong> Tap the menu button and then &quot;Install App&quot;</li>
                <li><strong>Desktop:</strong> Click the install icon in the address bar</li>
              </ul>
            </div>
          ) : (
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Install this app on your device for quick access and offline use.
            </p>
          )}

          <div className="mt-4 flex space-x-3">
            {!showManualInstructions && (
              <button
                onClick={handleInstallClick}
                className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Install
              </button>
            )}
            <button
              onClick={handleDismiss}
              className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {showManualInstructions ? 'Got it' : 'Not now'}
            </button>
          </div>
        </div>
        <button
          onClick={handleDismiss}
          className="ml-3 flex-shrink-0 text-gray-400 hover:text-gray-500"
        >
          <span className="sr-only">Close</span>
          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  );
}
