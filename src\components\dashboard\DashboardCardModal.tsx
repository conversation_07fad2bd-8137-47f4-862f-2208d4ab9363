'use client';

import { Modal } from '@/components/ui/Modal';
import { ReactNode } from 'react';

interface DashboardCardModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

export function DashboardCardModal({
  isOpen,
  onClose,
  title,
  children,
  size = 'xl',
  className = ''
}: DashboardCardModalProps) {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size={size}
      className={`dashboard-card-modal ${className}`}
      showCloseButton={true}
      closeOnBackdropClick={true}
    >
      <div className="dashboard-modal-content">
        {children}
      </div>
    </Modal>
  );
}
