#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const MEMORY_LIMIT = '12288';
const PORT = process.env.PORT || '3000';
const CACHE_DIR = path.join(process.cwd(), 'node_modules/.cache');

function ensureCacheDirectory() {
  if (!fs.existsSync(CACHE_DIR)) {
    fs.mkdirSync(CACHE_DIR, { recursive: true });
    console.log('[dev-lightning] Created cache directory:', CACHE_DIR);
  }
}

function bootstrapCaches() {
  ensureCacheDirectory();

  const tsBuildInfoPath = path.join(process.cwd(), 'tsconfig.tsbuildinfo');
  if (!fs.existsSync(tsBuildInfoPath)) {
    fs.writeFileSync(tsBuildInfoPath, '{}');
    console.log('[dev-lightning] Initialised TypeScript build info cache');
  }

  const swcCacheDir = path.join(CACHE_DIR, 'swc');
  if (!fs.existsSync(swcCacheDir)) {
    fs.mkdirSync(swcCacheDir, { recursive: true });
    console.log('[dev-lightning] Prepared SWC cache directory');
  }
}

bootstrapCaches();

const env = {
  ...process.env,
  NODE_ENV: 'development',
  NEXT_TELEMETRY_DISABLED: '1',
  NEXT_SKIP_TSC: '1',
  NEXT_SKIP_PACKAGEJSON_MINIFICATION: '1',
  NEXT_MINIMAL_TRANSPILATION: '1',
  NEXT_DISABLE_ESLINT: '1',
  TURBO: '1',
  NODE_OPTIONS: `--max-old-space-size=${MEMORY_LIMIT} --no-warnings --no-deprecation`,
};

console.log('[dev-lightning] Launching Next.js with Turbopack');
const devProcess = spawn('next', ['dev', '--turbo', '-p', PORT], {
  env,
  stdio: 'inherit',
  shell: true,
});

process.on('SIGINT', () => {
  devProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  devProcess.kill('SIGTERM');
  process.exit(0);
});

devProcess.on('close', (code) => {
  process.exit(code ?? 0);
});
