/* Custom styles for react-big-calendar with refined blueprint theme */

/* CSS Variables for theme colors */
:root {
  --color-white: #ffffff;
  --color-light-gray: #f5f5f5;
  /* Light gray instead of cream */
  --color-dark-sepia: #483C32;
  --color-blueprint-blue: #1C71A6;
  --color-blueprint-blue-light: #3D8CBC;
  /* Lighter blue for highlights */
  --color-muted-red: #D9534F;
  --color-white-alpha-90: rgba(255, 255, 255, 0.9);
  --color-white-alpha-20: rgba(255, 255, 255, 0.2);
  --color-white-alpha-10: rgba(255, 255, 255, 0.1);
  --color-dark-sepia-alpha-90: rgba(72, 60, 50, 0.9);
  --color-dark-sepia-alpha-70: rgba(72, 60, 50, 0.7);
  --color-dark-sepia-alpha-40: rgba(72, 60, 50, 0.4);
  --color-dark-sepia-alpha-20: rgba(72, 60, 50, 0.2);
  --color-dark-sepia-alpha-10: rgba(72, 60, 50, 0.1);
  --color-blueprint-blue-alpha-20: rgba(28, 113, 166, 0.2);
  --color-blueprint-blue-alpha-10: rgba(28, 113, 166, 0.1);
}

/* Improve overall calendar appearance - Web View Optimized for Full Page */
.rbc-calendar {
  font-family: inherit;
  border-radius: 0;
  /* No border radius for full-page integration */
  overflow: hidden;
  height: 100%;
  /* Full height */
  background-color: var(--color-white);
  color: var(--color-dark-sepia);
  border: none;
  /* No border for seamless integration */
  box-shadow: none;
  /* No shadow for seamless integration */
  width: 100%;
  /* Full width */
}


/* Header row styling - Full Page Optimization */
.rbc-header {
  padding: 10px 3px;
  /* Slightly shorter to save vertical space */
  font-weight: 600;
  font-size: 0.9rem;
  background-color: var(--color-dark-sepia);
  color: var(--color-white);
  border-bottom: none;
  border-right: 1px solid var(--color-dark-sepia-alpha-20);
  font-family: var(--font-playfair), serif;
  text-transform: uppercase;
  letter-spacing: 0.03em;
}

.rbc-header:last-child {
  border-right: none;
}

.dark .rbc-header {
  background-color: var(--color-dark-sepia);
  color: var(--color-white);
  border-bottom: 1px solid var(--color-white-alpha-20);
}

/* Enhance month view - Full Page Optimization */
.rbc-month-view {
  border-radius: 0;
  overflow: hidden;
  background-color: var(--color-white);
  border: none;
  height: 100%;
  /* Full height */
  /* Expand to fill available space */
  display: flex;
  flex-direction: column;
  flex: 1;
}

.dark .rbc-month-view {
  background-color: var(--color-dark-sepia-alpha-10);
  border: none;
}

.rbc-month-row {
  display: flex;
  position: relative;
  flex-direction: column;
  flex: 1 0 0;
  /* Ensure even height for each week */
  overflow: hidden;
  height: 100%;
  border-bottom: 1px solid var(--color-dark-sepia-alpha-10);
}

.rbc-month-row:last-child {
  border-bottom: none;
}

/* Date cell styling */
.rbc-date-cell {
  padding: 1rem 0.75rem 0.5rem;
  /* Increased padding */
  text-align: right;
  font-size: 1.1rem;
  /* Slightly larger font */
  font-weight: 600;
  /* Slightly bolder for better visibility */
  color: var(--color-dark-sepia);
  font-family: var(--font-lato), sans-serif;
  background-color: var(--color-white);
  position: relative;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  /* Remove touch highlight on mobile */
  touch-action: manipulation;
  /* Optimize for touch */
}

/* Add active state to improve touch feedback */
.rbc-date-cell:active {
  background-color: var(--color-blueprint-blue-alpha-20);
  transition: background-color 0.1s;
}

.dark .rbc-date-cell {
  color: var(--color-cream-alpha-90);
  background-color: var(--color-dark-sepia-alpha-10);
}

.rbc-date-cell.rbc-now {
  font-weight: bold;
  color: var(--color-blueprint-blue);
  background-color: var(--color-light-gray);
}

.dark .rbc-date-cell.rbc-now {
  color: var(--color-blueprint-blue);
  background-color: var(--color-dark-sepia-alpha-40);
}

/* Off-range dates (days from other months) */
.rbc-off-range {
  color: var(--color-dark-sepia-alpha-40);
  background-color: var(--color-light-gray);
  /* Add pattern for visual distinction */
  background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.02) 25%, transparent 25%, transparent 50%, rgba(0, 0, 0, 0.02) 50%, rgba(0, 0, 0, 0.02) 75%, transparent 75%, transparent);
  background-size: 10px 10px;
}

.dark .rbc-off-range {
  color: var(--color-cream-alpha-20);
  background-color: var(--color-dark-sepia-alpha-20);
}

/* Day cells */
.rbc-day-bg {
  transition: background-color 0.2s;
  padding: 0.5rem;
  border: 1px solid var(--color-dark-sepia-alpha-10);
  position: relative;
  /* Add subtle inner shadow to create depth */
  box-shadow: inset 0 0 0 1px rgba(72, 60, 50, 0.03);

  /* Mobile swipe gestures and optimizations */
  @media (max-width: 767px) {

    /* View switching optimization */
    .rbc-view-switcher {
      display: flex;
      justify-content: center;
      gap: 0.25rem;
      width: 100%;
    }

    .rbc-view-switcher .rbc-btn-group {
      width: 100%;
      display: flex;
    }

    .rbc-view-switcher button {
      flex: 1;
      min-width: unset;
      font-size: 0.75rem;
      padding: 0.4rem 0.25rem;
    }

    /* Fix for mobile month view - prevent text cut-off */
    .rbc-event-content {
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.1;
    }

    /* Prevent long bill names from breaking layout in agenda view */
    .rbc-agenda-view .rbc-agenda-event-cell>div {
      max-width: 100%;
      overflow: hidden;
    }

    /* Handle portrait orientation better */
    @media (orientation: portrait) {
      .rbc-calendar {
        min-height: 70vh;
      }

      /* Use standard height settings */
      .rbc-month-view .rbc-month-row {
        min-height: 8rem;
        /* Standard row height */
        overflow-y: auto;
        /* Allow scrolling within rows */
      }
    }

    /* Handle landscape orientation better */
    @media (orientation: landscape) {
      .rbc-calendar {
        min-height: 85vh;
      }

      /* More horizontal space in landscape */
      .rbc-header {
        font-size: 0.8rem;
        padding: 4px 2px;
      }
    }
  }
}

/* Modern pull-to-refresh visual indicator */
@keyframes pullRefresh {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.calendar-pull-indicator {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--color-blueprint-blue);
  border-top-color: transparent;
  opacity: 0;
  transition: opacity 0.3s, top 0.3s;
  pointer-events: none;
}

.calendar-pull-active .calendar-pull-indicator {
  top: 10px;
  opacity: 1;
  animation: pullRefresh 1s infinite linear;
}

/* Make calendar events stand out better on mobile */
@media (max-width: 767px) {
  .rbc-month-view .rbc-event {
    background-color: var(--color-white);
    border-left: 3px solid var(--color-blueprint-blue);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .rbc-month-view .rbc-event.paid-event {
    border-left-color: var(--color-dark-sepia-alpha-40);
    opacity: 0.75;
  }

  .rbc-month-view .rbc-event.overdue-event {
    border-left-color: var(--color-muted-red);
  }

  /* Special styling for due-today events */
  .rbc-month-view .rbc-event.due-today-event {
    border-left-color: #FF9500;
    /* Orange for due today */
    background-color: rgba(255, 149, 0, 0.1);
    /* Light orange background */
    box-shadow: 0 1px 5px rgba(255, 149, 0, 0.25);
    /* Enhanced shadow */
  }

  /* Special styling for future bill instances */
  .rbc-month-view .rbc-event.future-event {
    border-left-color: #9333ea;
    /* Purple for future bills */
    background-color: rgba(147, 51, 234, 0.1);
    /* Light purple background */
    opacity: 0.8;
    /* Slightly transparent to indicate future */
    border-style: dashed;
    /* Dashed border to indicate projected */
  }

  .dark .rbc-month-view .rbc-event {
    background-color: var(--color-dark-sepia-alpha-70);
    border-left: 3px solid var(--color-blueprint-blue);
  }

  .dark .rbc-month-view .rbc-event.paid-event {
    border-left-color: var(--color-white-alpha-20);
  }

  .dark .rbc-month-view .rbc-event.overdue-event {
    border-left-color: var(--color-muted-red);
  }

  /* Dark mode styling for due-today events */
  .dark .rbc-month-view .rbc-event.due-today-event {
    border-left-color: #FF9500;
    /* Orange for due today */
    background-color: rgba(255, 149, 0, 0.2);
    /* Light orange background */
    box-shadow: 0 1px 5px rgba(255, 149, 0, 0.3);
    /* Enhanced shadow */
  }

  /* Dark mode styling for future bill instances */
  .dark .rbc-month-view .rbc-event.future-event {
    border-left-color: #a855f7;
    /* Lighter purple for dark mode */
    background-color: rgba(168, 85, 247, 0.2);
    /* Light purple background */
    opacity: 0.8;
    /* Slightly transparent to indicate future */
    border-style: dashed;
    /* Dashed border to indicate projected */
  }
}

.rbc-day-bg.rbc-today {
  background-color: var(--color-blueprint-blue-alpha-10);
  border: 1px solid var(--color-blueprint-blue-alpha-20);
  /* Add subtle radial gradient for today's date to make it stand out */
  background-image: radial-gradient(circle at center, var(--color-blueprint-blue-alpha-20) 0%, rgba(255, 255, 255, 0) 70%);
}

.dark .rbc-day-bg.rbc-today {
  background-color: var(--color-blueprint-blue-alpha-20);
  border: 1px solid var(--color-blueprint-blue);
}

/* Event styling */
.rbc-row-segment {
  padding: 0 4px 1px 4px;
  margin-bottom: 1px;
  min-height: 0;
  /* Let it be determined by row height */
}

.rbc-event {
  padding: 1px 3px;
  /* Reduced padding for compactness */
  border-radius: 4px;
  margin-bottom: 3px;
  box-shadow: 0 2px 4px rgba(72, 60, 50, 0.15);
  transition: transform 0.2s, box-shadow 0.2s;
  overflow: hidden;
  border: 1px solid var(--color-dark-sepia-alpha-20);
  min-height: 28px;
  position: relative;
  background-color: var(--color-white) !important;
  color: var(--color-dark-sepia) !important;
  border-left: 3px solid var(--color-blueprint-blue);
  /* Add a subtle gradient for more visual interest */
  background-image: linear-gradient(to right, rgba(28, 113, 166, 0.05), transparent) !important;
}

.rbc-event:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(72, 60, 50, 0.2);
  border-color: var(--color-dark-sepia-alpha-40);
  border-left-width: 5px;
  z-index: 5;
  /* Make hovered events appear above others */
}

.dark .rbc-event:hover {
  background-color: var(--color-dark-sepia-alpha-40);
  border-color: var(--color-white-alpha-40);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.rbc-event:active {
  transform: scale(0.98);
}

.rbc-event-content {
  font-size: 0.85rem;
  font-weight: 600;
  line-height: 1.3;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1px;
  font-family: var(--font-lato), sans-serif;
  /* Add text shadow for better readability */
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Styling for the '+X more' link in month view */
.rbc-show-more {
  display: block;
  /* Ensure it takes its own line if needed */
  text-align: center;
  font-size: 0.8rem;
  /* Consistent with other small text */
  font-weight: 500;
  color: var(--color-blueprint-blue);
  background-color: rgba(245, 245, 245, 0.5);
  /* --color-light-gray with alpha */
  border-radius: 4px;
  padding: 3px 5px;
  margin-top: 4px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.rbc-show-more:hover,
.rbc-show-more:focus {
  color: var(--color-white);
  background-color: var(--color-blueprint-blue-light, #3D8CBC);
  /* Use a lighter blue from theme */
  text-decoration: none;
}

.dark .rbc-show-more {
  color: var(--color-white-alpha-90, rgba(255, 255, 255, 0.9));
  background-color: var(--color-dark-sepia-alpha-40, rgba(72, 60, 50, 0.4));
}

.dark .rbc-show-more:hover,
.dark .rbc-show-more:focus {
  color: var(--color-dark-sepia);
  background-color: var(--color-white-alpha-20, rgba(255, 255, 255, 0.2));
}

/* Toolbar styling */
/* Toolbar styling - Web View Optimized */
.rbc-toolbar {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  /* Better spacing for desktop */
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 1.5rem;
  /* More padding for desktop */
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-dark-sepia-alpha-10);
}

.dark .rbc-toolbar {
  background-color: var(--color-dark-sepia-alpha-10);
  border-bottom: 1px solid var(--color-cream-alpha-10);
}

.rbc-toolbar-label {
  font-weight: 600;
  font-size: 1.75rem;
  /* Larger title for desktop */
  color: var(--color-dark-sepia);
  flex-grow: 1;
  font-family: var(--font-playfair), serif;
  text-align: center;
  margin: 0 2rem;
  /* Add margin for better spacing */
}

.dark .rbc-toolbar-label {
  color: var(--color-white);
}

.rbc-toolbar button {
  color: var(--color-dark-sepia);
  border: 1px solid var(--color-dark-sepia-alpha-40);
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  /* Larger buttons for desktop */
  font-size: 0.95rem;
  /* Larger text for desktop */
  font-weight: 500;
  transition: all 0.2s;
  background-color: var(--color-white);
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: var(--font-lato), sans-serif;
  box-shadow: 0 2px 4px var(--color-dark-sepia-alpha-10);
  min-width: 100px;
  /* Ensure buttons have reasonable width */
}

.dark .rbc-toolbar button {
  color: var(--color-white);
  border-color: var(--color-cream-alpha-20);
  background-color: var(--color-dark-sepia-alpha-20);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.rbc-toolbar button:hover {
  background-color: var(--color-light-gray);
  border-color: var(--color-dark-sepia-alpha-70);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px var(--color-dark-sepia-alpha-20);
}

.dark .rbc-toolbar button:hover {
  background-color: var(--color-dark-sepia-alpha-40);
  border-color: var(--color-white-alpha-40);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.rbc-toolbar button.rbc-active {
  background-color: var(--color-blueprint-blue);
  color: white;
  border-color: var(--color-blueprint-blue);
  box-shadow: 0 1px 3px rgba(28, 113, 166, 0.4);
}

.dark .rbc-toolbar button.rbc-active {
  background-color: var(--color-blueprint-blue);
  border-color: var(--color-blueprint-blue);
  color: var(--color-white);
}

.dark .rbc-toolbar button.rbc-active {
  background-color: var(--color-blueprint-blue);
  border-color: var(--color-blueprint-blue);
  color: white;
  box-shadow: 0 1px 3px rgba(28, 113, 166, 0.6);
}

/* Time grid styling (for week and day views) */
.rbc-time-header {
  border-bottom: 1px solid var(--color-dark-sepia-alpha-20);
}

.dark .rbc-time-header {
  border-color: var(--color-cream-alpha-20);
}

.rbc-time-content {
  border-top: 1px solid var(--color-dark-sepia-alpha-20);
}

.dark .rbc-time-content {
  border-color: var(--color-cream-alpha-20);
}

.rbc-time-slot {
  font-size: 0.75rem;
  color: var(--color-dark-sepia-alpha-90);
  font-family: var(--font-lato), sans-serif;
}

.dark .rbc-time-slot {
  color: var(--color-cream-alpha-90);
}

/* Agenda view styling */
.rbc-agenda-view table {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
  border-spacing: 0;
  border-collapse: collapse;
}

.dark .rbc-agenda-view table {
  border-color: #4b5563;
}

.rbc-agenda-view table thead {
  background-color: rgba(243, 244, 246, 0.7);
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  color: #2d3748;
}

.dark .rbc-agenda-view table thead {
  background-color: rgba(55, 65, 81, 0.7);
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

.rbc-agenda-view table th {
  font-weight: 600;
  font-size: 0.875rem;
  padding: 10px;
}

.rbc-agenda-view table td {
  padding: 10px;
  border-bottom: 1px solid #e5e7eb;
  border-color: #4b5563;
}

.rbc-agenda-date-cell,
.rbc-agenda-time-cell {
  padding: 0.5rem;
  font-weight: 500;
}

.rbc-agenda-event-cell {
  padding: 0.5rem;
}

/* Animation for event hover */
.rbc-event {
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.rbc-event:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .rbc-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .rbc-toolbar-label {
    margin: 8px 0;
    font-size: 1rem;
    order: -1;
    /* Display month at the top */
  }

  .rbc-btn-group {
    justify-content: center;
    flex-wrap: wrap;
    display: flex;
    gap: 0.5rem;
  }

  .rbc-header {
    padding: 6px 2px;
    font-size: 0.75rem;
  }

  .rbc-date-cell {
    font-size: 0.75rem;
  }

  .rbc-event-content {
    font-size: 0.7rem;
  }

  /* Improve month label display for small screens */
  .rbc-month-header {
    display: flex;
    justify-content: space-between;
  }

  .rbc-month-view {
    padding-bottom: 10px;
  }

  /* Make the day abbreviations more compact */
  .rbc-header span {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* One-letter day names for very small screens */
  @media (max-width: 380px) {
    .rbc-header span {
      max-width: 14px;
      margin: 0 auto;
    }
  }
}

/* Make sure the toolbar buttons are properly sized and spaced on mobile */
@media (max-width: 500px) {
  .rbc-toolbar button {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }

  .rbc-btn-group {
    margin: 0 auto;
  }

  .rbc-toolbar-label {
    font-size: 1.1rem;
    font-weight: 700;
    padding: 8px 0;
    line-height: 1.2;
  }
}

/* Fix for month header positioning */
.rbc-month-header {
  height: auto !important;
}

/* Enhance first-row visibility */
.rbc-month-row:first-child {
  border-top: 1px solid var(--color-dark-sepia-alpha-20);
}

.dark .rbc-month-row:first-child {
  border-top-color: var(--color-cream-alpha-20);
}

/* --- Themed Agenda View Styling --- */

/* Apply theme to the main agenda container */
.rbc-agenda-view {
  background-color: var(--color-white);
  /* Use theme background */
  border: 1px solid var(--color-dark-sepia-alpha-20);
  /* Consistent border */
  border-radius: 0.5rem;
  /* Match other views */
  overflow: hidden;
  /* Hide outer overflow */
  display: flex;
  /* Use flexbox for structure */
  flex-direction: column;
  /* Stack header and content vertically */
  max-height: 70vh;
  /* Make it taller, limit height */
  box-shadow: 0 4px 12px var(--color-dark-sepia-alpha-10);
}

.dark .rbc-agenda-view {
  background-color: var(--color-dark-sepia-alpha-10);
  /* Dark theme background */
  border-color: var(--color-cream-alpha-20);
}

/* Style the overall table - Make it scrollable */
.rbc-agenda-view .rbc-agenda-table {
  border: none;
  /* Remove default table border */
  color: var(--color-dark-sepia);
  /* Default text color */
  flex-grow: 1;
  /* Allow table to grow */
  overflow-y: auto;
  /* Enable vertical scrolling */
  display: block;
  /* Needed for overflow on table */
}

.dark .rbc-agenda-view .rbc-agenda-table {
  color: var(--color-cream-alpha-90);
}

/* Style the header row (Date | Time | Event) */
/* Use display: table-header-group for sticky header potential */
.rbc-agenda-view thead {
  display: table-header-group;
  /* Stick header */
  position: sticky;
  top: 0;
  z-index: 1;
}

.rbc-agenda-view thead th {
  background-color: var(--color-dark-sepia);
  /* Theme header bg - match the month view header */
  border-bottom: 1px solid var(--color-dark-sepia-alpha-40);
  /* Separator */
  color: var(--color-white);
  padding: 0.75rem 0.5rem;
  font-weight: 600;
  text-align: left;
  font-family: var(--font-playfair), serif;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  /* Adjust widths now that Time is hidden */
}

.rbc-agenda-view thead th.rbc-header:first-child {
  /* Date header */
  width: 100px;
  /* Fixed width for Date */
}

.rbc-agenda-view thead th.rbc-header:last-child {
  /* Event header */
  width: auto;
  /* Let event take remaining space */
}


.dark .rbc-agenda-view thead th {
  background-color: var(--color-dark-sepia-alpha-20);
  border-bottom-color: var(--color-white-alpha-20);
  color: var(--color-white);
}

/* Improved style for the table body */
.rbc-agenda-view tbody {
  display: table-row-group;
}

/* Style the date cells (e.g., "Mon May 05") */
.rbc-agenda-view .rbc-agenda-date-cell {
  padding: 0.75rem 0.5rem;
  font-weight: 500;
  border-right: 1px solid var(--color-dark-sepia-alpha-20);
  /* Separator */
  width: 90px;
  /* Slightly narrower for mobile */
  vertical-align: top;
  /* Align date to top */
  font-family: var(--font-lato), sans-serif;
  background-color: var(--color-light-gray);
  color: var(--color-dark-sepia);
  position: sticky;
  /* Keep dates visible during scroll */
  left: 0;
  z-index: 1;
}

.dark .rbc-agenda-view .rbc-agenda-date-cell {
  border-right-color: var(--color-cream-alpha-20);
  background-color: var(--color-dark-sepia-alpha-20);
  color: var(--color-cream-alpha-90);
}

/* Mobile optimizations for date cells */
@media (max-width: 500px) {
  .rbc-agenda-view .rbc-agenda-date-cell {
    width: 70px;
    padding: 0.5rem 0.35rem;
    font-size: 0.85rem;
  }

  .rbc-agenda-view thead th.rbc-header:first-child {
    width: 70px;
  }
}


/* Hide the time cell */
.rbc-agenda-view .rbc-agenda-time-cell {
  /* Keep styles for consistency if shown, but hide it */
  padding: 0.75rem 0.5rem;
  border-right: 1px solid var(--color-dark-sepia-alpha-20);
  display: none;
  /* Hide the Time column */
}

.dark .rbc-agenda-view .rbc-agenda-time-cell {
  border-right-color: var(--color-cream-alpha-20);
}

/* Add padding to the event cell */
.rbc-agenda-view .rbc-agenda-event-cell {
  padding: 0.5rem;
  width: auto;
  /* Take remaining width */
}

/* Enhanced style for content rows with modern touch interactions */
.rbc-agenda-view table tbody tr {
  border-bottom: 1px solid var(--color-dark-sepia-alpha-10);
  /* Subtle row separator */
  display: table-row;
  /* Ensure rows behave like table rows */
  transition: all 0.2s ease;
  position: relative;
}

.rbc-agenda-view table tbody tr:hover {
  background-color: var(--color-light-gray);
}

.rbc-agenda-view table tbody tr:active {
  background-color: var(--color-blueprint-blue-alpha-10);
  /* Feedback on tap */
  transform: scale(0.99);
  /* Subtle scale on tap */
}

.dark .rbc-agenda-view table tbody tr {
  border-bottom-color: var(--color-white-alpha-10);
}

.dark .rbc-agenda-view table tbody tr:hover {
  background-color: var(--color-dark-sepia-alpha-20);
}

.dark .rbc-agenda-view table tbody tr:active {
  background-color: var(--color-blueprint-blue-alpha-20);
}

.rbc-agenda-view table tbody tr:last-child {
  border-bottom: none;
  /* No border on last row */
}

/* Ensure our custom event takes necessary space */
.rbc-agenda-view .rbc-agenda-event-cell>div {
  margin-bottom: 0;
  /* Remove extra margin if theme handles spacing */
}

/* Add modern scroll indicators for mobile */
.rbc-agenda-view::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, var(--color-white), transparent);
  pointer-events: none;
  opacity: 0.7;
  z-index: 2;
}

.dark .rbc-agenda-view::after {
  background: linear-gradient(to top, var(--color-dark-sepia-alpha-40), transparent);
}

/* Mobile optimization for touch targets */
@media (max-width: 500px) {
  .rbc-agenda-view .rbc-agenda-event-cell {
    padding: 0.5rem 0.35rem 0.5rem 0.25rem;
  }

  /* Add subtle ripple effect for touch feedback */
  .rbc-agenda-view table tbody tr::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at var(--x, center) var(--y, center), var(--color-blueprint-blue-alpha-10) 0%, transparent 10%);
    opacity: 0;
    transform: scale(5);
    transition: opacity 0.3s, transform 0.3s;
  }

  .rbc-agenda-view table tbody tr:active::after {
    opacity: 1;
    transform: scale(0);
    transition: opacity 0s, transform 0.4s;
  }
}

/* Side panel for bill editing */
.calendar-side-panel {
  position: fixed;
  right: 0;
  top: 0;
  height: 100vh;
  width: 40%;
  max-width: 600px;
  min-width: 320px;
  background-color: var(--color-white);
  border-left: 1px solid var(--color-dark-sepia-alpha-20);
  box-shadow: -5px 0 15px rgba(72, 60, 50, 0.1);
  z-index: 100;
  overflow-y: auto;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  padding: 1.5rem;
}

.rbc-toolbar {
  margin-bottom: 10px;
  padding: 5px 10px;
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-dark-sepia-alpha-10);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.dark .calendar-side-panel {
  background-color: var(--color-dark-sepia-alpha-10);
  border-left: 1px solid var(--color-cream-alpha-20);
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
}

.calendar-side-panel.open {
  transform: translateX(0);
}

.calendar-side-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--color-dark-sepia-alpha-10);
}

.dark .calendar-side-panel-header {
  border-bottom: 1px solid var(--color-white-alpha-10);
}

.calendar-side-panel-title {
  font-family: var(--font-playfair), serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-dark-sepia);
}

.dark .calendar-side-panel-title {
  color: var(--color-white);
}

.calendar-close-button,
.calendar-action-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  color: var(--color-dark-sepia);
}

.dark .calendar-close-button,
.dark .calendar-action-button {
  color: var(--color-white);
}

.calendar-close-button:hover,
.calendar-action-button:hover {
  background-color: var(--color-dark-sepia-alpha-10);
  transform: translateY(-1px);
}

.dark .calendar-close-button:hover,
.dark .calendar-action-button:hover {
  background-color: var(--color-white-alpha-10);
}

/* Delete button styling */
.calendar-action-button.text-muted-red {
  color: var(--color-muted-red);
}

.dark .calendar-action-button.text-muted-red {
  color: var(--color-muted-red);
}

.calendar-action-button.text-muted-red:hover {
  background-color: rgba(217, 83, 79, 0.1);
}

.calendar-edit-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 90;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.calendar-edit-overlay.active {
  opacity: 1;
  pointer-events: auto;
}

/* Responsive adjustments for side panel */
@media (max-width: 768px) {
  .calendar-side-panel {
    width: 90%;
    max-width: 90%;
  }
}

/* Calendar layout when side panel is open */
.calendar-with-sidepanel-open {
  margin-right: 40%;
  transition: margin-right 0.3s ease-in-out;
}

@media (max-width: 1200px) {
  .calendar-with-sidepanel-open {
    margin-right: 0;
  }
}

/* Animate bill being edited */
.bill-being-edited {
  animation: pulse-highlight 2s ease-in-out infinite;
  border-left-width: 5px !important;
  z-index: 5;
}

@keyframes pulse-highlight {

  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(28, 113, 166, 0.4);
  }

  50% {
    box-shadow: 0 0 0 5px rgba(28, 113, 166, 0.2);
  }
}