import { OptimizedImage } from '@/components/ui/OptimizedImage';
import Image from 'next/image';
import { useState } from 'react';
import { AuthModal } from './AuthModal';

export default function LandingPage() {
  const [authOpen, setAuthOpen] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-indigo-50 to-white dark:from-gray-900 dark:to-gray-950">
      <header className="w-full py-6 px-4 flex justify-between items-center max-w-5xl mx-auto">
        <div className="flex items-center space-x-2">
          <OptimizedImage src="/images/logo.png" alt="Payday Pilot Logo" width={40} height={40} />
          <span className="text-2xl font-bold text-indigo-700 dark:text-white">Payday Pilot</span>
        </div>
        <div className="space-x-2">
          <button onClick={() => { setShowSignUp(false); setAuthOpen(true); }} className="px-4 py-2 rounded-md font-medium text-indigo-700 border border-indigo-700 hover:bg-indigo-50 dark:text-white dark:border-white dark:hover:bg-gray-800">Login</button>
        </div>
      </header>
      <main className="flex-1 flex flex-col items-center justify-center text-center px-4">
        {/* Hero section without large logo */}
        <h1 className="text-4xl md:text-5xl font-extrabold text-gray-900 dark:text-white mb-4">Take Control of Your Bills</h1>
        <p className="text-lg md:text-2xl text-gray-700 dark:text-gray-300 mb-8 max-w-2xl mx-auto">Never miss a payment again. Payday Pilot helps you track, organize, and predict your bills so you can stress less and save more.</p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-10">
          <button
            onClick={() => { setShowSignUp(true); setAuthOpen(true); }}
            className="px-8 py-4 rounded-lg text-lg font-semibold text-white hover:bg-indigo-800 shadow-md"
            style={{ backgroundColor: '#2563eb' }}
          >
            Get Started
          </button>
        </div>
        {/* Feature section with app screenshot instead of logo */}
        <div className="flex flex-col md:flex-row gap-8 items-center justify-center mb-12">
          <Image src="/images/logo.png" alt="App Screenshot" width={320} height={320} className="rounded-xl shadow-lg border border-gray-200 dark:border-gray-800" priority />
          <ul className="text-left space-y-4 max-w-md">
            <li className="flex items-start gap-3">
              <span className="text-indigo-600 dark:text-indigo-400 text-2xl">✓</span>
              <span className="text-lg text-gray-800 dark:text-gray-200">Smart bill reminders & notifications</span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-indigo-600 dark:text-indigo-400 text-2xl">✓</span>
              <span className="text-lg text-gray-800 dark:text-gray-200">Visualize your cash flow & upcoming expenses</span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-indigo-600 dark:text-indigo-400 text-2xl">✓</span>
              <span className="text-lg text-gray-800 dark:text-gray-200">Simple, secure, and always free to start</span>
            </li>
          </ul>
        </div>
        {/* How it works section */}
        <section className="w-full max-w-3xl mx-auto mt-8 mb-16">
          <h2 className="text-2xl md:text-3xl font-bold text-indigo-700 dark:text-indigo-300 mb-6">How it works</h2>
          <div className="grid md:grid-cols-3 gap-8 text-left">
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-6 flex flex-col items-center">
              <span className="text-4xl mb-2">📝</span>
              <h3 className="font-semibold text-lg mb-2">1. Add Your Bills</h3>
              <p className="text-gray-600 dark:text-gray-300">Quickly enter your recurring bills and due dates. Payday Pilot keeps everything organized in one place.</p>
            </div>
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-6 flex flex-col items-center">
              <span className="text-4xl mb-2">🔔</span>
              <h3 className="font-semibold text-lg mb-2">2. Get Reminders</h3>
              <p className="text-gray-600 dark:text-gray-300">Receive smart notifications before bills are due, so you never miss a payment again.</p>
            </div>
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-6 flex flex-col items-center">
              <span className="text-4xl mb-2">📊</span>
              <h3 className="font-semibold text-lg mb-2">3. Track & Plan</h3>
              <p className="text-gray-600 dark:text-gray-300">See your upcoming expenses and cash flow at a glance. Plan ahead with confidence.</p>
            </div>
          </div>
        </section>
      </main>
      <footer className="py-6 text-center text-gray-400 text-sm">&copy; {new Date().getFullYear()} Payday Pilot. All rights reserved.</footer>
      <AuthModal open={authOpen} onClose={() => setAuthOpen(false)} initialSignUp={showSignUp} />
    </div>
  );
}
