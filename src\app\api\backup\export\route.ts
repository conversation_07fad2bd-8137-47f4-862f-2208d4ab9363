import { db } from '@/lib/firebase-admin';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
    // Get UID from header (adjust as needed for your auth flow)
    const uid = req.headers.get('x-user-uid');
    if (!uid) {
        return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    // Fetch user bills
    const billsSnap = await db.collection('bills').where('userId', '==', uid).get();
    const bills = billsSnap.docs.map((doc: FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>) => ({ id: doc.id, ...doc.data() }));

    // Fetch user preferences
    const userDoc = await db.collection('users').doc(uid).get();
    const preferences = userDoc.data() || {};

    // Combine into one export object
    const exportData = {
        bills,
        preferences,
        exportedAt: new Date().toISOString(),
    };

    // Return as downloadable JSON
    return new NextResponse(JSON.stringify(exportData), {
        status: 200,
        headers: {
            'Content-Type': 'application/json',
            'Content-Disposition': `attachment; filename="payday-backup-${uid}.json"`,
        },
    });
}
