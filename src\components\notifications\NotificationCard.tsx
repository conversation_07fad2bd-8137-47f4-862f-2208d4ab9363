import { motion } from 'framer-motion';

interface NotificationCardProps {
  notification: {
    id: string;
    title: string;
    body: string;
    date: string;
    read: boolean;
    type?: string;
  };
  onClick?: () => void;
  onMarkAsRead?: () => void;
  onDelete?: () => void;
}

function NotificationCard({ notification, onClick, onMarkAsRead, onDelete }: NotificationCardProps) {
  const getNotificationIcon = (type?: string) => {
    switch (type) {
      case 'bill':
        return '💳';
      case 'reminder':
        return '⏰';
      case 'payment':
        return '💰';
      case 'system':
        return '⚙️';
      default:
        return '🔔';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      whileHover={{ scale: 1.01 }}
      className={`
        relative flex items-start rounded-2xl border transition-all duration-200 cursor-pointer
        ${!notification.read
          ? 'border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-950/30 shadow-md'
          : 'border-gray-100 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm hover:shadow-md'
        }
        p-4 gap-4 group
      `}
      onClick={onClick}
    >
      {/* Unread indicator */}
      {!notification.read && (
        <div className="absolute left-0 top-4 w-1 h-8 bg-blue-500 rounded-r-full" />
      )}

      {/* Icon */}
      <div className="flex-shrink-0 mt-1">
        <div className={`
          inline-flex items-center justify-center h-10 w-10 rounded-xl text-lg
          ${!notification.read
            ? 'bg-blue-100 dark:bg-blue-900/50'
            : 'bg-gray-100 dark:bg-gray-800'
          }
        `}>
          {getNotificationIcon(notification.type)}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between gap-2">
          <h3 className={`
            font-semibold text-sm leading-tight
            ${!notification.read
              ? 'text-gray-900 dark:text-white'
              : 'text-gray-700 dark:text-gray-300'
            }
          `}>
            {notification.title}
          </h3>
          <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap flex-shrink-0">
            {formatDate(notification.date)}
          </span>
        </div>
        <p className={`
          text-sm mt-1 leading-relaxed
          ${!notification.read
            ? 'text-gray-700 dark:text-gray-300'
            : 'text-gray-600 dark:text-gray-400'
          }
        `}>
          {notification.body}
        </p>
      </div>

      {/* Action buttons */}
      <div className="flex-shrink-0 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        {!notification.read && onMarkAsRead && (
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={(e) => {
              e.stopPropagation();
              onMarkAsRead();
            }}
            className="p-1.5 rounded-lg bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-200"
            title="Mark as read"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </motion.button>
        )}

        {onDelete && (
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="p-1.5 rounded-lg bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800 transition-colors duration-200"
            title="Delete notification"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </motion.button>
        )}
      </div>

      {/* Unread dot indicator */}
      {!notification.read && (
        <div className="absolute top-2 right-2 w-2 h-2 bg-blue-500 rounded-full" />
      )}
    </motion.div>
  );
}

export default NotificationCard;
