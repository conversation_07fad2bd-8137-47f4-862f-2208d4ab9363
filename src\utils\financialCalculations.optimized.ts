// Financial calculation utilities for loans, debts, and payments with improved precision

import { DebtInfo, LoanInfo, PaymentFrequency } from '@/types/bill';
import { Decimal } from 'decimal.js';

/**
 * Convert payment frequency to number of payments per year
 */
export function getPaymentsPerYear(frequency: PaymentFrequency): number {
    switch (frequency) {
        case 'weekly': return 52;
        case 'biweekly': return 26;
        case 'monthly': return 12;
        case 'quarterly': return 4;
        case 'annually': return 1;
        default: return 12;
    }
}

/**
 * Convert annual interest rate to periodic rate with improved precision
 */
export function getPeriodicRate(annualRate: number, frequency: PaymentFrequency): number {
    const paymentsPerYear = getPaymentsPerYear(frequency);
    // Use a more precise division
    return new Decimal(annualRate).div(100).div(paymentsPerYear).toNumber();
}

/**
 * Calculate loan payment using PMT formula with enhanced precision
 * PMT = P * [r(1+r)^n] / [(1+r)^n - 1]
 */
export function calculateLoanPayment(loanInfo: LoanInfo): number {
    const { originalAmount, interestRate, loanTerm, paymentFrequency } = loanInfo;

    if (!originalAmount || originalAmount <= 0) {
        return 0;
    }

    if (!interestRate || interestRate === 0) {
        // No interest loan - simple division with improved precision
        const paymentsPerYear = getPaymentsPerYear(paymentFrequency || 'monthly');
        const totalPayments = new Decimal(loanTerm || 12).div(12).mul(paymentsPerYear);
        return new Decimal(originalAmount).div(totalPayments).toDecimalPlaces(2).toNumber();
    }

    const periodicRate = getPeriodicRate(interestRate, paymentFrequency || 'monthly');
    const paymentsPerYear = getPaymentsPerYear(paymentFrequency || 'monthly');
    const totalPayments = new Decimal(loanTerm || 12).div(12).mul(paymentsPerYear).toNumber();

    // Convert to Decimal for high precision calculation
    const decPeriodicRate = new Decimal(periodicRate);
    const decOriginalAmount = new Decimal(originalAmount);

    // Calculate using Decimal.js for high precision
    const numerator = decPeriodicRate.mul(Decimal.pow(decPeriodicRate.add(1), totalPayments));
    const denominator = Decimal.pow(decPeriodicRate.add(1), totalPayments).minus(1);

    // Handle potential division by zero
    if (denominator.isZero()) {
        return decOriginalAmount.div(totalPayments).toDecimalPlaces(2).toNumber();
    }

    return decOriginalAmount.mul(numerator.div(denominator)).toDecimalPlaces(2).toNumber();
}

/**
 * Calculate minimum payment for credit card debt with improved precision
 */
export function calculateMinimumPayment(debtInfo: DebtInfo): number {
    if (!debtInfo || !debtInfo.currentBalance) return 0;

    const { currentBalance, minimumPaymentCalculation, minimumPaymentPercentage, minimumPayment } = debtInfo;

    switch (minimumPaymentCalculation) {
        case 'percentage':
            return new Decimal(currentBalance)
                .mul(new Decimal(minimumPaymentPercentage || 2).div(100))
                .toDecimalPlaces(2)
                .toNumber();

        case 'fixed':
            return minimumPayment || 0;

        case 'balance_based':
            // Common credit card formula: 1% of balance + interest + fees
            const interestCharge = new Decimal(currentBalance)
                .mul(new Decimal(debtInfo.interestRate || 0).div(100).div(12))
                .toDecimalPlaces(2);

            const onePercentOfBalance = new Decimal(currentBalance).mul(0.01).toDecimalPlaces(2);
            const calculatedMin = onePercentOfBalance.plus(interestCharge);

            return Math.max(25, calculatedMin.toNumber());

        default:
            return minimumPayment || 0;
    }
}

/**
 * Calculate total interest for a loan with improved precision
 */
export function calculateTotalInterest(loanInfo: LoanInfo): number {
    if (!loanInfo || !loanInfo.originalAmount) return 0;

    const monthlyPayment = calculateLoanPayment(loanInfo);
    const paymentsPerYear = getPaymentsPerYear(loanInfo.paymentFrequency || 'monthly');
    const totalPayments = new Decimal(loanInfo.loanTerm || 12).div(12).mul(paymentsPerYear);

    const totalPaid = new Decimal(monthlyPayment).mul(totalPayments);

    return totalPaid.minus(loanInfo.originalAmount).toDecimalPlaces(2).toNumber();
}

/**
 * Calculate payoff date for a loan
 */
export function calculatePayoffDate(loanInfo: LoanInfo): Date {
    if (!loanInfo || !loanInfo.startDate) {
        return new Date(); // Return current date if no valid info
    }

    const startDate = new Date(loanInfo.startDate);
    if (isNaN(startDate.getTime())) {
        return new Date(); // Return current date if invalid start date
    }

    const payoffDate = new Date(startDate);
    payoffDate.setMonth(payoffDate.getMonth() + (loanInfo.loanTerm || 12));

    return payoffDate;
}

/**
 * Calculate debt payoff time with minimum payments with improved precision
 */
export function calculateDebtPayoffTime(debtInfo: DebtInfo, monthlyPayment?: number): {
    months: number;
    totalInterest: number;
    totalPaid: number;
} {
    if (!debtInfo || !debtInfo.currentBalance) {
        return { months: 0, totalInterest: 0, totalPaid: 0 };
    }

    const payment = monthlyPayment || calculateMinimumPayment(debtInfo);
    if (payment <= 0) {
        return { months: Infinity, totalInterest: Infinity, totalPaid: Infinity };
    }

    const monthlyRate = new Decimal(debtInfo.interestRate || 0).div(100).div(12);
    let balance = new Decimal(debtInfo.currentBalance);
    let months = 0;
    let totalInterest = new Decimal(0);

    // Prevent infinite loops with a reasonable upper limit
    const MAX_ITERATIONS = 1200; // 100 years

    while (balance.greaterThan(0.01) && months < MAX_ITERATIONS) {
        const interest = balance.mul(monthlyRate).toDecimalPlaces(2);
        totalInterest = totalInterest.plus(interest);

        balance = balance.plus(interest).minus(payment);
        if (balance.lessThan(0)) {
            balance = new Decimal(0);
        }

        months++;
    }

    return {
        months,
        totalInterest: totalInterest.toDecimalPlaces(2).toNumber(),
        totalPaid: new Decimal(debtInfo.currentBalance).plus(totalInterest).toDecimalPlaces(2).toNumber()
    };
}

/**
 * Generate amortization schedule for a loan with improved precision
 */
export function generateAmortizationSchedule(
    loanInfo: LoanInfo,
    numberOfPayments: number = 12
): Array<{
    paymentNumber: number;
    paymentDate: Date;
    paymentAmount: number;
    principalPayment: number;
    interestPayment: number;
    remainingBalance: number;
}> {
    if (!loanInfo || !loanInfo.originalAmount) {
        return [];
    }

    const schedule = [];
    const payment = calculateLoanPayment(loanInfo);
    const periodicRate = getPeriodicRate(loanInfo.interestRate || 0, loanInfo.paymentFrequency || 'monthly');

    let balance = new Decimal(loanInfo.originalAmount);
    let paymentDate = new Date(loanInfo.startDate || new Date());

    for (let i = 1; i <= numberOfPayments; i++) {
        const interestPayment = balance.mul(periodicRate).toDecimalPlaces(2);
        let principalPayment = new Decimal(payment).minus(interestPayment);

        // Adjust last payment if needed
        if (principalPayment.greaterThan(balance)) {
            principalPayment = balance;
        }

        balance = balance.minus(principalPayment);

        // Add to schedule
        schedule.push({
            paymentNumber: i,
            paymentDate: new Date(paymentDate), // Clone date
            paymentAmount: payment,
            principalPayment: principalPayment.toNumber(),
            interestPayment: interestPayment.toNumber(),
            remainingBalance: balance.toDecimalPlaces(2).toNumber()
        });

        // Advance date according to payment frequency
        if (loanInfo.paymentFrequency === 'biweekly') {
            paymentDate.setDate(paymentDate.getDate() + 14);
        } else if (loanInfo.paymentFrequency === 'weekly') {
            paymentDate.setDate(paymentDate.getDate() + 7);
        } else if (loanInfo.paymentFrequency === 'quarterly') {
            paymentDate.setMonth(paymentDate.getMonth() + 3);
        } else if (loanInfo.paymentFrequency === 'annually') {
            paymentDate.setFullYear(paymentDate.getFullYear() + 1);
        } else {
            // Default to monthly
            paymentDate.setMonth(paymentDate.getMonth() + 1);
        }
    }

    return schedule;
}

/**
 * Calculate savings from extra payments with improved precision
 */
export function calculateExtraPaymentSavings(
    loanInfo: LoanInfo,
    extraPayment: number
): {
    monthsSaved: number;
    interestSaved: number;
    newPayoffDate: Date;
} {
    if (!loanInfo || !loanInfo.originalAmount || extraPayment <= 0) {
        return { monthsSaved: 0, interestSaved: 0, newPayoffDate: new Date() };
    }

    const originalPayment = calculateLoanPayment(loanInfo);
    const originalInterest = calculateTotalInterest(loanInfo);
    const originalPayoffDate = calculatePayoffDate(loanInfo);

    // Calculate with extra payment
    const newMonthlyPayment = new Decimal(originalPayment).plus(extraPayment).toNumber();

    // Precise amortization calculation
    const periodicRate = getPeriodicRate(loanInfo.interestRate || 0, loanInfo.paymentFrequency || 'monthly');
    const paymentsPerYear = getPaymentsPerYear(loanInfo.paymentFrequency || 'monthly');

    let balance = new Decimal(loanInfo.originalAmount);
    let months = 0;
    let totalInterest = new Decimal(0);

    const MAX_ITERATIONS = 1200; // Prevent infinite loops

    while (balance.greaterThan(0.01) && months < MAX_ITERATIONS) {
        const interestCharge = balance.mul(periodicRate).toDecimalPlaces(2);
        totalInterest = totalInterest.plus(interestCharge);

        const principalPayment = Decimal.min(
            new Decimal(newMonthlyPayment).minus(interestCharge),
            balance
        );

        balance = balance.minus(principalPayment);
        months++;
    }

    const monthsWithOriginalTerm = loanInfo.loanTerm || 12;
    const monthsSaved = Math.max(0, monthsWithOriginalTerm - months);

    const newPayoffDate = new Date(loanInfo.startDate || new Date());
    newPayoffDate.setMonth(newPayoffDate.getMonth() + months);

    return {
        monthsSaved,
        interestSaved: Math.max(0, originalInterest - totalInterest.toNumber()),
        newPayoffDate
    };
}

/**
 * Validate financial inputs
 */
export function validateFinancialInputs(
    amount: number,
    interestRate: number,
    term: number
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (amount <= 0) {
        errors.push('Amount must be greater than 0');
    }

    if (interestRate < 0 || interestRate > 100) {
        errors.push('Interest rate must be between 0% and 100%');
    }

    if (term <= 0 || term > 600) {
        errors.push('Term must be between 1 and 600 months');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Format currency with proper decimal handling
 */
export function formatCurrency(amount: number): string {
    return new Decimal(amount || 0)
        .toDecimalPlaces(2)
        .toNumber()
        .toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
}
