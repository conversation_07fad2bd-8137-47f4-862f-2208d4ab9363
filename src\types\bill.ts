// Enhanced bill tracking with comprehensive loan/debt support

export type BillType = 'regular' | 'loan' | 'debt';
export type PaymentFrequency = 'weekly' | 'biweekly' | 'monthly' | 'quarterly' | 'annually';
export type InterestType = 'simple' | 'compound' | 'apr';
export type LoanType = 'mortgage' | 'auto' | 'student' | 'personal' | 'business' | 'other';
export type DebtType = 'credit_card' | 'personal_loan' | 'line_of_credit' | 'other';

export interface LoanInfo {
  loanType: LoanType;
  originalAmount: number;
  remainingBalance: number;
  interestRate: number; // Annual percentage rate
  interestType: InterestType;
  loanTerm: number; // in months
  startDate: string;
  payoffDate?: string;
  paymentFrequency: PaymentFrequency;
  monthlyPayment?: number; // Calculated or user-provided
  extraPayment?: number; // Additional payment amount
  compoundingPeriod?: number; // Times per year (12 for monthly, 365 for daily)
}

export interface DebtInfo {
  debtType: DebtType;
  currentBalance: number;
  creditLimit?: number; // For credit cards
  minimumPayment: number;
  interestRate: number; // APR
  lastStatementDate?: string;
  paymentDueDate: string;
  minimumPaymentCalculation?: 'percentage' | 'fixed' | 'balance_based';
  minimumPaymentPercentage?: number; // e.g., 2% of balance
}

export interface PaymentHistoryEntry {
  date: string;
  amount: number;
  onTime: boolean;
  paymentType?: 'minimum' | 'extra' | 'full';
  interestCharged?: number;
  principalPaid?: number;
}

export interface Bill {
  id: string;
  userId?: string;
  name: string;
  amount: number;
  dueDate: string;
  category: string;
  billType: BillType;
  isRecurring?: boolean;
  frequency?: PaymentFrequency;
  reminderDays?: number;
  notes?: string;
  paidDate?: string;
  createdAt: string;
  updatedAt: string;
  vendor?: string;
  isPaid?: boolean;
  // Enhanced loan/debt support
  isLoan?: boolean;
  isDebt?: boolean;
  loanInfo?: LoanInfo;
  debtInfo?: DebtInfo;
  totalPaid?: number;
  paymentHistory?: PaymentHistoryEntry[];
  // Calculated fields
  calculatedPayment?: number;
  nextPaymentDate?: string;
  payoffDate?: string;
  totalInterest?: number;
  // Fields for True Renewal
  renewalOfBillId?: string;
  isRenewedOriginal?: boolean;
}

export interface BillFormData {
  name: string;
  amount: number;
  dueDate: string;
  category: string | null;
  billType: BillType;
  isRecurring?: boolean;
  frequency?: PaymentFrequency;
  reminderDays?: number;
  notes?: string;
  vendor?: string;
  isLoan?: boolean;
  isDebt?: boolean;
  loanInfo?: LoanInfo;
  debtInfo?: DebtInfo;
  tags?: string[];
}

export interface BillFilters {
  paidStatus?: 'paid' | 'unpaid';
  searchQuery?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

export const DEFAULT_BILL: BillFormData = {
  name: '',
  amount: 0,
  dueDate: new Date().toISOString().split('T')[0],
  category: 'Other',
  billType: 'regular',
  isRecurring: false,
  frequency: 'monthly',
  reminderDays: 3,
  notes: '',
  isLoan: false,
  isDebt: false
};

export const BILL_CATEGORIES = [
  'Housing',
  'Utilities',
  'Transportation',
  'Food',
  'Health',
  'Insurance',
  'Personal',
  'Entertainment',
  'Education',
  'Loans',
  'Credit Cards',
  'Debt',
  'Savings',
  'Subscriptions',
  'Technology',
  'Other'
];

export const COMMON_VENDORS = [
  'Netflix',
  'Amazon',
  'Spotify',
  'Apple',
  'Google',
  'AT&T',
  'Verizon',
  'Comcast',
  'T-Mobile',
  'Bank of America',
  'Chase',
  'Wells Fargo',
  'Citibank',
  'American Express',
  'Discover',
  'Capital One',
  'State Farm',
  'Progressive',
  'Geico',
  'Allstate'
];
