'use client';

import { ReactNode } from 'react';

interface BaseWidgetProps {
  title: string;
  children: ReactNode;
  icon?: React.ReactNode;
}

export const BaseWidget: React.FC<BaseWidgetProps> = ({ title, children, icon }) => {
  return (
    <div
      className="relative p-5 md:p-6 bg-card-light dark:bg-card-dark border border-border-light dark:border-border-dark rounded-2xl shadow-lg overflow-hidden"
    >
      <div className="flex items-center gap-2 mb-4">
        {icon && <span className="inline-flex items-center justify-center text-finance-green text-base md:text-lg" aria-hidden>{icon}</span>}
        <h3 className="font-serif text-xl md:text-2xl font-bold text-text-light dark:text-text-dark tracking-tight">{title}</h3>
      </div>
      <div className="relative z-10">{children}</div>
    </div>
  );
};