import { UserRule } from '@/types/rules'; // Import UserRule type
import { v4 as uuidv4 } from 'uuid'; // Import uuid
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface DashboardWidget {
  id: string;
  name: string;
  enabled: boolean;
  position: number;
  size: 'small' | 'medium' | 'large';
}

export interface OnboardingState {
  completed: boolean;
  currentStep: number;
  seenFeatures: string[];
  lastSeen: string | null;
}

export interface NotificationPreferences {
  // Bill reminders
  enableBillReminders: boolean;
  billReminderDays: number[]; // Days before due date to show reminder (e.g., [7, 3, 1, 0])
  billOverdueReminders: boolean; // Show reminders for overdue bills
  
  // Other notification settings
  showBadgeForRead: boolean; // Show badge for read notifications
  groupSimilarNotifications: boolean;
}

export interface UserPreferences {
  // Onboarding state
  onboarding: OnboardingState;

  // Dashboard preferences
  dashboardWidgets: DashboardWidget[];

  // Feature usage for smart defaults
  featureUsage: Record<string, number>;
  lastActions: Array<{ action: string, timestamp: number }>;

  // UI preferences
  compactMode: boolean;
  showAnimations: boolean;
  reducedMotion: boolean;
  highContrastMode: boolean;
  
  // Notification preferences
  notificationPreferences: NotificationPreferences;

  // Automation rules
  rules: UserRule[];
}

interface UserPreferencesStore extends UserPreferences {
  // Methods
  updateWidget: (widgetId: string, updates: Partial<DashboardWidget>) => void;
  reorderWidgets: (newOrder: DashboardWidget[]) => void;
  toggleWidget: (widgetId: string, enabled: boolean) => void;
  resetWidgetDefaults: () => void;

  // Onboarding methods
  advanceOnboarding: () => void;
  completeOnboarding: () => void;
  markFeatureSeen: (featureId: string) => void;

  // Feature usage tracking
  trackFeatureUsage: (featureId: string) => void;
  trackAction: (actionName: string) => void;

  // UI preference methods
  toggleCompactMode: () => void;
  toggleAnimations: () => void;
  setReducedMotion: (enabled: boolean) => void;
  setHighContrastMode: (enabled: boolean) => void;
  
  // Notification preference methods
  updateNotificationPreferences: (updates: Partial<NotificationPreferences>) => void;
  toggleBillReminders: (enabled: boolean) => void;
  setBillReminderDays: (days: number[]) => void;

  // Rule methods
  addRule: (rule: Omit<UserRule, 'id'>) => void;
  updateRule: (ruleId: string, updates: Partial<UserRule>) => void;
  deleteRule: (ruleId: string) => void;
}

// Default widgets that appear on the dashboard
const DEFAULT_WIDGETS: DashboardWidget[] = [
  {
    id: 'bill-calendar',
    name: 'Bill Calendar',
    enabled: true,
    position: 0,
    size: 'large'
  }
];

export const useUserPreferences = create<UserPreferencesStore>()(
  persist(
    (set, get) => ({
      // Initial state
      onboarding: {
        completed: false,
        currentStep: 0,
        seenFeatures: [],
        lastSeen: null
      },
      dashboardWidgets: [...DEFAULT_WIDGETS],
      featureUsage: {},
      lastActions: [],
      compactMode: false,
      showAnimations: true,
      reducedMotion: false,
      highContrastMode: false,
      notificationPreferences: {
        enableBillReminders: true,
        billReminderDays: [7, 3, 1, 0], // Remind 7 days before, 3 days before, 1 day before, and on due date
        billOverdueReminders: true,
        showBadgeForRead: true, // Initially show badge for read notifications
        groupSimilarNotifications: true
      },
      rules: [], // Initialize rules array

      // Methods for widgets
      updateWidget: (widgetId, updates) => {
        set((state) => ({
          dashboardWidgets: state.dashboardWidgets.map(widget =>
            widget.id === widgetId ? { ...widget, ...updates } : widget
          )
        }));
      },

      reorderWidgets: (newOrder) => {
        set(() => ({
          dashboardWidgets: newOrder.map((widget, index) => ({
            ...widget,
            position: index
          }))
        }));
      },

      toggleWidget: (widgetId, enabled) => {
        set((state) => ({
          dashboardWidgets: state.dashboardWidgets.map(widget =>
            widget.id === widgetId ? { ...widget, enabled } : widget
          )
        }));
      },

      resetWidgetDefaults: () => {
        set(() => ({
          dashboardWidgets: [...DEFAULT_WIDGETS]
        }));
      },

      // Onboarding methods
      advanceOnboarding: () => {
        set((state) => ({
          onboarding: {
            ...state.onboarding,
            currentStep: state.onboarding.currentStep + 1,
            lastSeen: new Date().toISOString()
          }
        }));
      },

      completeOnboarding: () => {
        set((state) => ({
          onboarding: {
            ...state.onboarding,
            completed: true,
            lastSeen: new Date().toISOString()
          }
        }));
      },

      markFeatureSeen: (featureId) => {
        set((state) => ({
          onboarding: {
            ...state.onboarding,
            seenFeatures: [...state.onboarding.seenFeatures, featureId],
            lastSeen: new Date().toISOString()
          }
        }));
      },

      // Feature usage tracking
      trackFeatureUsage: (featureId) => {
        set((state) => ({
          featureUsage: {
            ...state.featureUsage,
            [featureId]: (state.featureUsage[featureId] || 0) + 1
          }
        }));
      },

      trackAction: (actionName) => {
        set((state) => {
          const newActions = [
            { action: actionName, timestamp: Date.now() },
            ...state.lastActions.slice(0, 19) // Keep only last 20 actions
          ];

          return { lastActions: newActions };
        });
      },

      // UI preference methods
      toggleCompactMode: () => {
        set((state) => ({ compactMode: !state.compactMode }));
      },

      toggleAnimations: () => {
        set((state) => ({ showAnimations: !state.showAnimations }));
      },

      setReducedMotion: (enabled) => {
        set(() => ({ reducedMotion: enabled }));
      },

      setHighContrastMode: (enabled) => {
        set(() => ({ highContrastMode: enabled }));
      },

      // Notification preference methods
      updateNotificationPreferences: (updates) => {
        set((state) => ({
          notificationPreferences: {
            ...state.notificationPreferences,
            ...updates
          }
        }));
      },
      
      toggleBillReminders: (enabled) => {
        set((state) => ({
          notificationPreferences: {
            ...state.notificationPreferences,
            enableBillReminders: enabled
          }
        }));
      },
      
      setBillReminderDays: (days) => {
        set((state) => ({
          notificationPreferences: {
            ...state.notificationPreferences,
            billReminderDays: days
          }
        }));
      },

      // Rule methods
      addRule: (ruleData) => {
        const newRule: UserRule = {
          ...ruleData,
          id: uuidv4(), // Assign a unique ID
        };
        set((state) => ({
          rules: [...state.rules, newRule].sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0))
        }));
      },

      updateRule: (ruleId, updates) => {
        set((state) => ({
          rules: state.rules.map(rule =>
            rule.id === ruleId ? { ...rule, ...updates } : rule
          ).sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0))
        }));
      },

      deleteRule: (ruleId) => {
        set((state) => ({
          rules: state.rules.filter(rule => rule.id !== ruleId)
        }));
      },
    }),
    {
      name: 'user-preferences-storage'
    }
  )
);