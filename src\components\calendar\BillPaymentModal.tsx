'use client';

import { Bill } from '@/types/bill';
import { Dialog, Transition } from '@headlessui/react';
import { format, addMonths, parseISO } from 'date-fns';
import { Fragment, useState, useEffect } from 'react';

interface BillPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  bill: Bill | null;
  onMarkPaid?: (billId: string, updateDueDate: boolean, newDueDate?: string) => Promise<void>;
  onEditPaid?: (billId: string, updates: Partial<Bill>) => Promise<void>;
}

export function BillPaymentModal({ isOpen, onClose, bill, onMarkPaid, onEditPaid }: BillPaymentModalProps) {
  const [loading, setLoading] = useState(false);
  const [needsDueDateUpdate, setNeedsDueDateUpdate] = useState<boolean | null>(null);
  const [newDueDate, setNewDueDate] = useState('');
  const [paidDate, setPaidDate] = useState('');
  const [paidAmount, setPaidAmount] = useState('');
  const [paymentNotes, setPaymentNotes] = useState('');
  const [step, setStep] = useState<'initial' | 'update-due-date' | 'edit-payment' | 'done'>('initial');

  // Determine if the bill is likely a utility bill (variable amounts)
  const isUtilityOrVariable = bill?.category === 'Utilities' || 
                            bill?.category === 'Internet & Phone' || 
                            bill?.category === 'Food' ||
                            bill?.vendor?.toLowerCase().includes('electric') ||
                            bill?.vendor?.toLowerCase().includes('water') ||
                            bill?.vendor?.toLowerCase().includes('gas') ||
                            bill?.vendor?.toLowerCase().includes('internet') ||
                            bill?.vendor?.toLowerCase().includes('phone');

  // Calculate suggested next due date (one month from current due date)
  const suggestedNextDueDate = bill?.dueDate 
    ? format(addMonths(parseISO(bill.dueDate), 1), 'yyyy-MM-dd')
    : '';

  // Handle resetting the modal when it opens or closes
  const handleClose = () => {
    setStep('initial');
    setNeedsDueDateUpdate(null);
    setNewDueDate('');
    setPaidDate('');
    setPaidAmount('');
    setPaymentNotes('');
    onClose();
  };
  
  // Initialize form values when the modal opens with a bill
  useEffect(() => {
    if (isOpen && bill) {
      // If bill is already paid, set up for editing
      if (bill.paidDate) {
        setPaidDate(bill.paidDate);
        setPaidAmount(bill.amount.toString());
        setPaymentNotes(bill.notes || '');
        setStep('edit-payment');
      } else {
        // For unpaid bills, start with the standard flow
        setStep('initial');
        setPaidDate(new Date().toISOString().split('T')[0]);
        setPaidAmount(bill.amount.toString());
      }
      
      // Always set the suggested next due date
      if (bill.dueDate) {
        setNewDueDate(suggestedNextDueDate);
      }
    }
  }, [isOpen, bill, suggestedNextDueDate]);

  const handleMarkPaid = async () => {
    if (!bill) return;
    
    setLoading(true);
    
    try {
      // Handle editing already paid bills
      if (bill.paidDate && step === 'edit-payment') {
        if (onEditPaid) {
          await onEditPaid(bill.id, {
            paidDate,
            amount: parseFloat(paidAmount),
            notes: paymentNotes,
          });
          setStep('done');
        }
        return;
      }
      
      // Always prompt user to change due date after marking as paid
      if (step === 'initial') {
        setStep('update-due-date');
        setLoading(false);
        return;
      }
      
      // If we're at the update-due-date step, complete the process
      if (step === 'update-due-date') {
        if (onMarkPaid) {
          await onMarkPaid(
            bill.id, 
            needsDueDateUpdate === true, 
            needsDueDateUpdate === true ? newDueDate : undefined
          );
        }
        setStep('done');
      }
    } catch (error) {
      console.error('Error processing bill payment:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-40" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-gray-900 dark:text-white"
                >
                  {step === 'initial' && 'Mark Bill as Paid'}
                  {step === 'update-due-date' && 'Update Due Date?'}
                  {step === 'edit-payment' && 'Edit Payment Details'}
                  {step === 'done' && (bill?.paidDate ? 'Payment Updated' : 'Bill Marked as Paid')}
                </Dialog.Title>

                {step === 'initial' && (
                  <div className="mt-3">
                    <p className="text-sm text-gray-500 dark:text-gray-300">
                      Are you sure you want to mark &ldquo;{bill?.name}&rdquo; as paid?
                    </p>
                    
                    <div className="mt-4 flex items-center space-x-3">
                      <div className="flex-1">
                        <button
                          type="button"
                          className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                          onClick={handleClose}
                        >
                          Cancel
                        </button>
                      </div>
                      <div className="flex-1">
                        <button
                          type="button"
                          className="w-full rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                          onClick={handleMarkPaid}
                          disabled={loading}
                        >
                          {loading ? 'Processing...' : 'Mark as Paid'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {step === 'update-due-date' && (
                  <div className="mt-3">
                    <p className="text-sm text-gray-500 dark:text-gray-300">
                      Do you want to update the due date for &ldquo;{bill?.name}&rdquo;?
                    </p>
                    
                    <div className="mt-4 flex items-center space-x-3">
                      <div className="flex-1">
                        <button
                          type="button"
                          className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                          onClick={() => setNeedsDueDateUpdate(false)}
                        >
                          No
                        </button>
                      </div>
                      <div className="flex-1">
                        <button
                          type="button"
                          className="w-full rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                          onClick={() => setNeedsDueDateUpdate(true)}
                        >
                          Yes
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {step === 'edit-payment' && (
                  <div className="mt-3">
                    <p className="text-sm text-gray-500 dark:text-gray-300 mb-4">
                      Update the payment details for this bill.
                    </p>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Payment Date
                        </label>
                        <input
                          type="date"
                          className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 px-3 text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                          value={paidDate.split('T')[0]}
                          onChange={(e) => setPaidDate(e.target.value)}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Payment Amount
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 px-3 text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                          value={paidAmount}
                          onChange={(e) => setPaidAmount(e.target.value)}
                        />
                      </div>
                      
                      {needsDueDateUpdate && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            New Due Date
                          </label>
                          <input
                            type="date"
                            className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 px-3 text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                            value={newDueDate}
                            onChange={(e) => setNewDueDate(e.target.value)}
                          />
                        </div>
                      )}
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Payment Notes
                        </label>
                        <textarea
                          className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 px-3 text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                          rows={3}
                          value={paymentNotes}
                          onChange={(e) => setPaymentNotes(e.target.value)}
                          placeholder="Add any notes about this payment"
                        />
                      </div>
                    </div>
                    
                    <div className="mt-4 flex items-center space-x-3">
                      <div className="flex-1">
                        <button
                          type="button"
                          className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                          onClick={handleClose}
                        >
                          Cancel
                        </button>
                      </div>
                      <div className="flex-1">
                        <button
                          type="button"
                          className="w-full rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                          onClick={handleMarkPaid}
                          disabled={loading || (needsDueDateUpdate === true && !newDueDate) || needsDueDateUpdate === null}
                        >
                          {loading ? 'Processing...' : 'Continue'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {step === 'done' && (
                  <div className="mt-3">
                    <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900">
                      <svg
                        className="h-6 w-6 text-green-600 dark:text-green-300"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    <div className="mt-3 text-center">
                      <p className="text-sm text-gray-500 dark:text-gray-300">
                        {bill?.paidDate ? 'Payment details updated successfully.' : `${bill?.name} has been marked as paid.`}
                        {needsDueDateUpdate && newDueDate && (
                          <span> The next due date has been set to {format(parseISO(newDueDate), 'MMMM d, yyyy')}.</span>
                        )}
                      </p>
                      <div className="mt-4">
                        <button
                          type="button"
                          className="w-full rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                          onClick={handleClose}
                        >
                          Close
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
