{
  "compilerOptions": {
    "target": "ES2020",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "tsBuildInfoFile": ".next/cache/tsconfig.tsbuildinfo",
    "assumeChangesOnlyAffectDirectDependencies": true,
    "disableSourceOfProjectReferenceRedirect": true,
    "disableReferencedProjectLoad": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": [
        "./src/*"
      ]
    },
    "baseUrl": ".",
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": false,
    "exactOptionalPropertyTypes": false
  },
  "include": [
    "next-env.d.ts",
    "src/**/*.ts",
    "src/**/*.tsx",
    ".next/types/**/*.ts",
  ],
  "exclude": [
    "node_modules",
    "netlify/**/*",
    "functions/**/*",
    "scripts/**/*",
    "public/**/*",
    "**/*.test.*",
    "**/*.spec.*",
    "coverage/**/*",
    ".next/**/*",
    "out/**/*",
    "dist/**/*",
    ".turbo/**/*",
    "node_modules/googleapis/build/src/apis/**/*",
    "node_modules/firebase-admin/**/*",
    "node_modules/@firebase/**/*"
  ]
}