/* Browser Compatibility Fixes */

/* Text Size Adjust - Progressive Enhancement */
html {
  /* Modern browsers (Chrome 54+, Edge 79+) */
  text-size-adjust: 100%;

  /* Legacy WebKit browsers */
  -webkit-text-size-adjust: 100%;

  /* Legacy IE/Edge */
  -ms-text-size-adjust: 100%;
}

/* Firefox-specific fixes */
@-moz-document url-prefix() {

  /* Firefox doesn't support text-size-adjust, so we use font-size-adjust instead */
  html {
    font-size-adjust: 1;
  }

  /* Hide any elements that might show fetchpriority warnings */
  img[fetchpriority],
  link[fetchpriority] {
    /* These will be handled by JavaScript removal */
  }
}

/* Chrome/Chromium-specific optimizations */
@supports (text-size-adjust: 100%) {
  html {
    text-size-adjust: 100%;
  }
}

/* Safari-specific optimizations */
@supports (-webkit-text-size-adjust: 100%) {
  html {
    -webkit-text-size-adjust: 100%;
  }
}

/* Fallback for browsers that don't support any text-size-adjust */
@supports not ((text-size-adjust: 100%) or (-webkit-text-size-adjust: 100%)) {
  html {
    /* Use font-size and zoom controls instead */
    font-size: 100%;
    zoom: 1;
  }

  /* Prevent text inflation on mobile */
  body {
    font-size: 1rem;
    line-height: 1.5;
  }
}

/* Feature detection for fetchpriority support */
@supports (content: attr(fetchpriority)) {

  /* Browsers that support fetchpriority can use it */
  img[fetchpriority="high"] {
    /* Enhanced loading for supported browsers */
  }
}

/* Fallback for browsers without fetchpriority support */
@supports not (content: attr(fetchpriority)) {

  /* Use standard loading attributes */
  img[loading="eager"] {
    /* Standard eager loading */
  }
}

/* Theme color support detection */
@media (prefers-color-scheme: dark) {

  /* Only apply theme colors in browsers that support them */
  :root {
    --theme-color-fallback: #1f2937;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --theme-color-fallback: #ffffff;
  }
}

/* Progressive enhancement for modern CSS features */
@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    backdrop-filter: blur(10px);
  }
}

@supports not (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    /* Fallback for browsers without backdrop-filter */
    background-color: rgba(255, 255, 255, 0.8);
  }

  .dark .backdrop-blur {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* Container query fallbacks */
@supports (container-type: inline-size) {
  .container-query {
    container-type: inline-size;
  }
}

@supports not (container-type: inline-size) {
  .container-query {
    /* Use media queries as fallback */
  }
}

/* CSS Grid fallbacks */
@supports (display: grid) {
  .grid-layout {
    display: grid;
  }
}

@supports not (display: grid) {
  .grid-layout {
    /* Flexbox fallback */
    display: flex;
    flex-wrap: wrap;
  }

  .grid-layout>* {
    flex: 1 1 auto;
  }
}

/* Custom properties fallbacks */
@supports (color: var(--primary-color)) {
  .modern-colors {
    color: var(--primary-color);
  }
}

@supports not (color: var(--primary-color)) {
  .modern-colors {
    /* Static color fallback */
    color: #3b82f6;
  }
}

/* Scroll behavior fallbacks */
@supports (scroll-behavior: smooth) {
  html {
    scroll-behavior: smooth;
  }
}

@supports not (scroll-behavior: smooth) {

  /* JavaScript smooth scrolling will be used as fallback */
  html {
    scroll-behavior: auto;
  }
}

/* Focus-visible fallbacks */
@supports selector(:focus-visible) {
  .focus-modern:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  .focus-modern:focus:not(:focus-visible) {
    outline: none;
  }
}

@supports not selector(:focus-visible) {
  .focus-modern:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
}

/* Aspect ratio fallbacks */
@supports (aspect-ratio: 1) {
  .aspect-square {
    aspect-ratio: 1;
  }
}

@supports not (aspect-ratio: 1) {
  .aspect-square {
    position: relative;
    width: 100%;
  }

  .aspect-square::before {
    content: '';
    display: block;
    padding-top: 100%;
  }

  .aspect-square>* {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

/* Color scheme fallbacks */
@supports (color-scheme: light dark) {
  :root {
    color-scheme: light dark;
  }
}

@supports not (color-scheme: light dark) {

  /* Manual dark mode handling */
  .dark {
    background-color: #1f2937;
    color: #f9fafb;
  }
}

/* Logical properties fallbacks */
@supports (margin-inline-start: 1rem) {
  .margin-inline {
    margin-inline-start: 1rem;
    margin-inline-end: 1rem;
  }
}

@supports not (margin-inline-start: 1rem) {
  .margin-inline {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  [dir="rtl"] .margin-inline {
    margin-left: 1rem;
    margin-right: 1rem;
  }
}

/* Print styles for better compatibility */
@media print {

  /* Remove interactive elements that don't work in print */
  .no-print,
  button,
  input[type="button"],
  input[type="submit"],
  .modal,
  .tooltip {
    display: none !important;
  }

  /* Ensure good contrast for printing */
  * {
    color: #000 !important;
    background: #fff !important;
  }

  a {
    text-decoration: underline !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-width: 2px;
    --outline-width: 3px;
  }

  .focus-enhanced:focus-visible {
    outline-width: var(--outline-width);
  }

  .border {
    border-width: var(--border-width);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Forced colors mode (Windows High Contrast) */
@media (forced-colors: active) {
  .custom-colors {
    color: CanvasText;
    background-color: Canvas;
    border-color: CanvasText;
  }

  .interactive {
    color: LinkText;
  }

  .interactive:hover {
    color: VisitedText;
  }
}