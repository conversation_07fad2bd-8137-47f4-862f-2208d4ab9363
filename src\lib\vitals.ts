// Lightweight web-vitals reporting helper
// Usage: import { reportWebVitals } from '@/lib/vitals'; and export it from next app entry if needed
export function reportWebVitals(metric: any) {
    try {
        const body = JSON.stringify(metric);
        // Fire-and-forget; ignore result
        navigator.sendBeacon?.('/api/vitals', body) || fetch('/api/vitals', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body, keepalive: true });
    } catch { }
}
