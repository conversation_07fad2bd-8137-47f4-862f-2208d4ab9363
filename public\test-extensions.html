<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page - No Extensions</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 1rem;
            line-height: 1.6;
        }

        .success {
            color: green;
        }

        .error {
            color: red;
        }

        .info {
            color: blue;
        }
    </style>
</head>

<body>
    <h1>Browser Extension Test</h1>
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = new Date().toISOString() + ': ' + message;
            results.appendChild(div);
            console.log(message);
        }

        log('Test page loaded successfully', 'success');

        // Test customElements
        if (window.customElements) {
            log('customElements API available', 'success');

            // Try to define a test element
            try {
                class TestElement extends HTMLElement {
                    connectedCallback() {
                        this.textContent = 'Test element working';
                    }
                }
                window.customElements.define('test-element', TestElement);
                log('Successfully defined test-element', 'success');

                // Try to define the same element again (should fail)
                try {
                    window.customElements.define('test-element', TestElement);
                    log('ERROR: Should not be able to define same element twice!', 'error');
                } catch (e) {
                    log('Expected error when defining duplicate: ' + e.message, 'success');
                }

                // Test the problematic element name
                try {
                    class MCETextarea extends HTMLElement { }
                    window.customElements.define('mce-autosize-textarea', MCETextarea);
                    log('Successfully defined mce-autosize-textarea', 'success');
                } catch (e) {
                    log('Error defining mce-autosize-textarea: ' + e.message, 'error');
                }

            } catch (e) {
                log('Error defining test element: ' + e.message, 'error');
            }
        } else {
            log('customElements API not available', 'error');
        }

        // Add the test element to page
        const testEl = document.createElement('test-element');
        document.body.appendChild(testEl);
    </script>
</body>

</html>