'use client';

import React, { useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Bill, BillFormData } from '@/types/bill';
import { useBillsStore } from '@/stores/billsStore';
import { BillForm } from '@/components/bills/BillForm';
import { useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';
import { parseLocalDateString } from '@/utils/date';

// Inner component to handle form logic and access searchParams
function NewBillFormWrapper() {
  const { addBill, bills } = useBillsStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Ensure we have a valid bills array to prevent errors
  const safeBills = bills || [];

  // --- Get and parse the date parameter ---
  const dateParam = searchParams.get('date');
  let initialDueDate: Date | undefined = undefined;
  if (dateParam) {
    try {
      initialDueDate = parseLocalDateString(dateParam);
    } catch (error) {
      console.error(`Error parsing date parameter '${dateParam}':`, error);
    }
  }
  const initialData = initialDueDate ? { dueDate: initialDueDate.toISOString().split('T')[0] } : {};
  // --- End date parameter handling ---

  // BillForm supplies BillFormData; ensure required fields like billType are present
  const handleSave = (billData: BillFormData) => {
    setIsSubmitting(true);
    try {
      const newBillData: Omit<BillFormData, 'id'> = {
        name: billData.name || '',
        amount: billData.amount || 0,
        // Use the original dueDate from form data, which might have been changed by the user
        dueDate: billData.dueDate || new Date().toISOString().split('T')[0],
        category: billData.category || 'Other',
        billType: billData.billType ?? 'regular',
        isRecurring: billData.isRecurring || false,
        frequency: billData.frequency,
        reminderDays: billData.reminderDays || 3,
        notes: billData.notes,
        vendor: billData.vendor,
        isLoan: billData.isLoan,
        isDebt: billData.isDebt,
        loanInfo: billData.loanInfo,
        debtInfo: billData.debtInfo,
        tags: billData.tags,
      };
      addBill(newBillData);
      // Navigate back to calendar view after adding? Or keep /bills?
      // Let's go back to calendar for now as bills tab is removed.
      router.push('/calendar');
    } catch (error) {
      console.error('Error adding bill:', error);
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-6">
      <h1 className="sr-only">Add New Bill</h1>

      <div className="mb-6">
        <button
          onClick={handleCancel}
          className="flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back
        </button>
      </div>

      <BillForm
        onSave={handleSave}
        onCancel={handleCancel}
        allBills={safeBills}
        initialData={initialData}
      />

      {isSubmitting && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center gap-3">
            <div className="animate-spin h-6 w-6 border-4 border-primary border-t-transparent rounded-full"></div>
            <span>Saving bill...</span>
          </div>
        </div>
      )}
    </div>
  );
}

// Outer component to provide Suspense boundary
function AddBillPage() {
  return (
    <Suspense fallback={<div className="max-w-4xl mx-auto px-4 py-6 text-center">Loading form...</div>}>
      <NewBillFormWrapper />
    </Suspense>
  );
}

// Export as dynamic component with SSR disabled
export default dynamic(() => Promise.resolve(AddBillPage), { ssr: false });
