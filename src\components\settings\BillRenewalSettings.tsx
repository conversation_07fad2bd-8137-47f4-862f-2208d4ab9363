'use client';

import { BillRenewalOptions, DEFAULT_RENEWAL_OPTIONS } from '@/utils/billRenewal';
import { useEffect, useState } from 'react';

interface BillRenewalSettingsProps {
  onSave: (options: BillRenewalOptions) => void;
  initialOptions?: Partial<BillRenewalOptions>;
}

export function BillRenewalSettings({ onSave, initialOptions }: BillRenewalSettingsProps) {
  const [options, setOptions] = useState<BillRenewalOptions>({
    ...DEFAULT_RENEWAL_OPTIONS,
    ...initialOptions
  });

  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    const hasChanged = JSON.stringify(options) !== JSON.stringify({
      ...DEFAULT_RENEWAL_OPTIONS,
      ...initialOptions
    });
    setHasChanges(hasChanged);
  }, [options, initialOptions]);

  const handleChange = (field: keyof BillRenewalOptions, value: any) => {
    setOptions(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    onSave(options);
    setHasChanges(false);
  };

  const handleReset = () => {
    setOptions({
      ...DEFAULT_RENEWAL_OPTIONS,
      ...initialOptions
    });
  };

  return (
    <div className="space-y-6 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Bill Renewal Settings
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Configure how bills are automatically renewed and when to generate future bills.
        </p>
      </div>

      <div className="space-y-4">
        {/* Advance Months */}
        <div>
          <label htmlFor="advanceMonths" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Generate Bills in Advance
          </label>
          <div className="flex items-center space-x-3">
            <input
              type="number"
              id="advanceMonths"
              value={options.advanceMonths}
              onChange={(e) => handleChange('advanceMonths', parseInt(e.target.value) || 1)}
              min="1"
              max="12"
              className="w-20 px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:ring-2 focus:ring-primary transition-colors"
            />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              months in advance
            </span>
          </div>
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            How many months ahead to generate renewed bills
          </p>
        </div>

        {/* Auto-detect Recurring */}
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="autoDetectRecurring"
            checked={options.autoDetectRecurring}
            onChange={(e) => handleChange('autoDetectRecurring', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <div>
            <label htmlFor="autoDetectRecurring" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Auto-detect recurring bills
            </label>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Automatically identify bills that should be recurring based on name and category
            </p>
          </div>
        </div>

        {/* Respect Business Days */}
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="respectBusinessDays"
            checked={options.respectBusinessDays}
            onChange={(e) => handleChange('respectBusinessDays', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <div>
            <label htmlFor="respectBusinessDays" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Respect business days
            </label>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Move weekend due dates to the next Monday
            </p>
          </div>
        </div>

        {/* Skip One-time Bills */}
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="skipOneTime"
            checked={options.skipOneTime}
            onChange={(e) => handleChange('skipOneTime', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <div>
            <label htmlFor="skipOneTime" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Skip one-time bills
            </label>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Don&apos;t automatically renew bills marked as one-time payments
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={handleReset}
          disabled={!hasChanges}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Reset to Defaults
        </button>

        <button
          type="button"
          onClick={handleSave}
          disabled={!hasChanges}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Save Settings
        </button>
      </div>

      {/* Preview Section */}
      <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
          Current Settings Preview
        </h4>
        <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
          <div>• Bills will be generated {options.advanceMonths} month{options.advanceMonths !== 1 ? 's' : ''} in advance</div>
          <div>• Auto-detection of recurring bills: {options.autoDetectRecurring ? 'Enabled' : 'Disabled'}</div>
          <div>• Business day adjustment: {options.respectBusinessDays ? 'Enabled' : 'Disabled'}</div>
          <div>• Skip one-time bills: {options.skipOneTime ? 'Yes' : 'No'}</div>
        </div>
      </div>
    </div>
  );
}

export default BillRenewalSettings;
