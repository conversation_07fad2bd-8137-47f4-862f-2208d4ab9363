'use client';

import { auth } from '@/lib/firebase'; // Import auth instance
import { Bill } from '@/types/bill';

// TypeScript interface for notification options
interface CustomNotificationOptions {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  requireInteraction?: boolean;
  data?: Record<string, unknown>;
  actions?: Array<{ action: string, title: string, icon?: string }>; // Define actions property
  silent?: boolean;
  vibrate?: number[];
}

// Define subscription storage key
const SUBSCRIPTION_STORAGE_KEY = 'push-subscription';

/**
 * Modern service for handling notifications, including bill payment reminders
 * Supports both standard Web Notifications and Push API
 */
const notificationService = {
  /**
   * Request notification permissions from the user with improved error handling
   */
  async requestPermissions(): Promise<boolean> {
    // Check if we're in a browser environment with notification support
    if (typeof window === 'undefined' || !('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    try {
      if (Notification.permission === 'granted') {
        return true;
      }

      if (Notification.permission !== 'denied') {
        const permission = await Notification.requestPermission();

        // If permission granted, register service worker for push notifications
        if (permission === 'granted') {
          await this.registerServiceWorker();
        }

        return permission === 'granted';
      }

      return false;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  },

  /**
   * Register the service worker for push notifications
   */
  async registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      console.warn('Push notifications are not supported in this browser');
      return null;
    }

    try {
      // If already registered, return it
      const existing = await navigator.serviceWorker.getRegistration();
      if (existing) return existing;

      // Avoid double-registration races: load our central registration script if available
      const alreadyInjected = Array.from(document.getElementsByTagName('script')).some(s => s.src.includes('/register-sw.js'));
      if (!alreadyInjected) {
        try {
          const swRegScriptHead = await fetch('/register-sw.js', { method: 'HEAD' });
          if (swRegScriptHead.ok) {
            const script = document.createElement('script');
            script.src = '/register-sw.js';
            script.defer = true;
            document.head.appendChild(script);
          } else {
            // As a last resort, fall back to direct registration if the helper script is missing
            const swHead = await fetch('/custom-sw.js', { method: 'HEAD' });
            if (swHead.ok) {
              await navigator.serviceWorker.register('/custom-sw.js', { scope: '/' });
            } else {
              throw new Error('No service worker script available');
            }
          }
        } catch (e) {
          console.warn('Failed to inject register-sw.js, attempting direct SW registration', e);
          try {
            await navigator.serviceWorker.register('/custom-sw.js', { scope: '/' });
          } catch (innerErr) {
            console.error('Direct Service Worker registration failed:', innerErr);
            return null;
          }
        }
      }

      // Wait for the active controller/ready registration
      const ready = await navigator.serviceWorker.ready;
      return ready;
    } catch (error) {
      console.error('Service Worker registration orchestration failed:', error);
      return null;
    }
  },

  /**
   * Subscribe to push notifications using the Web Push API
   */
  async subscribeToPushNotifications(): Promise<PushSubscription | null> {
    try {
      const registration = await navigator.serviceWorker.ready;

      // Get the push subscription if it exists
      let subscription = await registration.pushManager.getSubscription();

      // If no subscription exists, create a new one
      if (!subscription) {
        const vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;

        if (!vapidPublicKey) {
          console.error('VAPID public key is missing');
          return null;
        }

        // Convert the base64 VAPID key to a Uint8Array
        const convertedVapidKey = this.urlBase64ToUint8Array(vapidPublicKey) as unknown as BufferSource;

        // Create a new subscription
        subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: convertedVapidKey
        });

        // Save the subscription to the server
        await this.saveSubscription(subscription);

        // Cache the subscription in local storage
        localStorage.setItem(SUBSCRIPTION_STORAGE_KEY, JSON.stringify(subscription.toJSON()));
      }

      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  },

  /**
   * Save the push subscription to the server
   */
  async saveSubscription(subscription: PushSubscription): Promise<boolean> {
    // Get the current user
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error('User not logged in. Cannot save subscription.');
      return false; // Or throw an error, depending on desired behavior
    }

    try {
      // Get the ID token
      const idToken = await currentUser.getIdToken();

      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        // API expects the PushSubscription object at the top level
        body: JSON.stringify(subscription),
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to save subscription to server:', error);
      return false;
    }
  },

  /**
   * Helper function to convert base64 VAPID key to Uint8Array
   */
  urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  },

  /**
   * Check if notifications are supported and permission is granted
   */
  isEnabled(): boolean {
    try {
      return (
        typeof window !== 'undefined' &&
        'Notification' in window &&
        Notification.permission === 'granted'
      );
    } catch (error) {
      console.warn('Error checking notification status:', error);
      return false;
    }
  },

  /**
   * Send a notification with improved reliability and options
   */
  async sendNotification(options: CustomNotificationOptions): Promise<Notification | null> {
    if (!this.isEnabled()) {
      console.warn('Notifications are not enabled');
      return null;
    }

    try {
      // Set defaults for consistent notifications
      const notificationOptions: NotificationOptions = {
        body: options.body,
        icon: options.icon ?? '/icons/icon-192x192.png',
        badge: options.badge ?? '/icons/icon-72x72.svg',
        tag: options.tag,
        requireInteraction: options.requireInteraction ?? false,
        data: options.data,
        silent: options.silent,
        // vibrate and actions are NOT valid in NotificationOptions in window context
      };

      // Try to use service worker for notifications first if available
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        const registration = await navigator.serviceWorker.ready;
        // Add vibrate and actions only for service worker notifications
        const swOptions: NotificationOptions & { actions?: any; vibrate?: any } = {
          ...notificationOptions,
          actions: options.actions,
          vibrate: options.vibrate ?? [200, 100, 200],
        };
        await registration.showNotification(options.title, swOptions);
        // Return a dummy Notification object for API consistency
        return new Notification(options.title, notificationOptions);
      }
      // Fall back to standard notification API (no vibrate/actions)
      const notification = new Notification(options.title, notificationOptions);
      notification.onclick = (event) => {
        event.preventDefault();
        if (window.parent) window.parent.focus();
        window.focus();
        if (options.data && options.data.url) {
          window.location.href = options.data.url as string;
        }
        notification.close();
      };
      return notification;
    } catch (error) {
      console.error('Failed to send notification:', error);
      return null;
    }
  },

  /**
   * Send a bill due reminder with action buttons
   */
  sendBillDueReminder(bill: Bill): Promise<Notification | null> {
    const dueDate = new Date(bill.dueDate).toLocaleDateString(undefined, {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });

    return this.sendNotification({
      title: `Bill Reminder: ${bill.name}`,
      body: `Your ${bill.name} bill of $${bill.amount.toFixed(2)} is due on ${dueDate}`,
      requireInteraction: true,
      tag: `bill-reminder-${bill.id}`,
      data: {
        billId: bill.id,
        url: `/bills/${bill.id}` // URL to navigate to when notification is clicked
      },
      actions: [
        {
          action: 'view',
          title: 'View Bill'
        },
        {
          action: 'mark-paid',
          title: 'Mark as Paid'
        }
      ]
    });
  },

  /**
   * Check for bills due in the next few days based on reminder settings
   * with improved error handling and throttling
   */
  async checkDueReminders(bills: Bill[]): Promise<void> {
    // Skip notifications in server-side rendering or if not enabled
    if (typeof window === 'undefined' || !bills || bills.length === 0) {
      return;
    }

    // Request permissions if not already granted
    if (!this.isEnabled()) {
      const granted = await this.requestPermissions();
      if (!granted) return;
    }

    // Safety check to prevent errors in production
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get the last notification timestamp to prevent too many notifications
      const lastNotified = localStorage.getItem('last-notification-check');
      if (lastNotified) {
        const lastCheck = new Date(parseInt(lastNotified, 10));
        // Only check once per hour
        if ((today.getTime() - lastCheck.getTime()) < (60 * 60 * 1000)) {
          return;
        }
      }

      // Update last notification check time
      localStorage.setItem('last-notification-check', today.getTime().toString());

      // Filter for unpaid bills that are due within the reminder period
      const dueBills = bills.filter((bill) => {
        // Skip bills that have been paid
        if (bill.paidDate) return false;

        const dueDate = new Date(bill.dueDate);
        dueDate.setHours(0, 0, 0, 0);

        const daysUntilDue = Math.floor(
          (dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
        );

        // Use a default of 3 days if reminderDays is undefined
        const reminderDays = bill.reminderDays ?? 3;
        return daysUntilDue <= reminderDays && daysUntilDue >= 0;
      });

      // Group notifications if there are multiple bills due
      if (dueBills.length > 3) {
        // Send a summary notification instead
        await this.sendNotification({
          title: `${dueBills.length} Bills Due Soon`,
          body: `You have ${dueBills.length} bills due in the next few days. Tap to view them.`,
          requireInteraction: true,
          data: { url: '/bills' },
          tag: 'bills-summary'
        });
      } else {
        // Send individual notifications with a slight delay between each
        for (const bill of dueBills) {
          await this.sendBillDueReminder(bill);
          // Add a small delay to prevent notification flood
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    } catch (error) {
      console.warn('Error checking due reminders:', error);
    }
  },

  /**
   * Register background sync for offline support
   */
  async registerBackgroundSync(): Promise<boolean> {
    if (!('serviceWorker' in navigator) || !('SyncManager' in window)) {
      console.warn('Background sync not supported');
      return false;
    }

    try {
      const registration = await navigator.serviceWorker.ready;

      // Check if SyncManager is available in this browser
      if ('sync' in registration) {
        // TypeScript doesn't recognize sync by default, use type assertion
        await (registration as any).sync.register('sync-bills');
        return true;
      } else {
        console.warn('Background sync API not available');
        return false;
      }
    } catch (error) {
      console.error('Background sync registration failed:', error);
      return false;
    }
  }
};

export default notificationService;
