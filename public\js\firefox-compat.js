// Firefox compatibility script - removes unsupported features for Firefox
(function () {
    'use strict';

    // Detect Firefox
    const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');

    if (isFirefox) {
        // Remove theme-color meta tag for Firefox to prevent console warnings
        const themeColorMeta = document.querySelector('meta[name="theme-color"]');
        if (themeColorMeta) {
            themeColorMeta.remove();
            console.log('PayDay Pilot: Removed theme-color meta tag for Firefox compatibility');
        }

        // Remove fetchpriority attributes from all images and links
        const elementsWithFetchPriority = document.querySelectorAll('img[fetchpriority], link[fetchpriority]');
        elementsWithFetchPriority.forEach(el => {
            el.removeAttribute('fetchpriority');
        });

        if (elementsWithFetchPriority.length > 0) {
            console.log(`PayDay Pilot: Removed fetchpriority from ${elementsWithFetchPriority.length} elements for Firefox compatibility`);
        }

        // Set up mutation observer to handle dynamically added elements
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // Element node
                        // Remove fetchpriority from newly added elements
                        if (node.hasAttribute && node.hasAttribute('fetchpriority')) {
                            node.removeAttribute('fetchpriority');
                        }
                        // Remove fetchpriority from children
                        const childrenWithFetchPriority = node.querySelectorAll && node.querySelectorAll('[fetchpriority]');
                        if (childrenWithFetchPriority) {
                            childrenWithFetchPriority.forEach(el => el.removeAttribute('fetchpriority'));
                        }
                        // Remove theme-color meta tags
                        if (node.tagName === 'META' && node.getAttribute('name') === 'theme-color') {
                            node.remove();
                        }
                    }
                });
            });
        });

        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });
    }
})();