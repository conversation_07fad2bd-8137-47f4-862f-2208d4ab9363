import { useAuth } from '@/hooks/useAuth';
import { arrayUnion, doc, updateDoc } from 'firebase/firestore';
import { getToken, onMessage } from 'firebase/messaging';
import { useEffect, useState } from 'react';
import { db, getMessaging as getMessagingService } from '../lib/firebase';

const VAPID_KEY = 'BPGISbLvwjX676dmPp7jHEbGTYDqPs1qUYEP6gNil39kNuWhgzAr-rGYKtyQxxkpFAo9BAIHv0NKCGheF8Hz0pI';

export function useFirebaseMessaging() {
    const { user } = useAuth();
    const [token, setToken] = useState<string | null>(null);
    const [permission, setPermission] = useState<NotificationPermission>('default');
    const [message, setMessage] = useState<any>(null);

    useEffect(() => {
        let unsubscribe: (() => void) | null = null;
        let isMounted = true;

        const initializeMessaging = async () => {
            try {
                const messaging = await getMessagingService();
                if (!messaging || !isMounted) {
                    console.log('Firebase Messaging not supported in this environment.');
                    return;
                }

                const perm = await Notification.requestPermission();
                if (!isMounted) return;

                setPermission(perm);

                if (perm === 'granted') {
                    try {
                        let serviceWorkerRegistration: ServiceWorkerRegistration | undefined = undefined;
                        if ('serviceWorker' in navigator) {
                            try {
                                serviceWorkerRegistration = await navigator.serviceWorker.ready;
                            } catch { }
                        }
                        const currentToken = await getToken(messaging, { vapidKey: VAPID_KEY, serviceWorkerRegistration });
                        if (!isMounted) return;

                        if (currentToken) {
                            console.log('FCM Token:', currentToken);
                            setToken(currentToken);

                            if (user) {
                                try {
                                    const userDocRef = doc(db, 'users', user.uid);
                                    await updateDoc(userDocRef, {
                                        fcmTokens: arrayUnion(currentToken)
                                    });
                                    console.log('FCM token saved to Firestore for user:', user.uid);
                                } catch (firestoreError) {
                                    console.error('Error saving FCM token to Firestore:', firestoreError);
                                }
                            } else {
                                console.log('User not logged in, cannot save FCM token.');
                            }
                        }
                    } catch (err) {
                        console.error('An error occurred while retrieving token: ', err);
                    }
                }

                // Set up message listener
                if (isMounted) {
                    unsubscribe = onMessage(messaging, (payload: any) => {
                        if (isMounted) {
                            setMessage(payload);
                        }
                    });
                }
            } catch (error) {
                console.error('Error initializing messaging:', error);
            }
        };

        initializeMessaging();

        return () => {
            isMounted = false;
            if (unsubscribe) {
                unsubscribe();
            }
        };
    }, [user]);

    return { token, permission, message };
}
