import * as admin from "firebase-admin";

let db: admin.firestore.Firestore;
let fcm: admin.messaging.Messaging;

try {
  const serviceAccountString = process.env.FIREBASE_SERVICE_ACCOUNT_KEY;
  if (!serviceAccountString) {
    throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set.");
  }
  const serviceAccount = JSON.parse(serviceAccountString);

  if (!admin.apps.length) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
    });
    console.log("Firebase Admin SDK initialized successfully.");
  }

  db = admin.firestore();
  fcm = admin.messaging();
  console.log("Firebase services (Firestore, FCM) are ready.");

} catch (error) {
  console.error("CRITICAL: Firebase Admin SDK initialization failed.", error);
  // Re-throw the error to ensure the function execution stops.
  // This prevents silent failures where the function might proceed with uninitialized services.
  throw new Error(`Firebase Admin SDK initialization failed: ${error instanceof Error ? error.message : String(error)}`);
}

export { db, fcm };
