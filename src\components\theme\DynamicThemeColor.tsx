'use client';

import { useTheme } from '@/stores/themeStore';
import { safeThemeColorUpdate } from '@/utils/browserCompat';
import { useEffect } from 'react';

/**
 * This component dynamically updates theme-related meta tags based on the user's
 * selected theme color. It runs only on the client side.
 */
export function DynamicThemeColor() {
  const { themeColor } = useTheme();

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Get the actual color value based on the theme color name
    const getThemeColorValue = (color: string): string => {
      switch (color) {
        case 'blue':
          return '#3880ff';
        case 'purple':
          return '#5260ff';
        case 'green':
          return '#2dd36f';
        case 'orange':
          return '#ff9500';
        case 'teal':
          return '#00b4d8';
        default:
          return '#3880ff';
      }
    };

    const colorValue = getThemeColorValue(themeColor);

    // Update theme-color meta tag using safe browser compatibility method
    safeThemeColorUpdate(colorValue);

    // Update msapplication-TileColor meta tag
    let tileColorMetaTag = document.querySelector('meta[name="msapplication-TileColor"]');
    if (tileColorMetaTag) {
      tileColorMetaTag.setAttribute('content', colorValue);
    } else {
      tileColorMetaTag = document.createElement('meta');
      tileColorMetaTag.setAttribute('name', 'msapplication-TileColor');
      tileColorMetaTag.setAttribute('content', colorValue);
      document.head.appendChild(tileColorMetaTag);
    }

    // Update manifest.json theme_color via a custom event
    // This won't directly modify the file but can be used by a service worker
    const event = new CustomEvent('theme-color-change', { detail: { color: colorValue } });
    window.dispatchEvent(event);

    console.log(`Theme color updated to: ${themeColor} (${colorValue})`);
  }, [themeColor]);

  // This component doesn't render anything visible
  return null;
}
