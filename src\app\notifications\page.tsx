'use client';

import { useBillsStore } from '@/stores/billsStore'; // Import useBillsStore
import { useUserPreferences } from '@/stores/userPreferencesStore';
import { differenceInDays, formatRelative, parseISO } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react'; // Added useCallback

// Define the notification interface
interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  date: string;
  read?: boolean;
  billId?: string;
}

// Define the Bill interface used for notifications
interface Bill {
  id: string;
  name: string;
  dueDate: string; 
}

export default function NotificationsPage() {
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [readStatusMap, setReadStatusMap] = useState<Record<string, boolean>>({});
  const [filter, setFilter] = useState<string>('all');
  const [notificationsCleared, setNotificationsCleared] = useState<boolean>(false);
  const { bills: billsFromStore, isLoading: isLoadingBills } = useBillsStore(); // Use bills from the store

  // Generate bill notifications based on due dates and user preferences
  const generateBillNotifications = useCallback(() => {
    if (isLoadingBills || !billsFromStore) {
      // console.log("generateBillNotifications: Bills are loading or not available yet.");
      return;
    }
    if (notificationsCleared) {
      // console.log("generateBillNotifications: Notifications were cleared, not generating.");
      setNotifications([]); // Ensure notifications are empty if cleared
      return;
    }

    try {
      const { notificationPreferences } = useUserPreferences.getState();
      const { enableBillReminders, billReminderDays, billOverdueReminders } = notificationPreferences;
      
      if (!enableBillReminders) {
        // console.log("generateBillNotifications: Bill reminders are disabled.");
        const nonBillNotifications = notifications.filter(n => n.type !== 'bill');
        setNotifications(nonBillNotifications);
        localStorage.setItem('notifications', JSON.stringify(nonBillNotifications));
        return;
      }
      
      const now = new Date();
      const generatedNotifications: Notification[] = [];
      
      billsFromStore.forEach((bill) => {
        if (bill.dueDate) {
          const dueDate = new Date(bill.dueDate);
          const daysUntilDue = differenceInDays(dueDate, now);
          
          // Skip overdue bills if user doesn't want to see them
          if (daysUntilDue < 0 && !billOverdueReminders) return;
          
          // Check if this bill should trigger a notification based on user preferences
          // For overdue bills or if the days until due matches a reminder day
          const shouldNotify = daysUntilDue < 0 || billReminderDays.includes(daysUntilDue);
          
          if (!shouldNotify) return;
          
          let message = '';
          
          if (daysUntilDue < 0) {
            message = `Your ${bill.name} bill was due ${Math.abs(daysUntilDue)} days ago.`;
          } else if (daysUntilDue === 0) {
            message = `Your ${bill.name} bill is due today.`;
          } else if (daysUntilDue === 1) {
            message = `Your ${bill.name} bill is due tomorrow.`;
          } else {
            message = `Your ${bill.name} bill is due in ${daysUntilDue} days.`;
          }
          
          generatedNotifications.push({
            id: `bill-${bill.id}-due-${bill.dueDate}`,
            title: daysUntilDue < 0 ? 'Bill Overdue' : 'Upcoming Bill',
            message,
            type: 'bill',
            date: now.toISOString(),
            billId: bill.id
          });
        }
      });

      // Merge with existing notifications
      setNotifications(prev => {
        const prevMap = new Map(prev.map(n => [n.id, n]));
        const merged = generatedNotifications.map(n => {
          const prevN = prevMap.get(n.id);
          // If a previous version exists, preserve the original date
          return prevN ? { ...n, date: prevN.date } : n;
        });
        
        // Add back any non-bill notifications
        prev.forEach(prevN => {
          if (prevN.type !== 'bill' && !merged.some(m => m.id === prevN.id)) {
            merged.push(prevN);
          }
        });

        // Save to localStorage
        localStorage.setItem('notifications', JSON.stringify(merged));
        return merged.sort((a, b) => parseISO(b.date).getTime() - parseISO(a.date).getTime()); // Sort by date descending
      });
    } catch (error) {
      console.error('Error generating bill notifications:', error);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps 
  }, [billsFromStore, isLoadingBills, notificationsCleared]); // Removed useUserPreferences, will use getState inside. Removed 'notifications' as direct dependency.

  // Load non-bill notifications and read status from localStorage on component mount
  // And trigger bill notification generation when billsFromStore is ready or preferences change
  useEffect(() => {
    const loadNonBillNotificationsAndStatus = () => {
      try {
        const savedNotifications = localStorage.getItem('notifications');
        const savedReadStatus = localStorage.getItem('notificationsReadStatus');
        const clearedStatus = localStorage.getItem('notificationsCleared');
        
        if (clearedStatus === 'true') {
          setNotificationsCleared(true);
          setNotifications([]); 
          return; 
        }
        
        if (savedNotifications) {
          // Load only non-bill notifications initially, bill notifications will be generated
          const allStoredNotifications: Notification[] = JSON.parse(savedNotifications);
          setNotifications(allStoredNotifications.filter(n => n.type !== 'bill'));
        }
        
        if (savedReadStatus) {
          setReadStatusMap(JSON.parse(savedReadStatus));
        }
      } catch (error) {
        console.error('Error loading non-bill notifications:', error);
      }
    };

    loadNonBillNotificationsAndStatus();
  }, []); // Run once on mount to load persistent state

  useEffect(() => {
    // Generate bill notifications when bills are loaded/changed or when cleared status changes
    if (!isLoadingBills && billsFromStore) {
       generateBillNotifications();
    }
  }, [billsFromStore, isLoadingBills, notificationsCleared, generateBillNotifications]);


  // Save read status to localStorage
  const saveReadStatus = (statusMap: Record<string, boolean>) => {
    localStorage.setItem('notificationsReadStatus', JSON.stringify(statusMap));
  };

  // Reset cleared status and regenerate notifications
  const regenerateNotifications = () => {
    setNotificationsCleared(false); // This will trigger the useEffect to call generateBillNotifications
    localStorage.setItem('notificationsCleared', 'false');
    // generateBillNotifications(); // No longer needed here, useEffect will handle it
  };

  // Filter notifications based on selected filter and cleared state
  const filteredNotifications = notificationsCleared 
    ? [] 
    : notifications.filter(notification => {
        if (filter === 'all') return true;
        if (filter === 'unread') return !readStatusMap[notification.id];
        return notification.type === filter;
      }).sort((a,b) => parseISO(b.date).getTime() - parseISO(a.date).getTime());

  // Mark notification as read
  const markAsRead = (id: string) => {
    // Update the read status map
    const updatedMap = { ...readStatusMap, [id]: true };
    setReadStatusMap(updatedMap);
    saveReadStatus(updatedMap);
  };

  // Mark all as read
  const markAllAsRead = () => {
    // Create a map with all notification IDs marked as read
    const allReadMap = { ...readStatusMap };
    notifications.forEach(notification => {
      allReadMap[notification.id] = true;
    });
    
    setReadStatusMap(allReadMap);
    saveReadStatus(allReadMap);
  };

  // Clear all notifications
  const clearAll = () => {
    // Empty the notifications array
    setNotifications([]);
    localStorage.setItem('notifications', '[]');
    
    // Mark notifications as explicitly cleared to prevent auto-regeneration
    setNotificationsCleared(true);
    localStorage.setItem('notificationsCleared', 'true');
    // Also clear the read status map from local storage as no notifications are present
    localStorage.removeItem('notificationsReadStatus'); // Or set to '{}'
    setReadStatusMap({}); // Clear in local state too
    
    // Trigger storage event to update badge count in other components
    window.dispatchEvent(new Event('storage'));
  };

  // Format date for display
  const formatDate = (dateString: string, format: 'relative' | 'standard' = 'standard') => {
    try {
      const date = parseISO(dateString);
      if (format === 'relative') {
        return formatRelative(date, new Date());
      }
      return date.toLocaleDateString();
    } catch (error) {
      return dateString;
    }
  };

  // Get notification type icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'bill':
        return (
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 dark:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'system':
        return (
          <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-600 dark:text-purple-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  // Get notification preferences for UI display
  const { notificationPreferences } = useUserPreferences();

  return (
    <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="mb-6">
        <h1 className="text-2xl font-serif font-bold mb-2">Notifications</h1>
        <p className="text-gray-600 dark:text-gray-400">Stay updated with your bills and important updates.</p>
        {!notificationPreferences.enableBillReminders && notificationsCleared && (
          <div className="mt-2 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg text-amber-800 dark:text-amber-200 text-sm">
            Bill reminders are currently disabled. <a href="/settings" className="underline">Enable them in settings</a> to receive bill notifications.
          </div>
        )}
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 md:p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
          <div className="inline-flex rounded-lg shadow-sm">
            <button
              type="button"
              onClick={() => setFilter('all')}
              className={`px-4 py-2 text-sm font-medium rounded-l-lg ${filter === 'all'
                ? 'bg-primary text-white'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
            >
              All
            </button>
            <button
              type="button"
              onClick={() => setFilter('unread')}
              className={`px-4 py-2 text-sm font-medium ${filter === 'unread'
                ? 'bg-primary text-white'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
            >
              Unread
            </button>
            <button
              type="button"
              onClick={() => setFilter('bill')}
              className={`px-4 py-2 text-sm font-medium rounded-r-lg ${filter === 'bill'
                ? 'bg-primary text-white'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
            >
              Bills
            </button>
          </div>

          <div className="flex gap-2">
            <button
              onClick={markAllAsRead}
              className="px-4 py-2 text-sm font-medium bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 shadow-sm"
            >
              Mark all as read
            </button>
            <button
              onClick={clearAll}
              className="px-4 py-2 text-sm font-medium bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-100 dark:hover:bg-red-800 shadow-sm"
            >
              Clear all
            </button>
            {notificationsCleared && (
              <button
                onClick={regenerateNotifications}
                className="px-4 py-2 text-sm font-medium bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800 shadow-sm"
              >
                Restore
              </button>
            )}
          </div>
        </div>

        {filteredNotifications.length > 0 ? (
          <div className="space-y-4">
            {filteredNotifications.map(notification => (
              <div
                key={notification.id}
                className={`bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm flex gap-4 ${!readStatusMap[notification.id] ? 'border-l-4 border-primary' : ''}`}
                onClick={() => markAsRead(notification.id)}
              >
                {getNotificationIcon(notification.type)}

                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium">{notification.title}</h3>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(notification.date, 'relative')}
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mt-1">{notification.message}</p>

                  {notification.billId && (
                    <div className="mt-2">
                      <a
                        href={`/bills/${notification.billId}`}
                        className="text-sm text-primary dark:text-primary-light hover:underline"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/bills/${notification.billId}`);
                        }}
                      >
                        View Bill
                      </a>
                    </div>
                  )}
                </div>

                {!readStatusMap[notification.id] && (
                  <div className="w-2 h-2 rounded-full bg-primary flex-shrink-0 mt-2"></div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-sm text-center">
            <div className="inline-flex items-center justify-center p-4 bg-gray-100 dark:bg-gray-700 rounded-full mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-1">No notifications</h3>
            <p className="text-gray-500 dark:text-gray-400">
              {filter === 'all'
                ? "You don't have any notifications yet."
                : `You don't have any ${filter} notifications.`}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
