# Migrate Bitbucket repo to GitHub with full history (mirror)
param(
  [string]$BitbucketUrl = 'https://bitbucket.org/blackinsurance89/payday-pilot-next.git',
  [string]$GithubOwner = 'JD011089',
  [string]$RepoName = 'payday-pilot-next',
  [switch]$Private
)

$ErrorActionPreference = 'Stop'

function Exec($cmd, $argList) {
  Write-Host "`n> $cmd $argList" -ForegroundColor DarkGray
  $p = Start-Process -FilePath $cmd -ArgumentList $argList -NoNewWindow -PassThru -Wait
  if ($p.ExitCode -ne 0) { throw "Command failed: $cmd $argList (ExitCode=$($p.ExitCode))" }
}

# Resolve gh path (use absolute path to avoid PATH issues)
$GhExe = Join-Path ${env:ProgramFiles} 'GitHub CLI\gh.exe'
if (!(Test-Path $GhExe)) {
  # Fallback to gh in PATH
  $GhExe = 'gh'
}

$FullRepo = "$GithubOwner/$RepoName"
$MirrorRoot = "repo-mirror-migration"
$MirrorDir = Join-Path $MirrorRoot "$RepoName.git"

Write-Host "==> Verifying GitHub CLI (gh)" -ForegroundColor Cyan
try {
  Exec $GhExe '--version'
}
catch {
  Write-Error "GitHub CLI (gh) not found. Please install it (winget install GitHub.cli) and re-run."
  exit 1
}

Write-Host "==> Checking GitHub auth status" -ForegroundColor Cyan
try {
  Exec $GhExe 'auth status'
}
catch {
  Write-Error "GitHub CLI not authenticated. Run: gh auth login"
  exit 1
}

Write-Host "==> Ensuring GitHub repo exists: $FullRepo (private)" -ForegroundColor Cyan
$repoExists = $false
try {
  & $GhExe repo view $FullRepo --json name | Out-Null
  $repoExists = $true
}
catch {
  $repoExists = $false
}
if (-not $repoExists) {
  # Create private repo under user account
  Exec $GhExe "repo create $FullRepo --private -y"
}
else {
  Write-Host "Repository already exists. Continuing..." -ForegroundColor Yellow
}

# Prepare mirror working directory
if (!(Test-Path $MirrorRoot)) { New-Item -ItemType Directory -Path $MirrorRoot | Out-Null }

if (Test-Path $MirrorDir) {
  Write-Host "Existing mirror found at $MirrorDir. Removing to start clean..." -ForegroundColor Yellow
  Remove-Item -Recurse -Force $MirrorDir
}

Write-Host "==> Mirror-cloning from Bitbucket: $BitbucketUrl" -ForegroundColor Cyan
Exec 'git' "clone --mirror `"$BitbucketUrl`" `"$MirrorDir`""

Set-Location $MirrorDir

Write-Host "==> Setting GitHub as origin: https://github.com/$FullRepo.git" -ForegroundColor Cyan
Exec 'git' "remote set-url origin https://github.com/$FullRepo.git"

Write-Host "==> Pushing all refs to GitHub (mirror)" -ForegroundColor Cyan
Exec 'git' 'push --mirror origin'

Write-Host "==> Verifying remote refs on GitHub" -ForegroundColor Cyan
& git ls-remote origin | Out-Host

Write-Host "`n✅ Migration completed successfully! Repo: https://github.com/$FullRepo" -ForegroundColor Green

