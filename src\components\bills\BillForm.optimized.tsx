'use client';

import {
    Bill,
    <PERSON><PERSON>L_CATEGORIES,
    BillFormData,
    DEFAULT_BILL,
    LoanInfo
} from '@/types/bill';
import { extractAmountFromDescription, extractDueDateFromString, suggestCategory, suggestTags } from '@/utils/billIntelligence';
import { detectBillTypeWithConfidence } from '@/utils/billRenewal';
import { detectDuplicates } from '@/utils/importUtils';
import { Combobox } from '@headlessui/react';
import { BanknotesIcon, CheckIcon, ChevronUpDownIcon, CreditCardIcon, DocumentTextIcon } from '@heroicons/react/20/solid';
import { memo, useCallback, useEffect, useId, useMemo, useReducer, useState } from 'react';

// Import optimized financial calculations
import {
    calculateLoanPayment,
    calculateMinimumPayment,
    formatCurrency
} from '@/utils/financialCalculations.optimized';

// Additional imports for enhanced functionality
import type { BillType } from '@/types/bill';

// Reusable form components
type FormFieldProps = {
    id: string;
    name: string;
    label: string;
    value: string | number;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
    required?: boolean;
    error?: string;
    type?: string;
    className?: string;
    prefix?: React.ReactNode;
    min?: string | number;
    step?: string | number;
    rows?: number;
    children?: React.ReactNode;
};

const FormField = memo(({
    id,
    name,
    label,
    value,
    onChange,
    onFocus,
    required = false,
    error,
    type = 'text',
    className = '',
    prefix,
    min,
    step,
    rows,
    children
}: FormFieldProps) => {
    const hasError = !!error;
    const isTextarea = type === 'textarea';
    const baseClasses = `w-full px-3 py-2 rounded-lg border ${hasError ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 dark:border-gray-600 focus:ring-primary'} focus:ring-2 transition-colors`;
    const describedBy = error ? `${id}-error` : undefined;

    return (
        <div className={className}>
            <label htmlFor={id} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {label} {required && <span className="text-red-500">*</span>}
            </label>

            <div className={prefix ? "relative" : ""}>
                {prefix && (
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500 dark:text-gray-400">
                        {prefix}
                    </span>
                )}

                {isTextarea ? (
                    <textarea
                        id={id}
                        name={name}
                        value={value}
                        onChange={onChange}
                        rows={rows || 3}
                        className={baseClasses}
                        aria-describedby={describedBy}
                    />
                ) : (
                    <input
                        type={type}
                        id={id}
                        name={name}
                        value={value}
                        onChange={onChange}
                        onFocus={onFocus}
                        required={required}
                        min={min}
                        step={step}
                        aria-required={required}
                        aria-invalid={hasError}
                        aria-describedby={describedBy}
                        className={`${baseClasses} ${prefix ? 'pl-8' : ''}`}
                    />
                )}

                {children}
            </div>

            {error && (
                <p id={describedBy} className="mt-1 text-sm text-red-500" role="alert">
                    {error}
                </p>
            )}
        </div>
    );
});

FormField.displayName = 'FormField';

type FormButtonProps = {
    type?: 'button' | 'submit';
    onClick?: () => void;
    disabled?: boolean;
    variant?: 'primary' | 'secondary';
    children: React.ReactNode;
    busy?: boolean;
    className?: string;
};

const FormButton = memo(({
    type = 'button',
    onClick,
    disabled = false,
    variant = 'primary',
    children,
    busy = false,
    className = ''
}: FormButtonProps) => {
    const baseClasses = variant === 'primary'
        ? 'bg-primary text-white hover:bg-primary-dark focus:ring-primary focus:ring-offset-2'
        : 'text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 focus:ring-gray-500';

    return (
        <button
            type={type}
            onClick={onClick}
            disabled={disabled || busy}
            aria-busy={busy}
            className={`px-4 py-2 rounded-lg transition-colors focus:outline-none focus:ring-2 ${baseClasses} ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
        >
            {busy ? 'Please wait...' : children}
        </button>
    );
});

FormButton.displayName = 'FormButton';

interface BillFormProps {
    bill?: Bill;
    onSave: (billData: BillFormData) => void;
    onCancel: () => void;
    allBills: Bill[];
    initialData?: Partial<BillFormData>;
}

interface LoanDetailsSectionProps {
    formData: BillFormData;
    fieldIds: Record<string, string>;
    errors: Partial<Record<keyof BillFormData, string>>;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    handleNumberFocus: (e: React.FocusEvent<HTMLInputElement>) => void;
    paymentBreakdown: { biweekly: number; weekly: number } | null;
}

type FormAction =
    | { type: 'SET_FIELD'; field: keyof BillFormData; value: any }
    | { type: 'SET_LOAN_FIELD'; field: string; value: any }
    | { type: 'INITIALIZE'; data: BillFormData }
    | { type: 'CLEAR_ERROR'; field: keyof BillFormData }
    | { type: 'SET_ERRORS'; errors: Partial<Record<keyof BillFormData, string>> };

type FormState = {
    data: BillFormData;
    errors: Partial<Record<keyof BillFormData, string>>;
    touched: boolean;
};

function formReducer(state: FormState, action: FormAction): FormState {
    switch (action.type) {
        case 'SET_FIELD':
            return {
                ...state,
                data: { ...state.data, [action.field]: action.value },
                touched: true,
                errors: { ...state.errors, [action.field]: undefined }
            };
        case 'SET_LOAN_FIELD':
            if (!state.data.loanInfo) return state;
            return {
                ...state,
                data: {
                    ...state.data,
                    loanInfo: { ...state.data.loanInfo, [action.field]: action.value }
                },
                touched: true
            };
        case 'INITIALIZE':
            return {
                ...state,
                data: action.data
            };
        case 'CLEAR_ERROR':
            const newErrors = { ...state.errors };
            delete newErrors[action.field];
            return { ...state, errors: newErrors };
        case 'SET_ERRORS':
            return { ...state, errors: action.errors };
        default:
            return state;
    }
}

// Cache for memoization of heavy calculations
const calculationCache = new Map();

export function BillForm({ bill, onSave, onCancel, allBills, initialData }: BillFormProps) {
    const isEditMode = !!bill?.id;
    const formId = useId();

    // Initialize form state with reducer for better state management
    const [formState, dispatch] = useReducer(formReducer, {
        data: useMemo(() => ({
            ...DEFAULT_BILL,
            ...(initialData || {}),
            ...(bill || {}),
        }), [bill, initialData]),
        errors: {},
        touched: false
    });

    const { data: formData, errors, touched: formTouched } = formState;

    // UI state - separate from form data
    const [uiState, setUiState] = useState({
        duplicateWarning: null as string | null,
        showLoanDetails: false,
        isSubmitting: false,
        aiHints: null as {
            category?: string;
            dueDate?: string;
            amount?: number;
            tags?: string[];
            duplicate?: string;
            recurring?: { isRecurring: boolean; frequency: string; confidence: number };
            billTypeDetection?: { suggestions: string[]; confidence: number };
        } | null
    });

    const { duplicateWarning, showLoanDetails, isSubmitting, aiHints } = uiState;

    // Helper function to update UI state
    const updateUiState = useCallback((updates: Partial<typeof uiState>) => {
        setUiState(prev => ({ ...prev, ...updates }));
    }, []);

    // Check if bill type is loan or debt
    const isLoanOrDebtType = useMemo(() => {
        return formData.billType === 'loan' || formData.billType === 'debt';
    }, [formData.billType]);

    // Initialize loan info when bill type changes to loan or debt
    useEffect(() => {
        updateUiState({ showLoanDetails: isLoanOrDebtType });

        if (isLoanOrDebtType && !formData.loanInfo) {
            const oneYearFromNow = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            const today = new Date().toISOString().split('T')[0];

            dispatch({
                type: 'SET_FIELD',
                field: 'isLoan',
                value: formData.billType === 'loan'
            });

            dispatch({
                type: 'SET_FIELD',
                field: 'isDebt',
                value: formData.billType === 'debt'
            });

            dispatch({
                type: 'SET_FIELD',
                field: 'loanInfo',
                value: {
                    originalAmount: formData.amount || 0,
                    remainingBalance: formData.amount || 0,
                    interestRate: 0,
                    loanTerm: 12,
                    startDate: today,
                    payoffDate: oneYearFromNow,
                    paymentFrequency: 'monthly'
                }
            });
        }
    }, [isLoanOrDebtType, formData.loanInfo, formData.amount, formData.billType, updateUiState]);

    // Check for duplicate bill names - memoized check function
    const checkForDuplicateName = useCallback((name: string) => {
        if (!name) return false;

        return allBills.some(
            existingBill =>
                existingBill.name.toLowerCase() === name.toLowerCase() &&
                existingBill.id !== bill?.id
        );
    }, [allBills, bill?.id]);

    // Apply duplicate check with useEffect
    useEffect(() => {
        if (!formTouched || !formData.name) return;

        const isDuplicate = checkForDuplicateName(formData.name);

        updateUiState({
            duplicateWarning: isDuplicate
                ? `A bill with the name "${formData.name}" already exists.`
                : null
        });
    }, [formData.name, formTouched, checkForDuplicateName, updateUiState]);

    // Generate AI hints from form data - extracted as a pure function
    const generateAiHints = useCallback((data: BillFormData, isTouched: boolean) => {
        if (!isTouched) return null;

        const nameOrNotes = data.name || data.notes || '';
        const hints: typeof aiHints = {};

        // Suggest category
        const aiCategory = suggestCategory(nameOrNotes);
        if (aiCategory && aiCategory !== data.category) {
            hints.category = aiCategory;
        }

        // Extract due date
        const aiDueDate = extractDueDateFromString(nameOrNotes);
        if (aiDueDate && aiDueDate !== data.dueDate) {
            hints.dueDate = aiDueDate;
        }

        // Extract amount
        const aiAmount = extractAmountFromDescription(nameOrNotes);
        if (aiAmount && aiAmount !== data.amount) {
            hints.amount = aiAmount;
        }

        // Intelligent bill type detection
        const billTypeDetection = detectBillTypeWithConfidence(
            data.name,
            data.category || undefined,
            data.amount
        );

        if (billTypeDetection.pattern && billTypeDetection.confidence > 50) {
            // Suggest recurring status and frequency
            if (data.isRecurring === undefined || data.isRecurring !== billTypeDetection.pattern.isRecurring) {
                hints.recurring = {
                    isRecurring: billTypeDetection.pattern.isRecurring,
                    frequency: billTypeDetection.pattern.defaultFrequency,
                    confidence: billTypeDetection.confidence
                };
            }

            // Suggest category if not already suggested
            if (!hints.category && billTypeDetection.pattern.category &&
                billTypeDetection.pattern.category !== data.category) {
                hints.category = billTypeDetection.pattern.category;
            }
        }

        // Add bill type suggestions if confidence is reasonable
        if (billTypeDetection.suggestions.length > 0 && billTypeDetection.confidence > 30) {
            hints.billTypeDetection = {
                suggestions: billTypeDetection.suggestions,
                confidence: billTypeDetection.confidence
            };
        }

        // Suggest tags
        const aiTags = suggestTags(
            data.name,
            data.notes,
            aiCategory || (data.category === null ? undefined : data.category),
            data.isRecurring
        );
        if (aiTags.length > 0) {
            hints.tags = aiTags;
        }

        // Smarter duplicate detection
        const dupes = detectDuplicates(
            { name: data.name, amount: data.amount, dueDate: data.dueDate },
            allBills
        );
        if (dupes.length > 0) {
            hints.duplicate = `Possible duplicate: "${dupes[0].bill.name}" (score ${dupes[0].score})`;
        }

        return Object.keys(hints).length > 0 ? hints : null;
    }, [allBills]);

    // Apply AI hints with useEffect - but debounced to avoid excessive processing
    useEffect(() => {
        // Debounce AI hints generation to avoid excessive CPU usage on rapid typing
        const debounceTimeout = setTimeout(() => {
            const hints = generateAiHints(formData, formTouched);
            updateUiState({ aiHints: hints });
        }, 300); // 300ms debounce

        return () => clearTimeout(debounceTimeout);
    }, [
        formData,
        formTouched,
        generateAiHints,
        updateUiState
    ]);

    // Calculate payment using enhanced financial utilities with caching
    const calculatePayment = useCallback((loanInfo: LoanInfo, billType: BillType): number => {
        // Create a cache key based on the input parameters
        const cacheKey = `${billType}-${JSON.stringify(loanInfo)}`;

        // Check if calculation is already cached
        if (calculationCache.has(cacheKey)) {
            return calculationCache.get(cacheKey);
        }

        let result = 0;
        if (billType === 'debt' && formData.debtInfo) {
            result = calculateMinimumPayment(formData.debtInfo);
        } else if (billType === 'loan') {
            result = calculateLoanPayment(loanInfo);
        }

        // Cache the result
        calculationCache.set(cacheKey, result);
        return result;
    }, [formData.debtInfo]);

    // Parse value based on input type with proper validation
    const parseInputValue = useCallback((value: string, type: string) => {
        if (type === 'number') {
            // Handle empty string
            if (value === '' || value === null || value === undefined) {
                return 0;
            }

            // Parse the number and validate
            const parsed = parseFloat(value);

            // Check for invalid numbers
            if (isNaN(parsed) || !isFinite(parsed)) {
                return 0;
            }

            // Ensure positive values for amounts
            return Math.max(0, parsed);
        }

        // For string values, ensure they're not null/undefined
        return value ?? '';
    }, []);

    // Handle form field changes with validation
    const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;

        // Handle loan info fields
        if (name.startsWith('loanInfo.')) {
            const loanField = name.split('.')[1];
            const newValue = parseInputValue(value, type);

            dispatch({
                type: 'SET_LOAN_FIELD',
                field: loanField,
                value: newValue
            });

            // Use requestAnimationFrame to batch UI updates for better performance
            requestAnimationFrame(() => {
                if (['originalAmount', 'interestRate', 'loanTerm'].includes(loanField) && formData.loanInfo) {
                    const updatedLoanInfo = {
                        ...formData.loanInfo,
                        [loanField]: newValue
                    };

                    const monthlyPayment = calculateLoanPayment(updatedLoanInfo);

                    if (!isNaN(monthlyPayment)) {
                        dispatch({
                            type: 'SET_FIELD',
                            field: 'amount',
                            value: monthlyPayment
                        });
                    }
                }
            });
        } else {
            // Handle regular fields
            dispatch({
                type: 'SET_FIELD',
                field: name as keyof BillFormData,
                value: parseInputValue(value, type)
            });
        }
    }, [formData.loanInfo, parseInputValue]);

    // Validate form data - with comprehensive validation
    const validateForm = useCallback((): boolean => {
        const newErrors: Partial<Record<keyof BillFormData, string>> = {};

        // Name validation with improved error messages
        if (!formData.name || typeof formData.name !== 'string') {
            newErrors.name = 'Bill name is required';
        } else {
            const trimmedName = formData.name.trim();
            if (trimmedName === '') {
                newErrors.name = 'Bill name is required';
            } else if (trimmedName.length < 3) {
                newErrors.name = 'Bill name must be at least 3 characters';
            } else if (trimmedName.length > 100) {
                newErrors.name = 'Bill name must be less than 100 characters';
            }
        }

        // Amount validation with null/undefined checks
        if (formData.amount === null || formData.amount === undefined) {
            newErrors.amount = 'Amount is required';
        } else if (typeof formData.amount !== 'number' || isNaN(formData.amount) || !isFinite(formData.amount)) {
            newErrors.amount = 'Amount must be a valid number';
        } else if (formData.amount <= 0) {
            newErrors.amount = 'Amount must be greater than 0';
        } else if (formData.amount > 1000000) {
            newErrors.amount = 'Amount cannot exceed $1,000,000';
        }

        // Due date validation
        if (!formData.dueDate || typeof formData.dueDate !== 'string') {
            newErrors.dueDate = 'Due date is required';
        } else {
            const dueDate = new Date(formData.dueDate);
            if (isNaN(dueDate.getTime())) {
                newErrors.dueDate = 'Due date must be a valid date';
            }
        }

        // Category validation
        if (!formData.category || typeof formData.category !== 'string' || formData.category.trim() === '') {
            newErrors.category = 'Category is required';
        }

        // Loan validation with improved error specificity
        if (showLoanDetails && formData.loanInfo) {
            if (formData.loanInfo.originalAmount === null || formData.loanInfo.originalAmount === undefined) {
                newErrors.loanInfo = 'Original loan amount is required';
            } else if (typeof formData.loanInfo.originalAmount !== 'number' || isNaN(formData.loanInfo.originalAmount)) {
                newErrors.loanInfo = 'Original loan amount must be a valid number';
            } else if (formData.loanInfo.originalAmount <= 0) {
                newErrors.loanInfo = 'Original loan amount must be greater than 0';
            } else if (formData.loanInfo.loanTerm === null || formData.loanInfo.loanTerm === undefined) {
                newErrors.loanInfo = 'Loan term is required';
            } else if (typeof formData.loanInfo.loanTerm !== 'number' || isNaN(formData.loanInfo.loanTerm)) {
                newErrors.loanInfo = 'Loan term must be a valid number';
            } else if (formData.loanInfo.loanTerm <= 0) {
                newErrors.loanInfo = 'Loan term must be greater than 0';
            } else if (formData.loanInfo.interestRate === null || formData.loanInfo.interestRate === undefined) {
                newErrors.loanInfo = 'Interest rate is required';
            } else if (typeof formData.loanInfo.interestRate !== 'number' || isNaN(formData.loanInfo.interestRate)) {
                newErrors.loanInfo = 'Interest rate must be a valid number';
            } else if (formData.loanInfo.interestRate < 0) {
                newErrors.loanInfo = 'Interest rate cannot be negative';
            } else if (formData.loanInfo.interestRate > 100) {
                newErrors.loanInfo = 'Interest rate cannot exceed 100%';
            }
        }

        // Recurring bill validation
        if (formData.isRecurring) {
            if (!formData.frequency || typeof formData.frequency !== 'string') {
                newErrors.frequency = 'Frequency is required for recurring bills';
            }

            if (formData.reminderDays !== null && formData.reminderDays !== undefined) {
                if (typeof formData.reminderDays !== 'number' || isNaN(formData.reminderDays)) {
                    newErrors.reminderDays = 'Reminder days must be a valid number';
                } else if (formData.reminderDays < 0 || formData.reminderDays > 365) {
                    newErrors.reminderDays = 'Reminder days must be between 0 and 365';
                }
            }
        }

        dispatch({ type: 'SET_ERRORS', errors: newErrors });
        return Object.keys(newErrors).length === 0;
    }, [formData, showLoanDetails]);

    // Handle number input focus event
    const handleNumberFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
        e.target.select();
    }, []);

    const handleSubmit = useCallback(async (e: React.FormEvent) => {
        e.preventDefault();

        // Don't submit if duplicate warning is active
        if (duplicateWarning) {
            return;
        }

        updateUiState({ isSubmitting: true });

        // Validate form before submission
        if (validateForm()) {
            try {
                await onSave(formData);
            } catch (error) {
                console.error('Error saving bill:', error);
                // You could set a form-level error here
            } finally {
                updateUiState({ isSubmitting: false });
            }
        } else {
            updateUiState({ isSubmitting: false });
        }
    }, [formData, duplicateWarning, validateForm, onSave, updateUiState]);

    // Memoized payment breakdown with improved calculation
    const paymentBreakdown = useMemo(() => {
        if (!showLoanDetails || !formData.loanInfo) return null;

        const monthlyPayment = formData.amount;
        if (!monthlyPayment || monthlyPayment <= 0) return null;

        // More accurate calculations for payment frequencies
        return {
            biweekly: parseFloat((monthlyPayment * 12 / 26).toFixed(2)),
            weekly: parseFloat((monthlyPayment * 12 / 52).toFixed(2))
        };
    }, [showLoanDetails, formData.loanInfo, formData.amount]);

    // State for category search
    const [query, setQuery] = useState('');

    // Filter categories based on search query
    const filteredCategories = useMemo(() => {
        if (!query) return BILL_CATEGORIES;
        const searchTerm = query.toLowerCase();
        return BILL_CATEGORIES.filter(category =>
            category.toLowerCase().includes(searchTerm)
        );
    }, [query]);

    // Generate IDs for form fields
    const fieldIds = useMemo(() => ({
        name: `name-${formId}`,
        billType: `billType-${formId}`,
        category: `category-${formId}`,
        amount: `amount-${formId}`,
        dueDate: `dueDate-${formId}`,
        notes: `notes-${formId}`,
        loanOrigAmount: `loanOrigAmount-${formId}`,
        loanInterestRate: `loanInterestRate-${formId}`,
        loanTerm: `loanTerm-${formId}`,
        paymentFrequency: `paymentFrequency-${formId}`,
        startDate: `startDate-${formId}`
    }), [formId]);

    return (
        <form
            onSubmit={handleSubmit}
            className="space-y-6 bill-form-container p-6 animate-fadeIn"
            aria-label={isEditMode ? "Edit Bill Form" : "New Bill Form"}
            noValidate
        >
            {/* AI Hints Section */}
            {aiHints && (
                <div className="mb-4 p-3 rounded bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 text-xs text-blue-900 dark:text-blue-200 space-y-1">
                    {aiHints.category && <div> Suggested category: <b>{aiHints.category}</b> <button type="button" className="ml-2 px-2 py-1 underline text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded" onClick={() => dispatch({ type: 'SET_FIELD', field: 'category', value: aiHints.category! })}>Apply</button></div>}
                    {aiHints.dueDate && <div> Extracted due date: <b>{aiHints.dueDate}</b> <button type="button" className="ml-2 px-2 py-1 underline text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded" onClick={() => dispatch({ type: 'SET_FIELD', field: 'dueDate', value: aiHints.dueDate! })}>Apply</button></div>}
                    {aiHints.amount && <div> Extracted amount: <b>${aiHints.amount}</b> <button type="button" className="ml-2 px-2 py-1 underline text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded" onClick={() => dispatch({ type: 'SET_FIELD', field: 'amount', value: aiHints.amount! })}>Apply</button></div>}
                    {aiHints.recurring && (
                        <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 p-2 rounded">
                            <span className="text-green-800 dark:text-green-200">
                                💡 Detected as {aiHints.recurring.isRecurring ? 'recurring' : 'one-time'} bill ({aiHints.recurring.frequency}) - Confidence: {Math.round(aiHints.recurring.confidence)}%
                            </span>
                            <button
                                type="button"
                                className="ml-2 px-2 py-1 underline text-green-700 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-800 rounded"
                                onClick={() => {
                                    dispatch({ type: 'SET_FIELD', field: 'isRecurring', value: aiHints.recurring!.isRecurring });
                                    dispatch({ type: 'SET_FIELD', field: 'frequency', value: aiHints.recurring!.frequency });
                                }}
                            >
                                Apply
                            </button>
                        </div>
                    )}
                    {aiHints.billTypeDetection && (
                        <div className="bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-700 p-2 rounded">
                            <span className="text-purple-800 dark:text-purple-200">
                                🔍 Bill type suggestions: {aiHints.billTypeDetection.suggestions.join(', ')}
                            </span>
                        </div>
                    )}
                    {aiHints.tags && aiHints.tags.length > 0 && <div> Suggested tags: {aiHints.tags.map(tag => <span key={tag} className="inline-block bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded px-2 py-0.5 mr-1">{tag}</span>)}</div>}
                    {aiHints.duplicate && <div className="text-red-600 dark:text-red-400"> {aiHints.duplicate}</div>}
                </div>
            )}
            <FormField
                id={fieldIds.name}
                name="name"
                label="Bill Name"
                value={formData.name}
                onChange={handleChange}
                required
                error={errors.name || (duplicateWarning || undefined)}
            />

            {/* Bill Type Selector */}
            <div>
                <label htmlFor={fieldIds.billType} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Bill Type <span className="text-red-500">*</span>
                </label>
                <div className="grid grid-cols-3 gap-3">
                    {(['regular', 'loan', 'debt'] as BillType[]).map((type) => {
                        const isSelected = formData.billType === type;
                        const icons = {
                            regular: <DocumentTextIcon className="h-5 w-5" />,
                            loan: <BanknotesIcon className="h-5 w-5" />,
                            debt: <CreditCardIcon className="h-5 w-5" />
                        };
                        const labels = {
                            regular: 'Regular Bill',
                            loan: 'Loan',
                            debt: 'Debt/Credit'
                        };

                        return (
                            <button
                                key={type}
                                type="button"
                                onClick={() => dispatch({ type: 'SET_FIELD', field: 'billType', value: type })}
                                className={`
                  relative flex flex-col items-center justify-center p-4 rounded-xl border-2 transition-all duration-200
                  ${isSelected
                                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 text-gray-600 dark:text-gray-400'
                                    }
                  hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                  min-h-[80px] touch-manipulation
                `}
                            >
                                {icons[type]}
                                <span className="mt-2 text-sm font-medium">{labels[type]}</span>
                                {isSelected && (
                                    <div className="absolute top-2 right-2">
                                        <CheckIcon className="h-4 w-4 text-blue-600" />
                                    </div>
                                )}
                            </button>
                        );
                    })}
                </div>
            </div>

            <div>
                <label htmlFor={fieldIds.category} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Category <span className="text-red-500">*</span>
                </label>
                <Combobox as="div" value={formData.category} onChange={(value) => {
                    dispatch({ type: 'SET_FIELD', field: 'category', value });
                }} className="relative">
                    <div className="relative">
                        <Combobox.Input
                            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 pl-3 pr-10 shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm"
                            onChange={(event) => setQuery(event.target.value)}
                            displayValue={(category: string) => category}
                            placeholder="Search categories..."
                            aria-describedby="category-description"
                        />
                        <Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                            <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                        </Combobox.Button>
                    </div>

                    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-700 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                        {filteredCategories.length === 0 && query !== '' ? (
                            <div className="relative cursor-default select-none py-2 px-4 text-gray-700 dark:text-gray-300">
                                No categories found.
                            </div>
                        ) : (
                            filteredCategories.map((category) => (
                                <Combobox.Option
                                    key={category}
                                    value={category}
                                    className={({ active }) =>
                                        `relative cursor-default select-none py-2 pl-10 pr-4 ${active ? 'bg-primary-600 text-white' : 'text-gray-900 dark:text-gray-100'
                                        }`
                                    }
                                >
                                    {({ selected, active }) => (
                                        <>
                                            <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                                                {category}
                                            </span>
                                            {selected ? (
                                                <span className={`absolute inset-y-0 left-0 flex items-center pl-3 ${active ? 'text-white' : 'text-primary-600'
                                                    }`}>
                                                    <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                </span>
                                            ) : null}
                                        </>
                                    )}
                                </Combobox.Option>
                            ))
                        )}
                    </Combobox.Options>
                </Combobox>
                <p id="category-description" className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Type to search or select a category
                </p>
            </div>

            {/* Extracted LoanDetailsSection as a memoized component */}
            {showLoanDetails && <LoanDetailsSection
                formData={formData}
                fieldIds={fieldIds}
                errors={errors}
                handleChange={handleChange}
                handleNumberFocus={handleNumberFocus}
                paymentBreakdown={paymentBreakdown}
            />}

            {!showLoanDetails && (
                <FormField
                    id={fieldIds.amount}
                    name="amount"
                    label="Amount"
                    value={formData.amount ?? ''}
                    onChange={handleChange}
                    onFocus={handleNumberFocus}
                    type="number"
                    min="0"
                    step="0.01"
                    required
                    error={errors.amount}
                    prefix="$"
                />
            )}

            <FormField
                id={fieldIds.dueDate}
                name="dueDate"
                label="Due Date"
                value={formData.dueDate}
                onChange={handleChange}
                type="date"
                required
                error={errors.dueDate}
            />

            <FormField
                id={fieldIds.notes}
                name="notes"
                label="Notes"
                value={formData.notes || ''}
                onChange={handleChange}
                type="textarea"
                rows={3}
            />

            {/* Recurring Bill Controls */}
            <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Recurring Bill Settings</h3>

                <div className="flex items-center space-x-3">
                    <input
                        type="checkbox"
                        id="isRecurring"
                        name="isRecurring"
                        checked={formData.isRecurring || false}
                        onChange={(e) => dispatch({ type: 'SET_FIELD', field: 'isRecurring', value: e.target.checked })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isRecurring" className="text-sm text-gray-700 dark:text-gray-300">
                        This is a recurring bill
                    </label>
                </div>

                {formData.isRecurring && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label htmlFor="frequency" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Frequency
                            </label>
                            <select
                                id="frequency"
                                name="frequency"
                                value={formData.frequency || 'monthly'}
                                onChange={handleChange}
                                className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:ring-2 focus:ring-primary transition-colors"
                            >
                                <option value="weekly">Weekly</option>
                                <option value="biweekly">Bi-weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="quarterly">Quarterly</option>
                                <option value="annually">Annually</option>
                            </select>
                        </div>

                        <div>
                            <label htmlFor="reminderDays" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Reminder Days
                            </label>
                            <input
                                type="number"
                                id="reminderDays"
                                name="reminderDays"
                                value={formData.reminderDays || 3}
                                onChange={handleChange}
                                min="0"
                                max="30"
                                className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:ring-2 focus:ring-primary transition-colors"
                            />
                            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                Days before due date to send reminder
                            </p>
                        </div>
                    </div>
                )}
            </div>

            <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                    type="button"
                    onClick={onCancel}
                    className="btn-modern btn-secondary"
                >
                    Back
                </button>

                <div className="flex items-center gap-4">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                        <span className="text-red-500">*</span> Required field
                    </span>
                    <button
                        type="submit"
                        disabled={isSubmitting || !!duplicateWarning}
                        className={`btn-modern btn-primary ${(isSubmitting || !!duplicateWarning) ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                        {isSubmitting ? (
                            <div className="flex items-center gap-2">
                                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                                Saving...
                            </div>
                        ) : (
                            isEditMode ? 'Update Bill' : 'Add Bill'
                        )}
                    </button>
                </div>
            </div>
        </form>
    );
}

// Enhanced LoanDetailsSection component with debt support
const LoanDetailsSection = memo(function LoanDetailsSection({
    formData,
    fieldIds,
    errors,
    handleChange,
    handleNumberFocus,
    paymentBreakdown
}: LoanDetailsSectionProps) {
    const isDebt = formData.billType === 'debt';
    const isLoan = formData.billType === 'loan';

    return (
        <div className="loan-details-section space-y-6">
            <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {isDebt ? 'Debt Details' : 'Loan Details'}
                </h3>
                <div className="flex items-center gap-2">
                    <div className="text-xs px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full font-medium">
                        {formData.category}
                    </div>
                    <div className="text-xs px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full font-medium">
                        {isDebt ? 'Debt' : 'Loan'}
                    </div>
                </div>
            </div>

            <FormField
                id={fieldIds.loanOrigAmount}
                name="loanInfo.originalAmount"
                label="Original Amount"
                value={formData.loanInfo?.originalAmount ?? 0}
                onChange={handleChange}
                onFocus={handleNumberFocus}
                type="number"
                min="0"
                step="0.01"
                required
                error={errors.loanInfo}
                prefix="$"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                    id={fieldIds.loanInterestRate}
                    name="loanInfo.interestRate"
                    label="Interest Rate (%)"
                    value={formData.loanInfo?.interestRate ?? 0}
                    onChange={handleChange}
                    onFocus={handleNumberFocus}
                    type="number"
                    min="0"
                    step="0.01"
                    required
                />

                <FormField
                    id={fieldIds.loanTerm}
                    name="loanInfo.loanTerm"
                    label="Loan Term (months)"
                    value={formData.loanInfo?.loanTerm ?? 12}
                    onChange={handleChange}
                    onFocus={handleNumberFocus}
                    type="number"
                    min="1"
                    required
                />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label htmlFor={fieldIds.paymentFrequency} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Payment Frequency
                    </label>
                    <select
                        id={fieldIds.paymentFrequency}
                        name="loanInfo.paymentFrequency"
                        value={formData.loanInfo?.paymentFrequency ?? 'monthly'}
                        onChange={handleChange}
                        className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary transition-colors"
                    >
                        <option value="monthly">Monthly</option>
                        <option value="biweekly">Bi-weekly</option>
                        <option value="weekly">Weekly</option>
                    </select>
                </div>

                <FormField
                    id={fieldIds.startDate}
                    name="loanInfo.startDate"
                    label="Start Date"
                    value={formData.loanInfo?.startDate ?? new Date().toISOString().split('T')[0]}
                    onChange={handleChange}
                    type="date"
                />
            </div>

            <div className="payment-summary">
                <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-3 flex items-center gap-2">
                    <span className="text-lg">💰</span>
                    Payment Summary
                </h4>
                <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                        <span className="text-sm font-medium">
                            {isDebt ? 'Minimum payment:' : 'Monthly payment:'}
                        </span>
                        <span className="font-bold text-lg text-green-600 dark:text-green-400">
                            {formatCurrency(formData.amount)}
                        </span>
                    </div>

                    {paymentBreakdown && !isDebt && (
                        <div className="grid grid-cols-2 gap-3">
                            <div className="text-center p-2 bg-white/30 dark:bg-gray-800/30 rounded-lg">
                                <div className="text-xs text-gray-600 dark:text-gray-400">Bi-weekly</div>
                                <div className="font-semibold">{formatCurrency(paymentBreakdown.biweekly)}</div>
                            </div>
                            <div className="text-center p-2 bg-white/30 dark:bg-gray-800/30 rounded-lg">
                                <div className="text-xs text-gray-600 dark:text-gray-400">Weekly</div>
                                <div className="font-semibold">{formatCurrency(paymentBreakdown.weekly)}</div>
                            </div>
                        </div>
                    )}

                    <div className="flex justify-between items-center pt-3 border-t border-blue-200 dark:border-blue-700">
                        <span className="text-sm font-medium">
                            {isDebt ? 'Total debt:' : 'Total to be paid:'}
                        </span>
                        <span className="font-bold text-blue-700 dark:text-blue-300">
                            {formatCurrency(
                                isDebt
                                    ? (formData.loanInfo?.originalAmount ?? 0)
                                    : ((formData.loanInfo?.loanTerm ?? 0) * formData.amount)
                            )}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
});

LoanDetailsSection.displayName = 'LoanDetailsSection';

export default BillForm;
