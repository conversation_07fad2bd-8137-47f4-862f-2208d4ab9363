import type { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HandlerResponse } from "@netlify/functions";
import * as admin from "firebase-admin";
import { db, fcm } from "./utils/firebase-admin";

const handler: Handler = async (event: HandlerEvent, context: HandlerContext): Promise<HandlerResponse> => {
  const testUserId = event.queryStringParameters?.userId;
  const testTitle = event.queryStringParameters?.title || "Test Notification";
  const testBody = event.queryStringParameters?.body || "This is a test notification from PayDay Pilot!";

  if (!testUserId) {
    return { statusCode: 400, body: "Missing 'userId' query parameter." };
  }

  console.log(`Test FCM: Attempting to send notification to user: ${testUserId}`);

  try {
    const userDoc = await db.collection('users').doc(testUserId).get();
    if (!userDoc.exists) {
      console.log(`Test FCM: User ${testUserId} not found.`);
      return { statusCode: 404, body: `User ${testUserId} not found.` };
    }

    const userData = userDoc.data();
    const fcmTokens: string[] = userData?.fcmTokens || [];

    if (fcmTokens.length === 0) {
      console.log(`Test FCM: No FCM tokens for user ${testUserId}.`);
      return { statusCode: 404, body: `No FCM tokens for user ${testUserId}.` };
    }

    const payload: admin.messaging.MessagingPayload = {
      notification: {
        title: testTitle,
        body: testBody,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.svg',
        click_action: 'https://paydaypilot.org/dashboard'
      },
      data: {
        url: `https://paydaypilot.org/dashboard`,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.svg',
        timestamp: Date.now().toString(),
        type: 'test_notification'
      }
    };

    console.log(`Test FCM: Sending to tokens: ${fcmTokens.join(', ')}`);
    const response: admin.messaging.MessagingDevicesResponse = await fcm.sendToDevice(
      fcmTokens,
      payload,
      { timeToLive: 86400 }
    );

    console.log(`Test FCM: Response for ${testUserId}: Success: ${response.successCount}, Failure: ${response.failureCount}`);
    
    if (response.failureCount > 0) {
        response.results.forEach((result, index) => {
            if (!result.success) {
                console.error(`Test FCM: Failure for token ${fcmTokens[index].substring(0,20)}...: ${result.error?.code}`);
            }
        });
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: `Test notification sent to user ${testUserId}.`,
        successCount: response.successCount,
        failureCount: response.failureCount,
        results: response.results
      }),
    };

  } catch (error) {
    const errorMessage = (error instanceof Error) ? error.message : String(error);
    console.error(`Test FCM: Error for user ${testUserId}:`, errorMessage);
    return { statusCode: 500, body: `Error sending test notification: ${errorMessage}` };
  }
};

export { handler };

