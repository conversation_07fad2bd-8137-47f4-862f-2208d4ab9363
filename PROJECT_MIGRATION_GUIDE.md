# Project Migration Guide - Moving Outside OneDrive

## Why Migrate?

Your project is currently located in OneDrive, which causes several development issues:
- **EPERM errors** during builds due to sync conflicts
- **Slower file operations** due to OneDrive overhead
- **Permission issues** with temporary files and caches
- **Build inconsistencies** when OneDrive syncs during compilation

Moving to `C:\dev\` will eliminate these issues permanently.

## Migration Options

### Option 1: Automated PowerShell Script (Recommended)
```bash
npm run migrate:project
```

**What it does:**
- ✅ Stops OneDrive temporarily
- ✅ Cleans build artifacts before migration
- ✅ Copies project files efficiently (excludes node_modules, .next, etc.)
- ✅ Installs dependencies in new location
- ✅ Tests the build
- ✅ Restarts OneDrive

### Option 2: Simple Batch File
```bash
# Double-click or run:
scripts/migrate-project.bat
```

**What it does:**
- ✅ Basic project copy to C:\dev\payday-pilot-next
- ✅ Installs dependencies
- ✅ Tests build

### Option 3: Manual Migration

1. **Create target directory:**
   ```bash
   mkdir C:\dev
   ```

2. **Stop OneDrive temporarily:**
   - Press `Ctrl + Shift + Esc` to open Task Manager
   - Find "Microsoft OneDrive" and end the task

3. **Clean current project:**
   ```bash
   npm run fix:comprehensive
   ```

4. **Copy project files:**
   ```bash
   robocopy "C:\Users\<USER>\OneDrive\Escritorio\Apps\payday-pilot-next" "C:\dev\payday-pilot-next" /E /XD .git .next .turbo node_modules build-out out dist
   ```

5. **Navigate to new location:**
   ```bash
   cd C:\dev\payday-pilot-next
   ```

6. **Install dependencies:**
   ```bash
   npm install
   ```

7. **Test build:**
   ```bash
   npm run build
   ```

## Post-Migration Steps

### 1. Update Development Environment

**VS Code:**
- File → Open Folder → `C:\dev\payday-pilot-next`
- Update workspace settings if needed

**Other IDEs:**
- Update project path in your IDE settings
- Refresh project structure

### 2. Update Shortcuts and Bookmarks
- Update any desktop shortcuts
- Update browser bookmarks to localhost
- Update terminal/command prompt shortcuts

### 3. Update Git Remote (if needed)
```bash
cd C:\dev\payday-pilot-next
git remote -v  # Check current remotes
# If needed, update remote URL
```

### 4. Verify Everything Works
```bash
# Test development server
npm run dev

# Test production build
npm run build

# Test other scripts
npm run lint
npm test
```

### 5. Clean Up Old Location
**⚠️ Only after verifying everything works!**

1. **Keep a backup temporarily:**
   - Don't delete the OneDrive copy immediately
   - Wait a few days to ensure everything works

2. **When ready to clean up:**
   ```bash
   # Navigate to old location
   cd "C:\Users\<USER>\OneDrive\Escritorio\Apps"
   
   # Remove old project (be careful!)
   rmdir /s payday-pilot-next
   ```

## Benefits After Migration

### ✅ **Build Performance**
- No more EPERM errors
- Faster build times (no OneDrive sync overhead)
- Reliable webpack caching
- No permission conflicts

### ✅ **Development Experience**
- Faster file operations
- No sync delays when saving files
- Better IDE performance
- Consistent build results

### ✅ **System Performance**
- Reduced OneDrive sync load
- Less disk I/O during development
- Better memory usage
- Fewer background processes

## Troubleshooting

### If Migration Fails

1. **Run as Administrator:**
   ```bash
   # Right-click PowerShell → "Run as Administrator"
   powershell -ExecutionPolicy Bypass -File scripts/migrate-project.ps1
   ```

2. **Manual copy if robocopy fails:**
   ```bash
   xcopy "C:\Users\<USER>\OneDrive\Escritorio\Apps\payday-pilot-next" "C:\dev\payday-pilot-next" /E /I /H /Y
   ```

3. **Check disk space:**
   ```bash
   # Ensure C:\ has enough space (at least 2GB free)
   dir C:\ 
   ```

### If Build Still Fails After Migration

1. **Clean install:**
   ```bash
   cd C:\dev\payday-pilot-next
   rmdir /s node_modules
   del package-lock.json
   npm install
   ```

2. **Use safe build:**
   ```bash
   npm run build:windows-safe
   ```

3. **Check Node.js version:**
   ```bash
   node --version  # Should be 18.x or higher
   npm --version   # Should be 8.x or higher
   ```

## Alternative: WSL2 Setup

If you prefer a Linux-like environment:

1. **Install WSL2:**
   ```bash
   wsl --install
   ```

2. **After restart, set up project in WSL2:**
   ```bash
   # In WSL2 terminal
   cd /home/<USER>
   git clone https://bitbucket.org/blackinsurance89/payday-pilot-next.git
   cd payday-pilot-next
   npm install
   npm run build
   ```

## Maintenance

### Regular Tasks
- Keep the new location clean of build artifacts
- Run `npm run fix:comprehensive` if issues arise
- Update Node.js and npm regularly
- Monitor disk space in C:\dev\

### Backup Strategy
- Use Git for code backup (already configured)
- Consider excluding C:\dev\ from OneDrive sync
- Use external backup tools for the entire C:\dev\ folder if needed

## Success Indicators

✅ **You'll know the migration worked when:**
- `npm run build` completes without EPERM errors
- Build times are faster
- No OneDrive sync notifications during development
- File operations are more responsive
- Consistent build results across runs

The migration to `C:\dev\payday-pilot-next` will provide a much better development experience and eliminate the Windows permission issues you've been experiencing!
