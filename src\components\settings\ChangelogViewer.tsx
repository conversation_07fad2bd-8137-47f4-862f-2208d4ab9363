'use client';

import { useChangelogNotification } from '@/hooks/useChangelogNotification';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface VersionInfo {
  version: string;
  releaseDate: string;
  sections: {
    [key: string]: string[];
  };
}

interface ChangelogViewerProps {
  className?: string;
  showOnlyLatest?: boolean;
}

export function ChangelogViewer({ className = '', showOnlyLatest = false }: ChangelogViewerProps) {
  const [versions, setVersions] = useState<VersionInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { markChangelogAsRead } = useChangelogNotification();

  useEffect(() => {
    const fetchChangelog = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/CHANGELOG.md');
        if (!response.ok) {
          throw new Error(`Failed to fetch changelog: ${response.status} ${response.statusText}`);
        }

        const content = await response.text();
        if (!content || content.trim().length === 0) {
          throw new Error('Changelog file is empty');
        }

        const parsedVersions = parseChangelog(content);

        if (parsedVersions.length === 0) {
          throw new Error('No valid changelog entries found');
        }

        setVersions(showOnlyLatest ? parsedVersions.slice(0, 1) : parsedVersions);
      } catch (err) {
        console.error('Error fetching changelog:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while loading changelog';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchChangelog();
  }, [showOnlyLatest]);

  // Mark changelog as read when component mounts
  useEffect(() => {
    markChangelogAsRead();
  }, [markChangelogAsRead]);

  const parseChangelog = (content: string): VersionInfo[] => {
    const versions: VersionInfo[] = [];
    const lines = content.split('\n');

    let currentVersion: VersionInfo | null = null;
    let currentSection = '';

    for (const line of lines) {
      // Match version headers like "## [0.6.1]" or "## 0.6.1"
      const versionMatch = line.match(/^##\s*\[?(\d+\.\d+\.\d+)\]?/);
      if (versionMatch) {
        if (currentVersion) {
          versions.push(currentVersion);
        }

        currentVersion = {
          version: versionMatch[1],
          releaseDate: '',
          sections: {}
        };
        currentSection = ''; // Reset current section when starting a new version
        continue;
      }

      // Match release date like "**Released: December 19, 2024**"
      const dateMatch = line.match(/\*\*Released:\s*([^*]+)\*\*/);
      if (dateMatch && currentVersion) {
        currentVersion.releaseDate = dateMatch[1].trim();
        continue;
      }

      // Match section headers like "### Improved"
      const sectionMatch = line.match(/^###\s+(.+)/);
      if (sectionMatch && currentVersion) {
        currentSection = sectionMatch[1];
        // Ensure the section array is initialized
        if (!currentVersion.sections[currentSection]) {
          currentVersion.sections[currentSection] = [];
        }
        continue;
      }

      // Match list items like "- **Dashboard Text Alignment**: ..."
      const itemMatch = line.match(/^-\s+(.+)/);
      if (itemMatch && currentVersion && currentSection && currentVersion.sections[currentSection]) {
        // Additional safety check to ensure the array exists before pushing
        currentVersion.sections[currentSection].push(itemMatch[1]);
      }
    }

    // Add the last version
    if (currentVersion) {
      versions.push(currentVersion);
    }

    return versions;
  };

  const formatReleaseDate = (dateString: string): string => {
    try {
      // Remove emoji and extra characters
      const cleanDate = dateString.replace(/[^\w\s,]/g, '').trim();
      const date = new Date(cleanDate);

      if (isNaN(date.getTime())) {
        return dateString; // Return original if parsing fails
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getSectionIcon = (sectionName: string): string => {
    const icons: { [key: string]: string } = {
      'New Features': '🎉',
      'Added': '➕',
      'Improved': '✨',
      'Changed': '🔄',
      'Fixed': '🐛',
      'Removed': '🗑️',
      'Security': '🔒',
      'Technical': '⚙️',
      'Performance': '⚡'
    };

    return icons[sectionName] || '📝';
  };

  const getSectionColor = (sectionName: string): string => {
    const colors: { [key: string]: string } = {
      'New Features': 'text-green-600 dark:text-green-400',
      'Added': 'text-blue-600 dark:text-blue-400',
      'Improved': 'text-purple-600 dark:text-purple-400',
      'Changed': 'text-orange-600 dark:text-orange-400',
      'Fixed': 'text-red-600 dark:text-red-400',
      'Removed': 'text-gray-600 dark:text-gray-400',
      'Security': 'text-yellow-600 dark:text-yellow-400',
      'Technical': 'text-indigo-600 dark:text-indigo-400',
      'Performance': 'text-emerald-600 dark:text-emerald-400'
    };

    return colors[sectionName] || 'text-gray-600 dark:text-gray-400';
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600 dark:text-gray-400">Loading changelog...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <div className="text-red-600 dark:text-red-400 mb-2">⚠️ Error loading changelog</div>
        <p className="text-sm text-gray-600 dark:text-gray-400">{error}</p>
      </div>
    );
  }

  if (versions.length === 0) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <div className="text-gray-600 dark:text-gray-400 mb-2">📝 No changelog entries found</div>
        <p className="text-sm text-gray-500 dark:text-gray-500">Check back later for updates!</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <AnimatePresence>
        {versions.map((version, index) => (
          <motion.div
            key={version.version}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            {/* Version Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="bg-blue-100 dark:bg-blue-900 rounded-full p-2">
                  <span className="text-blue-600 dark:text-blue-400 font-bold text-sm">
                    v{version.version}
                  </span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Version {version.version}
                  </h3>
                  {version.releaseDate && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Released {formatReleaseDate(version.releaseDate)}
                    </p>
                  )}
                </div>
              </div>

              {index === 0 && (
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  Latest
                </span>
              )}
            </div>

            {/* Version Sections */}
            <div className="space-y-4">
              {Object.entries(version.sections).map(([sectionName, items]) => (
                <div key={sectionName}>
                  <h4 className={`text-sm font-semibold mb-2 flex items-center space-x-2 ${getSectionColor(sectionName)}`}>
                    <span>{getSectionIcon(sectionName)}</span>
                    <span>{sectionName}</span>
                  </h4>
                  <ul className="space-y-2 ml-6">
                    {items.map((item, itemIndex) => (
                      <li key={itemIndex} className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                        <span className="inline-block w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full mr-2"></span>
                        <span dangerouslySetInnerHTML={{ __html: item.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') }} />
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

export default ChangelogViewer;
