import { admin, db } from '@/lib/firebase-admin'; // Assuming firebase-admin is configured
import { arrayRemove, arrayUnion, doc, updateDoc } from 'firebase/firestore';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { oneSignalPlayerId } = body;

    if (!oneSignalPlayerId) {
      return NextResponse.json({ error: 'OneSignal Player ID is required' }, { status: 400 });
    }

    // For server-side authentication, we'd typically verify an ID token
    // or use a session management system. For simplicity here, we'll assume
    // this endpoint is called by an authenticated client that has already
    // handled its Firebase auth state. In a real app, robust auth is key.
    // The `auth()` from firebase-admin is for admin tasks, not direct user session.
    // We need a way to get the current user's UID securely.
    // Let's assume the client sends the UID for now, though this needs secure handling in production.
    // A better approach would be to verify an ID token sent from the client.

    const idToken = request.headers.get('Authorization')?.split('Bearer ')[1];

    if (!idToken) {
        return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    if (!admin) {
      console.error('Firebase Admin SDK is not initialized.');
      return NextResponse.json({ error: 'Firebase Admin SDK not initialized. Cannot verify token.' }, { status: 500 });
    }

    let decodedToken;
    try {
        // Use admin.auth() to access the auth service
        decodedToken = await admin.auth().verifyIdToken(idToken);
    } catch (error) {
        console.error('Error verifying ID token:', error);
        return NextResponse.json({ error: 'Invalid authorization token' }, { status: 401 });
    }

    const userId = decodedToken.uid;

    if (!userId) {
      return NextResponse.json({ error: 'User not authenticated or UID not provided' }, { status: 401 });
    }

    const userDocRef = doc(db, 'users', userId);

    // Atomically add the new player ID to an array of player IDs for the user.
    // This supports multiple devices per user.
    // Also, ensure we don't add duplicates if the same ID is sent again.
    // First, remove it if it exists (to prevent duplicates if logic runs multiple times), then add it.
    // This is a common pattern, though Firestore's arrayUnion is generally idempotent for unique values.
    // However, to be absolutely sure and handle potential re-subscriptions cleanly:
    await updateDoc(userDocRef, {
      oneSignalPlayerIds: arrayRemove(oneSignalPlayerId) // Remove first to handle re-subscription
    });
    await updateDoc(userDocRef, {
      oneSignalPlayerIds: arrayUnion(oneSignalPlayerId) // Then add it
    });

    // Optional: Clean up old player IDs if a user has too many (e.g., > 5)
    // This would require reading the document, checking array length, and then updating.

    console.log(`OneSignal Player ID ${oneSignalPlayerId} associated with user ${userId}`);
    return NextResponse.json({ success: true, message: 'OneSignal Player ID updated' }, { status: 200 });

  } catch (error) {
    console.error('Error in /api/onesignal-subscribe:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to update OneSignal Player ID', details: errorMessage }, { status: 500 });
  }
}
