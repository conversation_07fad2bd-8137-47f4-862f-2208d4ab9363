'use client';

import { cn } from '@/utils/cn';
import { cva, type VariantProps } from 'class-variance-authority';
import { forwardRef, ReactNode } from 'react';

const buttonVariants = cva(
  // Base styles - using design system tokens with enhanced accessibility
  [
    'inline-flex items-center justify-center gap-2',
    'font-medium leading-none',
    'transition-all duration-200',
    'border border-transparent',
    'disabled:opacity-50 disabled:pointer-events-none',
    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-neutral-900',
    'focus-visible:ring-2 focus-visible:ring-primary-500',
    'active:scale-[0.98]',
    'touch-manipulation',
    // Enhanced keyboard navigation
    'aria-disabled:opacity-50 aria-disabled:pointer-events-none',
  ].join(' '),
  {
    variants: {
      variant: {
        // Primary button - main actions
        primary: [
          'bg-primary-500 text-white',
          'hover:bg-primary-600',
          'focus:ring-primary-500',
          'shadow-sm hover:shadow-md',
        ].join(' '),

        // Secondary button - less prominent actions
        secondary: [
          'bg-neutral-100 text-neutral-900 border-neutral-200',
          'hover:bg-neutral-200 hover:border-neutral-300',
          'focus:ring-neutral-500',
          'dark:bg-neutral-800 dark:text-neutral-100 dark:border-neutral-700',
          'dark:hover:bg-neutral-700 dark:hover:border-neutral-600',
        ].join(' '),

        // Outline button - subtle alternative
        outline: [
          'bg-transparent text-primary-600 border-primary-200',
          'hover:bg-primary-50 hover:border-primary-300',
          'focus:ring-primary-500',
          'dark:text-primary-400 dark:border-primary-800',
          'dark:hover:bg-primary-950 dark:hover:border-primary-700',
        ].join(' '),

        // Ghost button - minimal styling
        ghost: [
          'bg-transparent text-neutral-700 hover:bg-neutral-100',
          'focus:ring-neutral-500',
          'dark:text-neutral-300 dark:hover:bg-neutral-800',
        ].join(' '),

        // Destructive button - for dangerous actions
        destructive: [
          'bg-error-500 text-white',
          'hover:bg-error-600',
          'focus:ring-error-500',
          'shadow-sm hover:shadow-md',
        ].join(' '),

        // Success button - for positive actions
        success: [
          'bg-accent-500 text-white',
          'hover:bg-accent-600',
          'focus:ring-accent-500',
          'shadow-sm hover:shadow-md',
        ].join(' '),

        // Legacy variants for backward compatibility
        default: 'bg-neutral-900 text-white hover:bg-neutral-800 dark:bg-neutral-50 dark:text-neutral-900 dark:hover:bg-neutral-100',
        subtle: 'bg-neutral-100 text-neutral-900 hover:bg-neutral-200 dark:bg-neutral-800 dark:text-neutral-100 dark:hover:bg-neutral-700',
        link: 'bg-transparent underline-offset-4 hover:underline text-neutral-900 dark:text-neutral-100 hover:bg-transparent dark:hover:bg-transparent',
      },
      size: {
        xs: 'px-2 py-1 text-xs rounded-md min-h-[24px]',
        sm: 'px-3 py-1.5 text-sm rounded-md min-h-[32px]',
        md: 'px-4 py-2 text-md rounded-lg min-h-[40px]',
        lg: 'px-6 py-3 text-lg rounded-lg min-h-[48px]',
        xl: 'px-8 py-4 text-xl rounded-xl min-h-[56px]',
        // Legacy sizes
        default: 'px-4 py-2 text-md rounded-lg min-h-[40px]',
        icon: 'h-10 w-10 rounded-lg',
      },
      fullWidth: {
        true: 'w-full',
        false: 'w-auto',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      fullWidth: false,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  children: ReactNode;
  isLoading?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant,
    size,
    fullWidth,
    asChild = false,
    children,
    isLoading = false,
    leftIcon,
    rightIcon,
    disabled,
    ...props
  }, ref) => {
    // Ensure non-submit default to avoid accidental form submissions
    const { type: buttonType = 'button', ['aria-label']: ariaLabel, ...rest } = props as any;

    // Dev-only a11y hint: warn if button likely lacks an accessible name
    if (process.env.NODE_ENV !== 'production') {
      const hasTextLabel = typeof children === 'string' || Array.isArray(children) && children.some((c: any) => typeof c === 'string');
      if (!hasTextLabel && !ariaLabel) {
        // eslint-disable-next-line no-console
        console.warn('[a11y] <Button> rendered without visible text or aria-label. Provide an accessible name.');
      }
    }

    return (
      <button
        type={buttonType}
        aria-busy={isLoading || undefined}
        data-state={isLoading ? 'loading' : 'idle'}
        className={cn(buttonVariants({ variant, size, fullWidth }), className)}
        ref={ref}
        disabled={disabled || isLoading}
        {...rest}
      >
        {isLoading ? (
          <>
            <svg
              className="animate-spin h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <span className="sr-only" aria-live="polite">Loading...</span>
          </>
        ) : (
          <>
            {leftIcon && <span className="flex-shrink-0" aria-hidden="true">{leftIcon}</span>}
            <span>{children}</span>
            {rightIcon && <span className="flex-shrink-0" aria-hidden="true">{rightIcon}</span>}
          </>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };

