# Payday Pilot Changelog

## [0.6.3]
## [0.6.4]

Released: September 14, 2025

Improved
- PWA App Icon Reliability: Ensured the app uses the correct icons across Android, iOS, and desktop installs. The app icon now looks crisp everywhere.
- Install Experience: Fixed and standardized the manifest icon entries for better compatibility with different devices and browsers.

Fixed
- Bill Status Toggle: You can now toggle a bill between Paid and Unpaid directly from the Bills Overview and the Bill Details page.
- Edit Reliability: Saving changes on the Edit Bill form now waits for the save to complete before navigating, preventing accidental data loss.

What this means for you
- When installing PayDay Pilot to your home screen, you’ll get the correct icon every time.
- Managing bills feels more natural: mark a bill paid by mistake? Quickly mark it Unpaid.
- Editing bills is more reliable, with fewer surprises.

---


**Released: August 12, 2025** 🎉

### Improved
- **Bill Form Precision**: Completely overhauled financial calculations for bank-level accuracy
- **Form Performance**: Dramatically improved the speed and responsiveness of adding and editing bills
- **User Experience**: Enhanced validation with clearer error messages and more intuitive input handling
- **Financial Calculations**: Added high-precision math for loans, debts, and recurring payments

### Fixed
- **Calculation Errors**: Eliminated floating-point precision issues in payment calculations
- **Rounding Inconsistencies**: Fixed currency display to always show exact amounts to the penny
- **Form Validation**: Resolved issues with certain edge cases in financial inputs
- **UI Performance**: Fixed occasional lag when entering bill information

### Technical Improvements
- Added Decimal.js for precise financial calculations
- Implemented memoization and caching for better performance
- Enhanced form component architecture with React best practices
- Added comprehensive test suite for financial calculations

### What This Means for You
- Your bill amounts, loan payments, and financial projections are now calculated with perfect accuracy
- Adding and editing bills feels faster and more responsive
- You'll see clearer guidance when entering information, making the process more intuitive
- Complex financial information like loan payments and interest calculations are now 100% accurate

---

## [0.6.2]

**Released: August 9, 2025** 🎉

### Improved
- **Version Management**: Enhanced the app's update notification system to keep you better informed about new features and improvements
- **Settings Experience**: Streamlined how you discover what's new in each update through the Settings panel
- **Update Notifications**: Improved visual indicators that let you know when exciting new features are available
- **User Experience**: Made it easier to stay up-to-date with the latest improvements to your bill management experience

### What This Means for You
- You'll now get clearer notifications when new features are added to help you manage your bills better
- The "What's New" section in Settings will show you exactly how each update improves your experience
- No more wondering if you're missing out on helpful new features - the app will let you know!
- Updates are explained in simple terms that focus on how they benefit you, not technical details

---

## [0.6.1]

**Released: December 19, 2024** ✨

### Improved
- **Dashboard Text Alignment**: All text on dashboard cards is now perfectly centered for better visual balance and readability
- **Card Titles**: Dashboard card titles are now centered for a more polished appearance
- **Mobile Experience**: Enhanced text centering on mobile devices for improved readability

### Technical
- Updated dashboard card component styling for consistent text alignment
- Improved CSS rules for mobile text positioning
- Enhanced visual hierarchy across all dashboard cards

---

## 0.6.0

**Released: May 11, 2025** 🔔

### New Features

- **Push Notifications (OneSignal)**: Implemented web push notifications using OneSignal for bill reminders.
  - Client-side SDK initialization and user subscription.
  - API endpoint to store user's OneSignal Player ID.
  - Scheduled Netlify Function to query for upcoming bills and send notifications via OneSignal.

### Technical Improvements

- Added `react-onesignal` package for client-side integration.
- Created `OneSignalInitializer.tsx` component for managing SDK setup.
- Implemented `/api/onesignal-subscribe` route for saving subscription details.
- Added `netlify/functions/send-onesignal-notifications.ts` for server-side notification logic.
- Scheduled the `send-onesignal-notifications` function to run daily.

## 0.5.0

**Released: May 10, 2025** ✨

### New Features

- **True Bill Renewal**: You can now properly "Renew" a bill from the edit panel. This creates a new, unpaid instance of the bill for a future date, while keeping the history of the original bill. This is great for tracking recurring expenses accurately!
- **Smarter Dashboard Tips**: The dashboard now provides an initial "Smart Tip" to alert you to your most urgent unpaid bill, with a quick link to view or manage it.

### Improvements & Fixes

- **Add Bill Streamlined**: When adding a bill from the calendar, the bill information form now opens immediately, making it quicker to enter details.
- **Notification System Overhaul**:
  - Notifications for bill reminders are now more reliable and consistent with your actual bill data.
  - Fixed an issue where the notification badge count might not reset to zero after clearing all notifications.
  - Resolved a bug that could cause the app to get stuck in an error loop on the notifications page.
- **Bill Renewal Stability**: Fixed several underlying issues that could prevent bill renewals from working correctly, especially for bills with optional details like 'frequency' or 'vendor'.

## 0.4.0

**Released: May 7, 2025** 📅

### Improvements

- Refined Google integration to focus solely on Calendar synchronization.
- Added a confirmation step before initiating Google Calendar connection.
- Improved the Google authentication callback flow using an intermediate page to ensure client-side state is ready.

### Removals

- Removed all Gmail import functionality and related UI elements.

## 0.3.0

**Released: May 4, 2025** 🚀

### What's New

- **Mobile View Improved**: Calendar is now cleaner on phone screens with less clutter
- **Bill Payments Fixed**: You can now pay bills directly from the calendar without errors
- **Better Performance**: Various improvements to make the app run smoother

## 0.2.0

**Released: May 4, 2025** 🚀

### UI & Design

- Simplified settings page by removing color theme options
- Removed Gmail integration functionality and related UI elements
- Applied 'Paper Maps & Blueprints' theme colors to navigation components (TopNav, BottomNav, DesktopNav)
- Applied serif font (Playfair Display) to all heading elements for improved readability
- Ensured consistent icon sizing throughout the application

### Technical Improvements

- Fixed build issues on Netlify deployment
- Resolved TypeScript type errors in BillImportWizard.tsx
- Implemented safe Firebase Admin initialization to handle build-time environments
- Added proper type annotations for Firestore document snapshots
- Updated Netlify configuration for smoother deployments

## 0.1.0

**Released: April 30, 2025** 🎬

- Initial version
- Gmail integration (connect/disconnect)
- Settings and account management
- Dashboard widgets and urgent bills
- Theme and notification preferences
