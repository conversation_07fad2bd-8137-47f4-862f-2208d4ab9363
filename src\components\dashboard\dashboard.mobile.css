/* Mobile-only enhancements for dashboard cards using design system */
@media (max-width: 640px) {
    .dashboard-card-mobile {
        width: 100% !important;
        margin-bottom: var(--space-5) !important;
        border-radius: var(--radius-xl) !important;
        box-shadow: var(--shadow-sm);
        font-size: var(--font-size-text-md) !important;
        padding: var(--space-5) var(--space-3) !important;
        text-align: center !important;
        display: block !important;
        transition: var(--transition-normal);
    }

    .dashboard-card-mobile:hover {
        box-shadow: var(--shadow-md);
    }

    .dashboard-card-mobile h2 {
        font-size: var(--font-size-text-lg) !important;
        margin-bottom: var(--space-3) !important;
        font-weight: var(--font-weight-semibold);
    }

    .dashboard-modal-content {
        padding: var(--space-4) var(--space-2) !important;
    }

    .dashboard-card-modal {
        border-radius: var(--radius-xl) !important;
        box-shadow: var(--shadow-lg);
    }

    /* Ensure consistent spacing for card content */
    .dashboard-page-mobile {
        padding: var(--space-2) !important;
    }

    .dashboard-cards-container {
        gap: var(--space-4) !important;
    }
}

/* Enhanced hover states for desktop */
@media (min-width: 641px) {
    .dashboard-card-mobile {
        transition: var(--transition-normal);
    }

    .dashboard-card-mobile:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

/* Focus states for accessibility */
.dashboard-card-mobile:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}