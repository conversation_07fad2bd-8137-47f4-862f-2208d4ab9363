'use client';

// Local-only stub for Google Calendar sync: no auth, no endpoints.
import { useCallback, useState } from 'react';

interface CalendarSyncSettings {
  calendarConnected: boolean;
  syncEnabled: boolean;
  lastSyncedAt: string | null;
  googleCalendarId: string | null;
}

export default function useGoogleCalendarSync(_user: any) {
  const [syncSettings] = useState<CalendarSyncSettings>({
    calendarConnected: false,
    syncEnabled: false,
    lastSyncedAt: null,
    googleCalendarId: null,
  });
  const [isLoading] = useState(false);
  const [syncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');
  const [error] = useState<string | null>(null);

  const fetchCalendarSettings = useCallback(async () => {
    // no-op in local-only mode
    return;
  }, []);

  const connectGoogleCalendar = useCallback(() => {
    // no-op in local-only mode
    return;
  }, []);

  const syncBillsToCalendar = useCallback(async () => {
    // no-op in local-only mode
    return false;
  }, []);

  return {
    syncSettings,
    isLoading,
    syncStatus,
    error,
    connectGoogleCalendar,
    syncBillsToCalendar,
    fetchCalendarSettings,
  };
}
