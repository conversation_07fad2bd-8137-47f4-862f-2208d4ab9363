# Code Citations

## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUp
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUp
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUp
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUp
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Com
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Com
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Com
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Com
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Com
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Com
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Com
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Com
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Com
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Com
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Com
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Com
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-m
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-m
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-m
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-m
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-m
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-m
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm
```


## License: MIT
https://github.com/tadoku/tadoku/blob/b4653eb0ef82230d6fea6129a789351833a1b5e4/frontend/tadoku-ui/components/Form.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text
```


## License: unknown
https://github.com/garbhagudi/garbhagudi-next/blob/729fa1d63fafdc877c8192bf654f2506020fa113/src/components/search/searchComponent.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text
```


## License: unknown
https://github.com/alexhernandez-git/user-zoom-challenge/blob/38012e61bbbd3f67992e482bba85d9f55fa4a701/frontend/src/components/Layout/Header/Search/index.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text
```


## License: unknown
https://github.com/jornl/keyword-register/blob/2661ae09a30e71b257110b438ccab71c27a91f24/resources/js/Pages/Keywords/Create.jsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text
```


## License: unknown
https://github.com/FlorjanKlancar/fantasy-league/blob/d619ea49e830222afded0787775c6b4a97e7a380/src/components/tournament/tenis/SelectPlayers.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text
```


## License: unknown
https://github.com/g-htz/sds-2022/blob/5ac3287baa100585f42eaddbeb909bd7f3ab7eda/software-design-studio-main/software-design-studio-main/src/components/ui/tag/TagField.tsx

```
="off"
      />
      <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </Combobox.Button>
    </div>
    <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text
```

