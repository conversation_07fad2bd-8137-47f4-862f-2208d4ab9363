import { DashboardCard } from '@/components/dashboard/DashboardCard';
import BillCalendarWidget from '@/components/dashboard/widgets/BillCalendarWidget';
import { useState } from 'react';
// You can import AddBillForm and SettingsPanel if you have them, or use placeholders

export function TabbedCard({
    title,
    tabs
}: {
    title: string;
    tabs: { label: string; content: React.ReactNode }[];
}) {
    const [activeTab, setActiveTab] = useState(0);
    return (
        <DashboardCard title={title}>
            <div className="flex space-x-2 mb-4 border-b border-gray-200 dark:border-gray-800">
                {tabs.map((tab, idx) => (
                    <button
                        key={tab.label}
                        className={`px-3 py-1 text-sm font-medium rounded-t transition-colors focus:outline-none ${idx === activeTab
                                ? 'bg-gray-100 dark:bg-gray-800 text-blue-600 dark:text-blue-400 border-b-2 border-blue-500'
                                : 'text-gray-500 dark:text-gray-400 hover:text-blue-600'
                            }`}
                        onClick={() => setActiveTab(idx)}
                        aria-selected={idx === activeTab}
                        role="tab"
                    >
                        {tab.label}
                    </button>
                ))}
            </div>
            <div role="tabpanel">{tabs[activeTab].content}</div>
        </DashboardCard>
    );
}

// Example usage for Bill Calendar card
export default function BillCalendarTabbedCard() {
    return (
        <TabbedCard
            title="Bill Calendar"
            tabs={[
                { label: 'Calendar', content: <BillCalendarWidget /> },
                { label: 'Add Bill', content: <div className="p-4 text-gray-500">[Add Bill Form Placeholder]</div> },
                { label: 'Settings', content: <div className="p-4 text-gray-500">[Settings Panel Placeholder]</div> },
            ]}
        />
    );
}
