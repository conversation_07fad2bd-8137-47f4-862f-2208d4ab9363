'use client';

import { browserSupports } from '@/utils/browserCompat';
import { useEffect } from 'react';

interface ConditionalThemeColorProps {
    color: string;
    media?: string; // for conditional colors based on dark mode
}

export function ConditionalThemeColor({ color, media }: ConditionalThemeColorProps) {
    useEffect(() => {
        // Only add theme-color if supported (not Firefox)
        if (browserSupports.themeColor && typeof document !== 'undefined') {
            const existingMeta = document.querySelector('meta[name="theme-color"]');

            if (!existingMeta) {
                const meta = document.createElement('meta');
                meta.name = 'theme-color';
                meta.content = color;
                if (media) {
                    meta.media = media;
                }
                document.head.appendChild(meta);
            } else if (existingMeta instanceof HTMLMetaElement) {
                existingMeta.content = color;
                if (media) {
                    existingMeta.media = media;
                }
            }
        }

        // Cleanup for Firefox - remove theme-color meta tag
        return () => {
            if (!browserSupports.themeColor && typeof document !== 'undefined') {
                const meta = document.querySelector('meta[name="theme-color"]');
                if (meta) {
                    meta.remove();
                }
            }
        };
    }, [color, media]);

    // For SSR and Firefox compatibility, we don't render anything
    if (typeof window === 'undefined' || !browserSupports.themeColor) {
        return null;
    }

    return null; // Client-side effect only
}