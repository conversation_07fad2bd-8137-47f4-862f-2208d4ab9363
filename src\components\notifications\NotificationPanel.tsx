import { useState } from 'react';
import NotificationCard from './NotificationCard';

interface Notification {
  id: string;
  title: string;
  body: string;
  date: string;
  read: boolean;
}

const mockNotifications: Notification[] = [
  // Example notifications (replace with real data/fetch logic)
  // {
  //   id: '1',
  //   title: 'Visa rewards club',
  //   body: 'Earn Points without making a purchase. Complete your first mission today!',
  //   date: '2023-12-16',
  //   read: false,
  // },
];

export function NotificationPanel() {
  // Simulate notification permission/onboarding state
  const [permission, setPermission] = useState<'default' | 'granted' | 'denied'>('default');
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);

  // Onboarding/opt-in state
  if (permission !== 'granted') {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] px-4">
        <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-6 w-full max-w-md">
          <h2 className="font-bold text-lg mb-4">Get notified about important stuff</h2>
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2 text-base">
              <span role="img" aria-label="due">⏰</span>
              <span>Your payment is due</span>
            </div>
            <div className="flex items-center space-x-2 mb-2 text-base">
              <span role="img" aria-label="purchase">🛒</span>
              <span>You make a new purchase</span>
            </div>
            <div className="flex items-center space-x-2 mb-2 text-base">
              <span role="img" aria-label="order">📦</span>
              <span>Your order is on the way</span>
            </div>
            <div className="flex items-center space-x-2 text-base">
              <span role="img" aria-label="deals">💡</span>
              <span>We’ve got deals and products we think you’ll like</span>
            </div>
          </div>
          <div className="flex justify-between gap-2 mt-4">
            <button
              className="flex-1 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition"
              onClick={() => setPermission('denied')}
            >
              Later
            </button>
            <button
              className="flex-1 py-2 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-700 transition"
              onClick={() => setPermission('granted')}
            >
              Get notified
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-3">You can adjust these settings later.</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] px-4">
        <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-6 w-full max-w-md flex flex-col items-center">
          <h2 className="font-bold text-lg mb-2">Notifications</h2>
          <button className="mb-4 px-4 py-2 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-700 transition text-sm">Customize your notifications!</button>
          <div className="my-8">
            <span className="text-5xl block mb-2" role="img" aria-label="inbox">📬</span>
            <div className="text-gray-500 dark:text-gray-400 mb-2">No notifications yet</div>
            <div className="text-xs text-gray-400">Your notifications will appear here once you’ve received them.</div>
          </div>
          <a href="#" className="text-xs text-blue-600 dark:text-blue-400 hover:underline">Go to historical notifications.</a>
        </div>
      </div>
    );
  }

  // Populated state
  return (
    <div className="flex flex-col items-center min-h-[60vh] px-4">
      <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-2">
          <h2 className="font-bold text-lg">Notifications</h2>
          <button className="px-3 py-1 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-700 transition text-xs">Customize your notifications!</button>
        </div>
        <div className="text-xs text-gray-400 mb-2">Previously</div>
        <div className="space-y-3">
          {notifications.map((n) => (
            <NotificationCard key={n.id} notification={n} />
          ))}
        </div>
        <div className="mt-4">
          <a href="#" className="text-xs text-blue-600 dark:text-blue-400 hover:underline">Go to historical notifications.</a>
        </div>
      </div>
    </div>
  );
}

export default NotificationPanel;
