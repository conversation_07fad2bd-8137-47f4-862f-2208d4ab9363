'use client';

import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';

interface BackToDashboardProps {
  className?: string;
}

export function BackToDashboard({ className = '' }: BackToDashboardProps) {
  const router = useRouter();

  return (
    <button
      onClick={() => router.push('/dashboard')}
      aria-label="Navigate back to dashboard"
      className={`
        inline-flex items-center px-4 py-2 text-sm font-medium 
        text-neutral-700 dark:text-neutral-300 
        hover:text-neutral-900 dark:hover:text-neutral-100 
        hover:bg-neutral-100 dark:hover:bg-neutral-800 
        transition-colors duration-200 rounded-lg
        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
        ${className}
      `}
    >
      <ArrowLeftIcon className="h-4 w-4 mr-2" aria-hidden="true" />
      Back to Dashboard
    </button>
  );
}
