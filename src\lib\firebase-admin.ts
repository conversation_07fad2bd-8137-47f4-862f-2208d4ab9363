// src/lib/firebase-admin.ts
import admin from 'firebase-admin';
import { getFirestore } from 'firebase-admin/firestore';

// Create a type-safe dummy implementation for Firestore
type DummyFirestore = {
  collection: (collectionPath: string) => {
    doc: (docPath: string) => {
      get: () => Promise<{ exists: boolean; data: () => Record<string, any> }>,
      set: (data: any) => Promise<void>,
      update: (data: any) => Promise<void>
    },
    where: (field: string, op: string, value: any) => {
      get: () => Promise<{
        empty: boolean,
        docs: Array<{ data: () => Record<string, any>, id: string }>,
        forEach: (callback: Function) => void
      }>
    }
  }
};

// Dummy DB that satisfies minimum Firebase interface requirements
const dummyDb: DummyFirestore = {
  collection: () => ({
    doc: () => ({
      get: async () => ({ exists: false, data: () => ({}) }),
      set: async () => {},
      update: async () => {}
    }),
    where: () => ({
      get: async () => ({
        empty: true,
        docs: [],
        forEach: () => {}
      })
    })
  })
};

// Detect environment
const isServer = typeof window === 'undefined';
const isBuildTime = process.env.NODE_ENV === 'production' &&
  (process.env.NEXT_PHASE === 'phase-production-build' || process.env.NETLIFY);

// Initialize with null values which will be populated if initialization succeeds
let db: any = null;
let firebaseAdmin: typeof admin | null = null;

// Function to create a safe Firebase Admin instance
function createFirebaseAdmin() {
  // Only try to initialize Firebase on server and not during build
  if (isServer && !isBuildTime) {
    try {
      // Only initialize if not already initialized
      if (!admin.apps.length) {
        let serviceAccount: any = null;
        let credentialsFound = false;

        try {
          // Try environment variable first
          const base64Credentials = process.env.FIREBASE_SERVICE_ACCOUNT_BASE64;
          if (base64Credentials) {
            try {
              const buffer = Buffer.from(base64Credentials, 'base64');
              const decodedCredentials = buffer.toString('utf-8');
              serviceAccount = JSON.parse(decodedCredentials);
              credentialsFound = true;
              console.log('Firebase Admin: Using FIREBASE_SERVICE_ACCOUNT_BASE64 credentials');
            } catch (parseError) {
              console.warn('Firebase Admin SDK: Failed to parse FIREBASE_SERVICE_ACCOUNT_BASE64', parseError);
            }
          }

          // Fallback to SERVICE_ACCOUNT_B64 env variable for local dev
          if (!credentialsFound) {
            const b64 = process.env.SERVICE_ACCOUNT_B64;
            if (b64) {
              try {
                const json = Buffer.from(b64, 'base64').toString('utf8');
                serviceAccount = JSON.parse(json);
                credentialsFound = true;
                console.log('Firebase Admin: Using SERVICE_ACCOUNT_B64 credentials');
              } catch (parseError) {
                console.warn('Firebase Admin SDK: Failed to parse SERVICE_ACCOUNT_B64', parseError);
              }
            }
          }

          // If no valid credentials found in development, use dummy implements
          if (!credentialsFound) {
            if (process.env.NODE_ENV === 'development') {
              console.warn('Firebase Admin SDK: No valid credentials found in development');
              return { admin: null, db: dummyDb };
            } else {
              throw new Error('Firebase credentials not found or invalid');
            }
          }

          // Validate the service account
          if (!serviceAccount || !serviceAccount.project_id) {
            throw new Error('Invalid or missing Firebase service account credentials');
          }

          admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
          });
          console.log('Firebase Admin initialized successfully');
          
          return {
            admin: admin,
            db: getFirestore()
          };
        } catch (credError) {
          console.error('Firebase credential error:', credError);
          throw credError;
        }
      }

      // If already initialized, return existing instances
      return {
        admin: admin,
        db: getFirestore()
      };

    } catch (error) {
      console.error('Firebase initialization error:', error);
      // Provide fallback stubs during development
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using Firebase stub in development due to error');
        return { admin: null, db: dummyDb };
      } else if (process.env.NODE_ENV === 'production') {
        // In production, this should be a fatal error.
        console.error('CRITICAL: Firebase Admin failed to initialize in production. Application will not function correctly.', error);
        // Re-throw the error to crash the process or signal unhealthiness.
        // This prevents the app from running with a non-functional core dependency.
        throw new Error(`CRITICAL: Firebase Admin failed to initialize in production. Error: ${(error as Error).message}`);
      } else {
        throw error; // Re-throw in other environments
      }
    }
  } else {
    // During build time or on client side, use dummy implementations
    console.log('Using Firebase stub during build or on client');
    return { admin: null, db: dummyDb };
  }
}

// Initialize Firebase admin
const { admin: initializedAdmin, db: initializedDb } = createFirebaseAdmin();
firebaseAdmin = initializedAdmin;
db = initializedDb;

// Export the references (use actual admin if available, otherwise provide safe fallbacks)
export {
  firebaseAdmin as admin,
  db
};

