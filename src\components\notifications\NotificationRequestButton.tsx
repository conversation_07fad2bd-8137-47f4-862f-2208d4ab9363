'use client';

import { useState, useCallback, useEffect } from 'react';
import { subscribeToPushNotifications } from '@/utils/notifications';

interface NotificationRequestButtonProps {
  className?: string;
}

export function NotificationRequestButton({ className = '' }: NotificationRequestButtonProps) {
  // Use null as initial state to avoid hydration mismatch
  const [status, setStatus] = useState<'default' | 'granted' | 'denied' | null>(null);
  const [mounted, setMounted] = useState(false);

  // Move browser-only code to useEffect
  useEffect(() => {
    setMounted(true);
    if (typeof Notification !== 'undefined') {
      setStatus(Notification.permission as 'default' | 'granted' | 'denied');
    } else {
      setStatus('default');
    }
  }, []);

  const requestPermission = useCallback(async () => {
    try {
      if (typeof Notification !== 'undefined' && Notification.permission === 'default') {
        const permission = await Notification.requestPermission();
        setStatus(permission as 'default' | 'granted' | 'denied');
        
        if (permission === 'granted') {
          await subscribeToPushNotifications();
        }
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  }, []);

  // Don't render anything until client-side hydration is complete
  if (!mounted) return null;
  
  // Now we can safely use browser-specific logic
  if (status === 'granted') {
    return null;
  }

  if (typeof Notification === 'undefined') {
    return null;
  }

  return (
    <button
      onClick={requestPermission}
      className={`flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors
        ${status === 'denied' 
          ? 'bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400 cursor-not-allowed' 
          : 'bg-primary/10 text-primary hover:bg-primary/20 dark:bg-primary/20 dark:hover:bg-primary/30'
        } ${className}`}
      disabled={status === 'denied'}
    >
      {status === 'denied' ? (
        <>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          Notifications blocked
        </>
      ) : (
        <>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
          </svg>
          Enable notifications
        </>
      )}
    </button>
  );
}
