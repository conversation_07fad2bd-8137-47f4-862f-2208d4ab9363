"use client";

// A very small client-side guard to prevent duplicate custom element definitions
// from throwing (common when extensions inject TinyMCE/webcomponents polyfills).
// Safe, no-ops re-definitions, and leaves first definition intact.
//
// This is a defensive workaround for dev environments and user browsers with
// aggressive extensions. It should be mounted as early as possible in the app layout.

import { useEffect } from "react";

type PatchedCustomElementRegistry = CustomElementRegistry & {
  __duplicateGuardApplied?: boolean;
  __originalDefine?: CustomElementRegistry["define"];
  __patchedElements?: Set<string>;
};

declare global {
  interface CustomElementRegistry {
    __duplicateGuardApplied?: boolean;
    __originalDefine?: CustomElementRegistry["define"];
    __patchedElements?: Set<string>;
  }
}

const shouldIgnoreError = (name: string, message: string) =>
  message.includes("has already been defined") ||
  message.includes("already been registered") ||
  message.includes("already been called") ||
  name === "mce-autosize-textarea";

const applyDuplicateGuard = (registry: PatchedCustomElementRegistry) => {
  if (registry.__duplicateGuardApplied) {
    return;
  }

  try {
    const originalDefine = registry.define.bind(registry);
    const patchedElements = new Set<string>();

    registry.__duplicateGuardApplied = true;
    registry.__originalDefine = originalDefine;
    registry.__patchedElements = patchedElements;

    registry.define = ((name: string, constructor: CustomElementConstructor, options?: ElementDefinitionOptions) => {
      try {
        if (registry.get(name)) {
          patchedElements.add(name);
          return;
        }
        originalDefine(name, constructor, options as any);
      } catch (err) {
        const message = (err as Error)?.message || "";
        if (shouldIgnoreError(name, message)) {
          patchedElements.add(name);
          return;
        }
        throw err;
      }
    }) as typeof registry.define;
  } catch (error) {
    console.warn("SafeCustomElements patch failed:", error);
  }
};

if (typeof window !== "undefined" && window.customElements) {
  applyDuplicateGuard(window.customElements as PatchedCustomElementRegistry);
}

export function SafeCustomElements() {
  useEffect(() => {
    if (typeof window === "undefined" || !window.customElements) return;

    // Apply the custom elements guard
    applyDuplicateGuard(window.customElements as PatchedCustomElementRegistry);

    // Mobile layout stability fix
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
      console.log('[SafeCustomElements] Mobile device detected - applying layout stability fixes');

      // Prevent layout shifts when custom elements fail to register
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as HTMLElement;
                // Check for custom elements that might cause layout issues
                if (element.tagName && element.tagName.includes('-') && element.style) {
                  element.style.setProperty('visibility', 'visible', 'important');
                }
              }
            });
          }
        });
      });

      // Start observing
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      // Cleanup on unmount
      return () => {
        observer.disconnect();
      };
    }
  }, []);

  return null;
}

