'use client';

import { useBillsStore } from '@/stores/billsStore';
import { useUserPreferences } from '@/stores/userPreferencesStore';
import { parseLocalDateString } from '@/utils/date';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { BaseWidget } from './BaseWidget';

interface BillEvent {
  id: string;
  name: string;
  amount: number;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue';
  category: string;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  bills: BillEvent[];
}

export default function BillCalendarWidget() {
  const [loading, setLoading] = useState(true);
  const { trackAction } = useUserPreferences();
  const router = useRouter();
  const { bills, isLoading } = useBillsStore();
  const [events, setEvents] = useState<BillEvent[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');

  useEffect(() => {
    if (!isLoading && bills) {
      setEvents(
        bills.map((bill) => {
          const dueDateObj = bill.dueDate ? parseLocalDateString(bill.dueDate) : null;
          return {
            id: bill.id,
            name: bill.name,
            amount: bill.amount,
            dueDate: bill.dueDate,
            status: bill.paidDate ? 'paid' : (dueDateObj && dueDateObj < new Date() ? 'overdue' : 'pending'),
            category: bill.category
          };
        })
      );
      setLoading(false);
    }
  }, [bills, isLoading]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getDayName = (dateString: string) => {
    const dateObj = parseLocalDateString(dateString);
    return dateObj ? dateObj.toLocaleDateString('en-US', { weekday: 'short' }) : '';
  };

  const getDayNumber = (dateString: string) => {
    const dateObj = parseLocalDateString(dateString);
    return dateObj ? dateObj.getDate() : '';
  };

  const getMonthName = (dateString: string) => {
    const dateObj = parseLocalDateString(dateString);
    return dateObj ? dateObj.toLocaleDateString('en-US', { month: 'short' }) : '';
  };

  const getStatusColor = (status: BillEvent['status']) => {
    switch (status) {
      case 'paid':
        return 'text-green-600 dark:text-green-400';
      case 'overdue':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-blue-600 dark:text-blue-400';
    }
  };

  // Generate calendar days for the current month
  const generateCalendarDays = (): CalendarDay[] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const startDate = new Date(firstDayOfMonth);
    startDate.setDate(startDate.getDate() - firstDayOfMonth.getDay());

    const days: CalendarDay[] = [];
    const currentDay = new Date(startDate);

    // Generate 42 days (6 weeks)
    for (let i = 0; i < 42; i++) {
      const isCurrentMonth = currentDay.getMonth() === month;
      const isToday = currentDay.getTime() === today.getTime();

      const dayBills = events.filter(event => {
        const eventDate = parseLocalDateString(event.dueDate);
        return eventDate &&
          eventDate.getDate() === currentDay.getDate() &&
          eventDate.getMonth() === currentDay.getMonth() &&
          eventDate.getFullYear() === currentDay.getFullYear();
      });

      days.push({
        date: new Date(currentDay),
        isCurrentMonth,
        isToday,
        bills: dayBills
      });

      currentDay.setDate(currentDay.getDate() + 1);
    }

    return days;
  };

  const calendarDays = generateCalendarDays();
  const monthName = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  return (
    <BaseWidget
      title="Upcoming Bills Calendar"
      icon={
        <svg className="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      }
    >
      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse flex gap-4">
              <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="flex-1 space-y-2 py-1">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-3">
          {events.map((event) => (
            <div
              key={event.id}
              className="flex gap-4 p-3 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-lg transition-colors cursor-pointer"
              onClick={() => trackAction('view_bill_details')}
            >
              <div className="w-16 h-16 rounded-lg bg-gray-100 dark:bg-gray-800 flex flex-col items-center justify-center text-center">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {getDayName(event.dueDate)}
                </div>
                <div className="text-xl font-bold">
                  {getDayNumber(event.dueDate)}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {getMonthName(event.dueDate)}
                </div>
              </div>

              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-medium">{event.name}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {event.category}
                    </div>
                  </div>
                  <div className={`font-medium ${getStatusColor(event.status)}`}>
                    {formatCurrency(event.amount)}
                  </div>
                </div>

                <div className={`mt-1 text-sm ${getStatusColor(event.status)}`}>
                  {event.status === 'paid' ? 'Paid' : event.status === 'overdue' ? 'Overdue' : 'Upcoming'}
                </div>
              </div>
            </div>
          ))}

          <button
            className="w-full mt-2 py-2 text-sm text-blue-600 dark:text-blue-400 hover:underline"
            onClick={() => {
              trackAction('view_full_calendar');
              router.push('/calendar');
            }}
          >
            View full calendar
          </button>
        </div>
      )}
    </BaseWidget>
  );
}