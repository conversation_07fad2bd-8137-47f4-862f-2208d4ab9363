'use client';

import { useEffect, useState } from 'react';

export default function PWADebug() {
  const [debugInfo, setDebugInfo] = useState<{
    hasServiceWorker: boolean;
    hasManifest: boolean;
    isHttps: boolean;
    navigatorPWA: boolean;
    installEvent: boolean;
    standalone: boolean;
  }>({
    hasServiceWorker: false,
    hasManifest: false,
    isHttps: false,
    navigatorPWA: false,
    installEvent: false,
    standalone: false
  });

  useEffect(() => {
    const checkPWA = async () => {
      try {
        // Check if running in a secure context (HTTPS or localhost)
        const isHttps = window.location.protocol === 'https:' || 
                       window.location.hostname === 'localhost';
        
        // Check if service worker is supported and registered
        const hasServiceWorker = 'serviceWorker' in navigator;
        
        // Check if manifest exists
        const manifestLink = document.querySelector('link[rel="manifest"]');
        const hasManifest = !!manifestLink;
        
        // Check if browser supports PWA features
        const navigatorPWA = 'BeforeInstallPromptEvent' in window || 
                            'onbeforeinstallprompt' in window;
        
        // Check if running in standalone mode
        const standalone = window.matchMedia('(display-mode: standalone)').matches || 
                          (window.navigator as any).standalone === true;
        
        setDebugInfo({
          hasServiceWorker,
          hasManifest,
          isHttps,
          navigatorPWA,
          installEvent: false, // Will be updated by event listener
          standalone
        });

        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', () => {
          setDebugInfo(prev => ({ ...prev, installEvent: true }));
        });
      } catch (error) {
        console.error('Error checking PWA status:', error);
      }
    };

    checkPWA();
  }, []);

  return (
    <div className="p-4 m-4 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h2 className="text-xl font-bold mb-4">PWA Debug Information</h2>
      <ul className="space-y-2">
        <li>
          <span className="font-medium">HTTPS/Secure Context:</span>{' '}
          <span className={debugInfo.isHttps ? 'text-green-500' : 'text-red-500'}>
            {debugInfo.isHttps ? '✓' : '✗'}
          </span>
        </li>
        <li>
          <span className="font-medium">Service Worker Support:</span>{' '}
          <span className={debugInfo.hasServiceWorker ? 'text-green-500' : 'text-red-500'}>
            {debugInfo.hasServiceWorker ? '✓' : '✗'}
          </span>
        </li>
        <li>
          <span className="font-medium">Manifest Found:</span>{' '}
          <span className={debugInfo.hasManifest ? 'text-green-500' : 'text-red-500'}>
            {debugInfo.hasManifest ? '✓' : '✗'}
          </span>
        </li>
        <li>
          <span className="font-medium">PWA Install API Support:</span>{' '}
          <span className={debugInfo.navigatorPWA ? 'text-green-500' : 'text-red-500'}>
            {debugInfo.navigatorPWA ? '✓' : '✗'}
          </span>
        </li>
        <li>
          <span className="font-medium">Install Event Triggered:</span>{' '}
          <span className={debugInfo.installEvent ? 'text-green-500' : 'text-red-500'}>
            {debugInfo.installEvent ? '✓' : '✗'}
          </span>
        </li>
        <li>
          <span className="font-medium">Running as Standalone:</span>{' '}
          <span className={debugInfo.standalone ? 'text-green-500' : 'text-red-500'}>
            {debugInfo.standalone ? '✓' : '✗'}
          </span>
        </li>
      </ul>
    </div>
  );
}
