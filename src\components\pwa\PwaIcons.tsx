'use client';

import { useEffect } from 'react';

const SERVICE_WORKER_ENTRY = '/custom-sw.js';

export function PwaIcons() {
  const version = process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0';

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const manifestHref = `/manifest.json?v=${version}`;
    const manifestLink = document.querySelector('link[rel="manifest"]');

    if (manifestLink instanceof HTMLLinkElement) {
      const expected = new URL(manifestHref, window.location.origin).href;
      if (manifestLink.href !== expected) {
        manifestLink.href = manifestHref;
      }
    } else {
      const link = document.createElement('link');
      link.rel = 'manifest';
      link.href = manifestHref;
      document.head.appendChild(link);
    }

    if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {
      navigator.serviceWorker
        .getRegistration()
        .then((registration) => {
          if (registration) return;

          const existingScript = Array.from(document.scripts).some((script) => script.src.includes('/register-sw.js'));
          if (existingScript) return;

          const script = document.createElement('script');
          script.src = `/register-sw.js?v=${version}`;
          script.async = true;
          const swPath = `${SERVICE_WORKER_ENTRY}?v=${version}`;
          script.dataset.controller = swPath;
          script.dataset.version = version;
          document.head.appendChild(script);
        })
        .catch((error) => {
          console.warn('Unable to determine existing service worker registration:', error);
        });
    }
  }, [version]);

  return (
    <>
      <link rel="manifest" href={`/manifest.json?v=${version}`} />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="PayDay Pilot" />
      <link rel="icon" type="image/png" href={`/icons/icon-192x192.png?v=${version}`} />
      <link rel="apple-touch-icon" href={`/icons/icon-192x192.png?v=${version}`} />
      <link rel="apple-touch-icon" sizes="152x152" href={`/icons/icon-192x192.png?v=${version}`} />
      <link rel="apple-touch-icon" sizes="180x180" href={`/icons/icon-192x192.png?v=${version}`} />
      <link rel="apple-touch-icon" sizes="167x167" href={`/icons/icon-192x192.png?v=${version}`} />
      <meta name="msapplication-TileImage" content={`/icons/icon-192x192.png?v=${version}`} />
    </>
  );
}
