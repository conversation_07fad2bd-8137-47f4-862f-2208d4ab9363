'use client';

import { Bill } from '@/types/bill';
import React, { createContext, ReactNode, useContext, useState } from 'react';

type BillCalendarContextType = {
  selectedBill: Bill | null;
  isSidePanelOpen: boolean;
  isPaidArchiveOpen: boolean;
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
  openSidePanel: (bill: Bill) => void;
  openSidePanelForBill: (bill: Bill) => void; // Added for compatibility
  editingBill: Bill | null; // Added for compatibility
  closeSidePanel: () => void;
  refreshCalendar: () => void;
  togglePaidArchive: () => void;
};

const BillCalendarContext = createContext<BillCalendarContextType | undefined>(undefined);

export const BillCalendarProvider = ({ children }: { children: ReactNode }) => {
  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);
  const [isSidePanelOpen, setIsSidePanelOpen] = useState(false);
  const [isPaidArchiveOpen, setIsPaidArchiveOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const openSidePanel = (bill: Bill) => {
    setSelectedBill(bill);
    setIsSidePanelOpen(true);
  };

  const closeSidePanel = () => {
    setIsSidePanelOpen(false);
    // We'll keep the selected bill for a moment to allow for a smooth closing animation
    setTimeout(() => {
      setSelectedBill(null);
    }, 300); // Match the transition time in CSS
  };

  const refreshCalendar = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const togglePaidArchive = () => {
    setIsPaidArchiveOpen(prev => !prev);
  };

  return (
    <BillCalendarContext.Provider
      value={{
        selectedBill,
        isSidePanelOpen,
        isPaidArchiveOpen,
        selectedDate,
        setSelectedDate,
        openSidePanel,
        // Add compatibility properties
        openSidePanelForBill: openSidePanel, // Alias for openSidePanel
        editingBill: selectedBill, // Alias for selectedBill
        closeSidePanel,
        refreshCalendar,
        togglePaidArchive,
      }}
    >
      {children}
    </BillCalendarContext.Provider>
  );
};

export const useBillCalendarContext = () => {
  const context = useContext(BillCalendarContext);
  if (context === undefined) {
    throw new Error('useBillCalendarContext must be used within a BillCalendarProvider');
  }
  return context;
};
