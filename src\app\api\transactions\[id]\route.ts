// src/app/api/transactions/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Transaction } from '@/types/financial';
import transactionsStore from '../../../../lib/transactionsStore';

interface Params {
  id: string;
}

// GET /api/transactions/[id] - Fetch a single transaction by ID (Optional, usually covered by GET all)
export async function GET(request: NextRequest, { params }: { params: Params }) {
  try {
    const { id } = params;
    const transaction = transactionsStore.getTransactionById(id);

    if (!transaction) {
      return NextResponse.json({ message: 'Transaction not found' }, { status: 404 });
    }
    console.log(`GET /api/transactions/${id} - Found transaction`);
    return NextResponse.json(transaction);
  } catch (error) {
    console.error(`Failed to fetch transaction ${params.id}:`, error);
    return NextResponse.json({ message: 'Failed to fetch transaction' }, { status: 500 });
  }
}

// PUT /api/transactions/[id] - Update a transaction by ID
export async function PUT(request: NextRequest, { params }: { params: Params }) {
  try {
    const { id } = params;
    const txData: Partial<Transaction> = await request.json();
    const now = new Date().toISOString();
    
    // Basic validation for type if provided
    if (txData.type && !['income', 'expense'].includes(txData.type)) {
        return NextResponse.json({ message: 'Invalid transaction type' }, { status: 400 });
    }
    
    // In a real app, update in your database here
    const updatedTx = transactionsStore.updateTransaction(id, {
      ...txData,
      updatedAt: now
    });
    
    if (!updatedTx) {
      return NextResponse.json({ message: 'Transaction not found' }, { status: 404 });
    }

    console.log(`PUT /api/transactions/${id} - Updated transaction`);
    return NextResponse.json(updatedTx);
  } catch (error) {
    console.error(`Failed to update transaction ${params.id}:`, error);
    return NextResponse.json({ message: 'Failed to update transaction' }, { status: 500 });
  }
}

// DELETE /api/transactions/[id] - Delete a transaction by ID
export async function DELETE(request: NextRequest, { params }: { params: Params }) {
  try {
    const { id } = params;
    
    // In a real app, delete from your database here
    const deleted = transactionsStore.deleteTransaction(id);
    
    if (!deleted) {
      return NextResponse.json({ message: 'Transaction not found' }, { status: 404 });
    }

    console.log(`DELETE /api/transactions/${id} - Deleted transaction`);
    // Return 204 No Content for successful deletion
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error(`Failed to delete transaction ${params.id}:`, error);
    return NextResponse.json({ message: 'Failed to delete transaction' }, { status: 500 });
  }
}
