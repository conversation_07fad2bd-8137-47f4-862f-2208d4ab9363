#!/usr/bin/env node

/**
 * Version bump script for PayDay Pilot
 * Updates package.json version and creates changelog template
 */

const fs = require('fs');
const path = require('path');

// Get version from command line argument or package.json
let newVersion = process.argv[2];

// If no version provided, read from package.json (for npm version commands)
if (!newVersion) {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  newVersion = packageJson.version;
}

if (!newVersion) {
  console.error('Usage: node scripts/bump-version.js <version>');
  console.error('Example: node scripts/bump-version.js 0.6.2');
  process.exit(1);
}

// Validate semantic version format
const versionRegex = /^\d+\.\d+\.\d+$/;
if (!versionRegex.test(newVersion)) {
  console.error('Error: Version must be in semantic version format (e.g., 1.2.3)');
  process.exit(1);
}

const rootDir = path.join(__dirname, '..');
const packageJsonPath = path.join(rootDir, 'package.json');
const changelogPath = path.join(rootDir, 'CHANGELOG.md');
const publicChangelogPath = path.join(rootDir, 'public', 'CHANGELOG.md');

try {
  // Update package.json
  console.log('📦 Updating package.json...');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const oldVersion = packageJson.version;
  packageJson.version = newVersion;
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
  console.log(`✅ Updated version from ${oldVersion} to ${newVersion}`);

  // Create changelog template
  const today = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const changelogTemplate = `# Payday Pilot Changelog

## [${newVersion}]

**Released: ${today}** ✨

### Added
-

### Improved
-

### Fixed
-

### Technical
-

---

`;

  // Update both changelog files
  console.log('📝 Updating changelog files...');

  // Read existing changelog
  let existingChangelog = '';
  if (fs.existsSync(changelogPath)) {
    existingChangelog = fs.readFileSync(changelogPath, 'utf8');
    // Remove the header to avoid duplication
    existingChangelog = existingChangelog.replace(/^# Payday Pilot Changelog\s*\n/, '');
  }

  // Write new changelog
  const newChangelog = changelogTemplate + existingChangelog;
  fs.writeFileSync(changelogPath, newChangelog);
  fs.writeFileSync(publicChangelogPath, newChangelog);

  console.log('✅ Created changelog template');

  console.log('\n🎉 Version bump complete!');
  console.log('\nNext steps:');
  console.log('1. Edit CHANGELOG.md to add your changes');
  console.log('2. Commit your changes');
  console.log('3. Test the application');
  console.log('4. Deploy when ready');

} catch (error) {
  console.error('❌ Error during version bump:', error.message);
  process.exit(1);
}
