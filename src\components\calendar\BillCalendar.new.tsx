import { useAuth } from '@/hooks/useAuth';
import { useBillsStore } from '@/stores/billsStore'; // Import the bills store
import { BillFormData } from '@/types/bill'; // Import BillFormData interface
import { parseLocalDateString } from '@/utils/date'; // Import the missing function
import clsx from 'clsx';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { FormEvent, useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';

// --- TypeScript Types ---
interface Bill {
  id: string;
  name: string;
  amount?: number;
  dueDate: string;
  category?: string;
  notes?: string;
  isPaid?: boolean;
  isRenewedOriginal?: boolean;
  renewalOfBillId?: string;
}

interface CalendarEvent {
  id: string;
  summary: string;
  description?: string;
  start: Date;
  end: Date;
  location?: string;
  eventType: 'google' | 'bill';
}

interface Day {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isWeekend: boolean;
  bills: Bill[];
}

type ViewMode = 'Month' | 'Week' | 'Agenda';

// --- Helper Functions ---
const getDaysInMonth = (year: number, month: number): Date[] => {
  const date = new Date(year, month, 1);
  const days: Date[] = [];
  while (date.getMonth() === month) {
    days.push(new Date(date));
    date.setDate(date.getDate() + 1);
  }
  return days;
};

const getMonthName = (date: Date): string => {
  return date.toLocaleString('default', { month: 'long' });
};

const getYear = (date: Date): number => {
  return date.getFullYear();
};

const formatDateForInput = (date: Date): string => {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// --- EditBillPanel Component ---
interface EditBillPanelProps {
  bill: Bill | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedBill: Bill) => void;
  onDelete: (billId: string) => void;
  onOpenRenewModal: (billToRenew: Bill) => void;
}

const EditBillPanel: React.FC<EditBillPanelProps> = ({ bill, isOpen, onClose, onSave, onDelete, onOpenRenewModal }) => {
  const [editedBill, setEditedBill] = useState<Bill | null>(bill);

  useEffect(() => {
    setEditedBill(bill);
  }, [bill]);

  if (!isOpen || !editedBill) {
    return null;
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    let processedValue: string | number | Date | boolean | undefined = value;

    if (name === 'amount') {
      processedValue = value === '' ? undefined : (isNaN(parseFloat(value)) ? undefined : parseFloat(value));
    } else if (type === 'date') {
      processedValue = value;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    setEditedBill(prev => prev ? { ...prev, [name]: processedValue } : null);
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (editedBill) onSave(editedBill);
  };

  const handleDelete = () => {
    if (editedBill && window.confirm(`Are you sure you want to delete "${editedBill.name}"?`)) {
      onDelete(editedBill.id);
    }
  };

  return (
    <div
      className={`fixed inset-0 z-50 bg-white dark:bg-gray-800 shadow-xl transform transition-transform duration-300 ease-in-out ${isOpen ? 'translate-y-0 md:translate-x-0' : 'translate-y-full md:translate-x-full'} md:inset-y-0 md:left-auto md:right-0 md:w-96`}
    >
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">{editedBill?.id.startsWith('temp-') ? 'Add New Bill' : 'Edit Bill'}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" aria-label="Close panel">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path></svg>
          </button>
        </div>
        <form onSubmit={handleSubmit} className="flex-grow p-6 space-y-4 overflow-y-auto">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bill Name</label>
            <input type="text" name="name" id="name" value={editedBill.name} onChange={handleChange} required className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 sm:text-sm" />
          </div>
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Amount</label>
            <input type="number" name="amount" id="amount" value={editedBill.amount === undefined ? '' : editedBill.amount} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 sm:text-sm" placeholder="0.00" step="0.01" />
          </div>
          <div>
            <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Due Date</label>
            <input type="date" name="dueDate" id="dueDate" value={editedBill.dueDate} onChange={handleChange} required className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 sm:text-sm" />
          </div>
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
            <input type="text" name="category" id="category" value={editedBill.category || ''} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 sm:text-sm" placeholder="e.g., Utility, Subscription" />
          </div>
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes</label>
            <textarea name="notes" id="notes" value={editedBill.notes || ''} onChange={handleChange} rows={3} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 sm:text-sm"></textarea>
          </div>
          <div className="flex items-center">
            <input type="checkbox" name="isPaid" id="isPaid" checked={!!editedBill.isPaid} onChange={handleChange} className="h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 dark:focus:ring-blue-400" />
            <label htmlFor="isPaid" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">Mark as Paid</label>
          </div>
          <div className="pt-4 space-y-2">
            <button type="submit" className="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Save Changes</button>
            {editedBill && !editedBill.id.startsWith('temp-') && (
              <button type="button" onClick={handleDelete} className="w-full px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">Delete Bill</button>
            )}
            {editedBill && !editedBill.id.startsWith('temp-') && !editedBill.isRenewedOriginal && (
              <button type="button" onClick={() => onOpenRenewModal(editedBill)} className="w-full px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">Renew Bill</button>
            )}
            <button type="button" onClick={onClose} className="w-full px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400">Cancel</button>
          </div>
        </form>
      </div>
    </div>
  );
};

// --- RenewBillModal Component ---
interface RenewBillModalProps {
  isOpen: boolean;
  billName: string;
  originalDueDate: string;
  onClose: () => void;
  onConfirm: (newDueDate: string, newAmount?: number, newNotes?: string) => void;
}

const RenewBillModal: React.FC<RenewBillModalProps> = ({ isOpen, billName, originalDueDate, onClose, onConfirm }) => {
  const [newDueDateForRenewal, setNewDueDateForRenewal] = useState('');
  const [newAmountForRenewal, setNewAmountForRenewal] = useState('');
  const [newNotesForRenewal, setNewNotesForRenewal] = useState('');

  useEffect(() => {
    if (isOpen && originalDueDate) {
      const originalDate = parseLocalDateString(originalDueDate);
      if (originalDate) {
        const nextMonthDate = new Date(originalDate.getFullYear(), originalDate.getMonth() + 1, originalDate.getDate());
        setNewDueDateForRenewal(formatDateForInput(nextMonthDate));
      } else {
        setNewDueDateForRenewal('');
      }
      setNewAmountForRenewal('');
      setNewNotesForRenewal('');
    }
  }, [isOpen, originalDueDate]);

  if (!isOpen) return null;

  const handleConfirmClick = () => {
    if (!newDueDateForRenewal) {
      toast.error('New due date is required for renewal.');
      return;
    }
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const newDueDateObj = parseLocalDateString(newDueDateForRenewal);

    if (newDueDateObj && newDueDateObj < today) {
      toast.error('New due date cannot be in the past.');
      return;
    }
    if (newDueDateObj && originalDueDate && parseLocalDateString(originalDueDate) && newDueDateObj <= parseLocalDateString(originalDueDate)!) {
      toast.error('New due date must be after the original due date.');
      return;
    }

    const amount = newAmountForRenewal ? parseFloat(newAmountForRenewal) : undefined;
    if (newAmountForRenewal && (isNaN(amount!) || amount! < 0)) {
      toast.error('Please enter a valid amount.');
      return;
    }
    onConfirm(newDueDateForRenewal, amount, newNotesForRenewal);
  };

  return (
    <div className="fixed inset-0 z-[100] bg-black bg-opacity-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full">
        <div className="flex items-center justify-between p-5 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Renew Bill: {billName}</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" aria-label="Close renew modal">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path></svg>
          </button>
        </div>
        <div className="p-6 space-y-4">
          <div>
            <label htmlFor="newDueDateForRenewal" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Due Date</label>
            <input type="date" id="newDueDateForRenewal" value={newDueDateForRenewal} onChange={(e) => setNewDueDateForRenewal(e.target.value)} required className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400" />
          </div>
          <div>
            <label htmlFor="newAmountForRenewal" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Amount (Optional)</label>
            <input type="number" id="newAmountForRenewal" value={newAmountForRenewal} onChange={(e) => setNewAmountForRenewal(e.target.value)} placeholder="Original amount will be used if blank" className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400" step="0.01" />
          </div>
          <div>
            <label htmlFor="newNotesForRenewal" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Notes (Optional)</label>
            <textarea id="newNotesForRenewal" value={newNotesForRenewal} onChange={(e) => setNewNotesForRenewal(e.target.value)} rows={2} placeholder="Original notes will be used if blank, or add new notes" className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400"></textarea>
          </div>
        </div>
        <div className="flex justify-end gap-3 p-5 border-t border-gray-200 dark:border-gray-700">
          <button onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md">Cancel</button>
          <button onClick={handleConfirmClick} className="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md">Confirm Renewal</button>
        </div>
      </div>
    </div>
  );
};

// --- Main Calendar Application Component ---
interface CalendarAppProps {
  compact?: boolean;
}

const CalendarApp: React.FC<CalendarAppProps> = ({ compact = false }) => {
  const { user } = useAuth();
  const [currentDate, setCurrentDate] = useState(new Date(2025, 4, 1));
  const [viewMode, setViewMode] = useState<ViewMode>('Month');
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date(2025, 4, 6));
  const [showPaid, setShowPaid] = useState(false);
  const [isSummaryPanelVisible, setIsSummaryPanelVisible] = useState(true);
  const { bills: allBills, addBill: storeAddBill, updateBill: storeUpdateBill, deleteBill: storeDeleteBill, renewBill: storeRenewBill, isLoading: billsLoading, error: billsError } = useBillsStore();

  const [isEditPanelOpen, setIsEditPanelOpen] = useState(false);
  const [editingBill, setEditingBill] = useState<Bill | null>(null);

  const [isRenewModalOpen, setIsRenewModalOpen] = useState(false);
  const [billToRenewDetails, setBillToRenewDetails] = useState<{ id: string; name: string; dueDate: string } | null>(null);

  const [isCalendarConnected, setIsCalendarConnected] = useState<boolean>(false);
  const [isCalendarLoading, setIsCalendarLoading] = useState<boolean>(false);
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [statusMessage, setStatusMessage] = useState<{ text: string; type: 'success' | 'error' | 'info' | null }>({ text: '', type: null });
  const [showGoogleAuthModal, setShowGoogleAuthModal] = useState<boolean>(false);

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const success = searchParams.get('success');
    const error = searchParams.get('error');

    if (success === 'true') {
      setIsCalendarConnected(true);
      setStatusMessage({ text: 'Successfully connected to Google Calendar!', type: 'success' });
      router.replace('/calendar');
    } else if (error) {
      setStatusMessage({ text: `Calendar connection failed: ${error}`, type: 'error' });
      router.replace('/calendar');
    }
  }, [searchParams, router]);

  useEffect(() => {
    if (selectedDate && allBills && (selectedDate.getMonth() !== currentDate.getMonth() || selectedDate.getFullYear() !== currentDate.getFullYear())) {
      const firstBillInCurrentMonth = allBills.find(b => {
        const billDate = parseLocalDateString(b.dueDate);
        return billDate && billDate.getMonth() === currentDate.getMonth() && billDate.getFullYear() === currentDate.getFullYear();
      });
      if (firstBillInCurrentMonth) {
        const billDate = parseLocalDateString(firstBillInCurrentMonth.dueDate);
        if (billDate) setSelectedDate(billDate);
      } else {
        setSelectedDate(new Date(currentDate.getFullYear(), currentDate.getMonth(), 1));
      }
    }
  }, [currentDate, selectedDate, allBills]);

  useEffect(() => {
    if (statusMessage.text && statusMessage.type) {
      const timer = setTimeout(() => {
        setStatusMessage({ text: '', type: null });
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [statusMessage]);

  const handleSyncToGoogleCalendar = () => {
    // TODO: Implement Google Calendar Sync Logic
    console.log('Attempting to sync to Google Calendar...');
    // For now, simulate a loading state and then a success/failure
    setIsCalendarLoading(true);
    setTimeout(() => {
      // Simulate success for now
      // setIsCalendarConnected(true);
      // setStatusMessage({ text: 'Successfully synced to Google Calendar!', type: 'success' });
      // Or simulate failure
      setStatusMessage({ text: 'Google Calendar sync is not yet implemented.', type: 'info' });
      setIsCalendarLoading(false);
    }, 2000);
  };

  const goToToday = () => {
    const today = new Date();
    setCurrentDate(today);
    setSelectedDate(today);
  };

  const goToPreviousMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  const handleAddBill = () => {
    const targetDate = selectedDate || new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const cleanTargetDate = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());
    const newBillForForm: Bill = {
      id: `temp-${Date.now()}`,
      name: "New Bill",
      amount: undefined,
      dueDate: formatDateForInput(cleanTargetDate),
      isPaid: false,
      category: 'Other',
      notes: '',
    };
    setEditingBill(newBillForForm);
    setIsEditPanelOpen(true);
  };

  const handleConnectCalendar = () => {
    if (!user) {
      setStatusMessage({ text: 'Please sign in to connect your calendar', type: 'error' });
      return;
    }
    if (isCalendarConnected) {
      setIsCalendarConnected(false);
      setCalendarEvents([]);
      setStatusMessage({ text: 'Calendar disconnected successfully', type: 'info' });
    } else {
      setShowGoogleAuthModal(true);
    }
  };

  const handleGoogleAuthConfirm = () => {
    if (!user?.uid) {
      setStatusMessage({ text: 'User authentication required', type: 'error' });
      setShowGoogleAuthModal(false);
      return;
    }
    setIsCalendarLoading(true);
    window.location.href = `/api/auth/google?uid=${user.uid}`;
  };

  const handleGoogleAuthDeny = () => {
    setShowGoogleAuthModal(false);
    setStatusMessage({ text: 'Calendar connection canceled', type: 'info' });
  };

  const syncBillsToCalendar = useCallback(async () => {
    if (!user || !isCalendarConnected) {
      toast.error('Please connect your Google Calendar first');
      return;
    }
    setIsCalendarLoading(true);
    setStatusMessage({ text: 'Exporting bills to your Google Calendar...', type: 'info' });
    try {
      const billsToExport = showPaid && allBills ? allBills : (allBills || []).filter(bill => !bill.isPaid);
      const response = await fetch('/api/calendar/sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ bills: billsToExport.map(billToCalendarEvent).filter(Boolean) })
      });
      if (!response.ok) throw new Error(`Failed to sync calendar: ${response.statusText}`);
      const result = await response.json();
      toast.success(`Successfully added ${result.created} bills to your Google Calendar`);
      setStatusMessage({ text: `${result.created} bills synced to Google Calendar`, type: 'success' });
    } catch (error) {
      console.error('Calendar sync error:', error);
      toast.error('Failed to sync bills to calendar');
      setStatusMessage({ text: 'Failed to sync bills to calendar', type: 'error' });
    } finally {
      setIsCalendarLoading(false);
    }
  }, [user, isCalendarConnected, allBills, showPaid]);

  const billToCalendarEvent = (bill: Bill) => {
    const { name, amount, dueDate, category, notes } = bill;
    const description = [
      `Amount: ${amount !== undefined ? amount.toLocaleString('en-US', { style: 'currency', currency: 'USD' }) : 'Not specified'}`,
      category ? `Category: ${category}` : '',
      notes ? `Notes: ${notes}` : '',
      '\n\nAdded by PayDay Pilot'
    ].filter(Boolean).join('\n');

    const startDateObj = parseLocalDateString(dueDate);
    if (!startDateObj) {
      console.error("billToCalendarEvent: Could not parse dueDate", dueDate);
      return null;
    }
    const endDateObj = new Date(startDateObj);
    endDateObj.setDate(startDateObj.getDate() + 1);

    return {
      summary: `Bill Due: ${name}`,
      description,
      start: { date: formatDateForInput(startDateObj) },
      end: { date: formatDateForInput(endDateObj) },
      colorId: '4',
      reminders: { useDefault: false, overrides: [{ method: 'popup', minutes: 60 * 24 }, { method: 'email', minutes: 60 * 24 * 2 }] }
    };
  };

  const handleOpenEditPanel = (bill: Bill) => {
    setEditingBill(bill);
    setIsEditPanelOpen(true);
  };

  const handleCloseEditPanel = () => {
    setIsEditPanelOpen(false);
    setEditingBill(null);
  };

  const handleSaveBill = async (updatedBill: Bill) => {
    const { id, ...billData } = updatedBill;
    try {
      if (id.startsWith('temp-')) {
        const billDataWithAmount = {
          ...billData,
          amount: billData.amount ?? 0 // Use nullish coalescing to provide a default
        };
        await storeAddBill(billDataWithAmount as Omit<BillFormData, 'id'>);
        toast.success(`${updatedBill.name} added successfully!`);
      } else {
        await storeUpdateBill(id, billData as Partial<Bill>);
        toast.success(`${updatedBill.name} updated.`);
      }
      handleCloseEditPanel();
    } catch (error) {
      console.error("Failed to save bill:", error);
      toast.error(`Failed to save ${updatedBill.name}.`);
    }
  };

  const handleDeleteBill = async (billId: string) => {
    try {
      await storeDeleteBill(billId);
      toast.success(`Bill deleted.`);
      handleCloseEditPanel();
    } catch (error) {
      console.error("Failed to delete bill:", error);
      toast.error('Failed to delete bill.');
    }
  };

  const handleOpenRenewModal = (billToRenew: Bill) => {
    if (!billToRenew || billToRenew.id.startsWith('temp-') || billToRenew.isRenewedOriginal) {
      toast.error("This bill cannot be renewed at this time.");
      return;
    }
    setBillToRenewDetails({ id: billToRenew.id, name: billToRenew.name, dueDate: billToRenew.dueDate });
    setIsRenewModalOpen(true);
    setIsEditPanelOpen(false);
  };

  const handleCloseRenewModal = () => {
    setIsRenewModalOpen(false);
    setBillToRenewDetails(null);
  };

  const handleConfirmRenewal = async (newDueDate: string, newAmount?: number, newNotes?: string) => {
    if (!billToRenewDetails) {
      toast.error("No bill selected for renewal.");
      return;
    }
    try {
      const renewedBill = await storeRenewBill(billToRenewDetails.id, newDueDate, newAmount, newNotes);
      if (renewedBill) {
        toast.success(`Bill "${renewedBill.name}" renewed successfully for ${newDueDate}.`);
        const newBillDate = parseLocalDateString(renewedBill.dueDate);
        if (newBillDate) {
          setCurrentDate(newBillDate);
          setSelectedDate(newBillDate);
        }
      }
      handleCloseRenewModal();
    } catch (error) {
      console.error("Failed to renew bill:", error);
      toast.error("Failed to renew bill. " + (error as Error).message);
    }
  };

  const generateCalendarDays = (year: number, month: number): Day[] => {
    const daysArray: Day[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const firstDayOfMonth = new Date(year, month, 1);
    // const lastDayOfMonth = new Date(year, month + 1, 0); // Not directly used, but good for context

    const startDayOfWeek = firstDayOfMonth.getDay();
    const prevMonthLastDay = new Date(year, month, 0);
    for (let i = 0; i < startDayOfWeek; i++) {
      const date = new Date(prevMonthLastDay.getFullYear(), prevMonthLastDay.getMonth(), prevMonthLastDay.getDate() - startDayOfWeek + 1 + i);
      daysArray.push({
        date, isCurrentMonth: false, isToday: date.getTime() === today.getTime(), isWeekend: date.getDay() === 0 || date.getDay() === 6,
        bills: (allBills || []).filter(bill => {
          const billDate = parseLocalDateString(bill.dueDate);
          return billDate && billDate.toDateString() === date.toDateString() && (showPaid || !bill.isPaid);
        }),
      });
    }

    const daysInCurrentMonth = getDaysInMonth(year, month);
    daysInCurrentMonth.forEach(date => {
      daysArray.push({
        date, isCurrentMonth: true, isToday: date.getTime() === today.getTime(), isWeekend: date.getDay() === 0 || date.getDay() === 6,
        bills: (allBills || []).filter(bill => {
          const billDate = parseLocalDateString(bill.dueDate);
          return billDate && billDate.toDateString() === date.toDateString() && (showPaid || !bill.isPaid);
        }),
      });
    });

    const nextMonthStartDay = new Date(year, month + 1, 1);
    const daysNeededFromNextMonth = (7 * 6) - daysArray.length;
    for (let i = 0; i < daysNeededFromNextMonth; i++) {
      const date = new Date(nextMonthStartDay.getFullYear(), nextMonthStartDay.getMonth(), i + 1);
      if (daysArray.length < 42) {
        daysArray.push({
          date, isCurrentMonth: false, isToday: date.getTime() === today.getTime(), isWeekend: date.getDay() === 0 || date.getDay() === 6,
          bills: (allBills || []).filter(bill => {
            const billDate = parseLocalDateString(bill.dueDate);
            return billDate && billDate.toDateString() === date.toDateString() && (showPaid || !bill.isPaid);
          }),
        });
      }
    }
    return daysArray.slice(0, 42);
  };

  const calendarDays = generateCalendarDays(getYear(currentDate), currentDate.getMonth());
  const dayHeaders = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

  const billsOnSelectedDate = selectedDate && allBills
    ? allBills.filter(bill => {
      const billDate = parseLocalDateString(bill.dueDate);
      return billDate && billDate.toDateString() === selectedDate.toDateString() && (showPaid || !bill.isPaid);
    })
    : [];

  const currentMonthBills = (allBills || []).filter(bill => {
    const billDate = parseLocalDateString(bill.dueDate);
    return billDate && billDate.getFullYear() === getYear(currentDate) && billDate.getMonth() === currentDate.getMonth();
  });
  const paidBillsCount = currentMonthBills.filter(b => b.isPaid).length;

  return (
    <div
      className={clsx(
        'flex flex-col font-sans text-gray-800 dark:text-gray-200 relative',
        compact
          ? 'h-full w-full bg-white dark:bg-gray-800 overflow-hidden calendar-compact'
          : 'md:flex-row bg-gray-100 dark:bg-gray-900 overflow-y-auto h-[calc(100vh-70px)]'
      )}
      style={compact ? { height: '100%', maxHeight: '100%' } : {}}
    >
      {/* Month Header - Only visible on small screens when not in compact mode */}
      {!compact && (
        <div className="flex flex-col items-center sm:hidden bg-white dark:bg-gray-800 w-full shadow-sm">
          {/* Month and Navigation Row */}
          <div className="flex w-full items-center justify-between px-3 py-2">
            <button
              onClick={goToPreviousMonth}
              className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-offset-gray-800 flex-shrink-0"
              aria-label="Previous month"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" /></svg>
            </button>
            <div className="text-center flex-grow px-1 min-w-0">
              <h2 className="text-base font-semibold text-gray-800 dark:text-gray-100 truncate">{getMonthName(currentDate)}</h2>
              <p className="text-xs text-gray-500 dark:text-gray-400">{getYear(currentDate)}</p>
            </div>
            <button
              onClick={goToNextMonth}
              className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-offset-gray-800 flex-shrink-0"
              aria-label="Next month"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" /></svg>
            </button>
          </div>

          {/* Today Button */}
          <div className="my-1">
            <button
              onClick={goToToday}
              className="px-4 py-1 text-xs text-blue-600 bg-white rounded-full shadow-sm border border-gray-200 hover:bg-gray-50 active:bg-gray-100 focus:outline-none dark:bg-gray-700 dark:text-blue-400 dark:border-gray-600 dark:hover:bg-gray-600 dark:active:bg-gray-500"
            >
              Today
            </button>
          </div>

          {/* Show Paid & Archives */}
          <div className="flex items-center justify-between w-full px-4 py-2 border-t border-gray-200 dark:border-gray-700">
            <label className="flex items-center text-xs text-gray-600 dark:text-gray-400">
              <input type="checkbox" checked={showPaid} onChange={(e) => setShowPaid(e.target.checked)} className="mr-1.5 h-3.5 w-3.5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-500 dark:focus:ring-offset-gray-800" />
              <span>Show Paid</span>
            </label>
            <button onClick={() => console.log("Archives clicked")} className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">Archives</button>
          </div>
        </div>
      )}


      {/* Main Calendar Area */}
      <main className={clsx(
        'flex-1 flex flex-col overflow-hidden',
        compact
          ? 'p-2 h-full'
          : 'p-1 sm:p-2 md:p-3 lg:p-4 overflow-y-auto',
        !compact && !isSummaryPanelVisible ? 'md:mr-0' : ''
      )}>
        {/* Top Navigation Bar */}
        <div className={clsx(
          'flex flex-col sm:flex-row sm:items-center justify-between border-b border-gray-300 dark:border-gray-700 gap-2 sm:gap-3',
          compact
            ? 'mb-2 pb-2'
            : 'mb-2 sm:mb-4 md:mb-6 pb-2 sm:pb-3 md:pb-4'
        )}>
          <div className="hidden sm:flex flex-col items-center gap-2 sm:flex-row sm:justify-start sm:items-center sm:space-x-3 px-2 sm:px-0">
            <div className="w-full flex flex-row justify-between items-center sm:w-auto sm:justify-center sm:space-x-2">
              <button
                onClick={goToPreviousMonth}
                className="w-10 h-10 flex items-center justify-center text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-full shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 active:bg-gray-200 dark:active:bg-gray-600"
                aria-label="Previous month"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="flex flex-col items-center">
                <span className="text-xl font-semibold text-gray-700 dark:text-gray-200">{getMonthName(currentDate)}</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">{getYear(currentDate)}</span>
              </div>
              <button
                onClick={goToNextMonth}
                className="w-10 h-10 flex items-center justify-center text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-full shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 active:bg-gray-200 dark:active:bg-gray-600"
                aria-label="Next month"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
            <button onClick={goToToday} className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 active:bg-gray-200 dark:active:bg-gray-600">Today</button>
          </div>
          <div className="hidden sm:flex items-center justify-between w-full sm:w-auto sm:justify-end sm:space-x-4 px-2 sm:px-0">
            <label className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <input type="checkbox" checked={showPaid} onChange={(e) => setShowPaid(e.target.checked)} className="mr-2 h-5 w-5 text-blue-600 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500" />
              <span className="whitespace-nowrap">Show Paid</span>
            </label>
            <button onClick={() => console.log("Archives clicked")} className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline px-2 py-1 border border-transparent hover:border-blue-200 dark:hover:border-blue-500 rounded-md">Archives</button>
          </div>
        </div>

        <div className="mb-3 pt-1 flex justify-center">
          <div className="flex space-x-1 bg-gray-200 dark:bg-gray-700 p-1 rounded-lg shadow-sm">
            {(['Month', 'Week', 'Agenda'] as ViewMode[]).map(mode => (
              <button
                key={mode}
                onClick={() => setViewMode(mode)}
                className={`px-4 py-1.5 text-sm font-medium rounded-md transition-colors ${viewMode === mode ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-white shadow' : 'text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 active:bg-gray-300'}`}
              >
                {mode}
              </button>
            ))}
          </div>
        </div>

        {viewMode === 'Month' && (
          <div className={clsx(
            'grid grid-cols-7 gap-px bg-gray-300 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 overflow-hidden flex-1',
            compact
              ? 'text-xs rounded-md'
              : 'rounded-lg shadow'
          )}>
            {dayHeaders.map(header => (
              <div
                key={header}
                className={clsx(
                  'py-1.5 sm:py-2 text-center text-[10px] sm:text-xs font-semibold text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 uppercase tracking-wider',
                  compact && 'py-1 sm:py-1.5'
                )}
              >
                {header}
              </div>
            ))}
            {calendarDays.map((day, index) => (
              <div
                key={index}
                className={clsx(
                  'p-1 sm:p-2 relative flex flex-col cursor-pointer transition-colors',
                  day.isCurrentMonth
                    ? 'bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-gray-700'
                    : 'bg-gray-100 dark:bg-slate-800 text-gray-400 dark:text-gray-500 hover:bg-gray-200 dark:hover:bg-slate-700',
                  selectedDate?.toDateString() === day.date.toDateString() && day.isCurrentMonth
                    ? '!bg-blue-100 dark:!bg-blue-900/70 border-2 border-blue-500 dark:border-blue-400'
                    : '',
                  day.isWeekend && day.isCurrentMonth ? 'bg-opacity-75 dark:bg-opacity-75' : '',
                  compact
                    ? 'min-h-[60px] h-[60px]'
                    : 'min-h-[45px] sm:min-h-[90px] md:min-h-[120px]'
                )}
                onClick={() => day.isCurrentMonth && setSelectedDate(day.date)}
              >
                <span
                  className={clsx(
                    'text-[10px] sm:text-xs md:text-sm font-medium mb-0.5 sm:mb-1',
                    day.isToday && day.isCurrentMonth
                      ? 'bg-blue-600 dark:bg-blue-500 text-white dark:text-gray-100 rounded-full h-5 w-5 sm:h-6 sm:w-6 flex items-center justify-center'
                      : '',
                    !day.isCurrentMonth
                      ? 'text-gray-400 dark:text-gray-500'
                      : 'text-gray-700 dark:text-gray-300'
                  )}
                >
                  {day.date.getDate()}
                </span>
                <div className={clsx('flex-grow overflow-y-auto text-left space-y-0.5 sm:space-y-1', compact && 'space-y-0')}> {/* bill chips */}
                  {day.bills.map(bill => (
                    <div
                      key={bill.id}
                      onClick={e => {
                        e.stopPropagation();
                        handleOpenEditPanel(bill);
                      }}
                      className={clsx(
                        'text-[9px] sm:text-xs p-0.5 sm:p-1 rounded text-white truncate cursor-pointer hover:opacity-80 touch-manipulation',
                        bill.isPaid
                          ? 'bg-green-500 dark:bg-green-600'
                          : 'bg-blue-500 dark:bg-blue-600'
                      )}
                      title={`${bill.name}${bill.amount ? `: ${bill.amount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}` : ''}${bill.notes ? ` (${bill.notes})` : ''}`}
                    >
                      {bill.name}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
        {viewMode === 'Week' && <div className="p-4 text-center text-gray-500 dark:text-gray-400">Week View Coming Soon!</div>}
        {viewMode === 'Agenda' && <div className="p-4 text-center text-gray-500 dark:text-gray-400">Agenda View Coming Soon!</div>}
      </main>

      {!compact && isSummaryPanelVisible && (
        <aside className={`fixed md:static inset-x-0 bottom-0 md:w-80 lg:w-96 bg-white dark:bg-gray-800 border-t md:border-t-0 md:border-l border-gray-300 dark:border-gray-700 shadow-lg overflow-y-auto flex-shrink-0 transform transition-transform duration-300 ease-in-out md:transform-none ${selectedDate ? 'translate-y-0' : 'translate-y-[calc(100%-2.5rem)]'} md:translate-y-0 max-h-[70vh] sm:max-h-[80vh] md:max-h-none`}>
          <div className="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700">
            <div className="md:hidden flex items-center justify-center cursor-pointer flex-1" onClick={() => selectedDate ? setSelectedDate(null) : setSelectedDate(new Date())}>
              <div className="w-10 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            </div>
            <button
              onClick={() => setIsSummaryPanelVisible(false)}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              aria-label="Close summary panel"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="p-3 sm:p-4 md:p-6 space-y-3 sm:space-y-4 overflow-y-auto">
            <div className="mb-4 md:mb-6">
              <h3 className="text-base md:text-lg font-semibold text-gray-700 dark:text-gray-200 mb-1">{selectedDate ? selectedDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' }) : 'No Date Selected'}</h3>
              <p className="text-xs md:text-sm text-gray-500 dark:text-gray-400">{selectedDate ? getYear(selectedDate) : ''}</p>
            </div>
            <button
              onClick={handleSyncToGoogleCalendar} // Assuming this handler exists
              className="w-full flex items-center justify-center gap-2 px-3 py-2 text-xs font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 touch-manipulation"
              disabled={isCalendarLoading}
            >
              {isCalendarLoading ? (
                <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2" className="h-4 w-4 text-white">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
              )}
              Sync Bills to Google Calendar
            </button>
            {billsOnSelectedDate.length > 0 ? (
              <div className="mb-4 md:mb-6">
                <h4 className="text-sm md:text-md font-semibold text-gray-600 dark:text-gray-300 mb-2">Due on this day:</h4>
                <ul className="space-y-2 md:space-y-3">
                  {billsOnSelectedDate.map(bill => (
                    <li key={bill.id} className="p-2 md:p-3 bg-gray-50 dark:bg-slate-700 rounded-lg shadow-sm">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-sm md:text-base text-gray-700 dark:text-gray-200">{bill.name}</span>
                        {bill.amount !== undefined && (
                          <span className={`text-xs md:text-sm font-semibold ${bill.isPaid ? 'text-gray-500 dark:text-gray-400 line-through' : 'text-green-600 dark:text-green-400'}`}>
                            {bill.amount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                          </span>
                        )}
                      </div>
                      {bill.notes && <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{bill.notes}</p>}
                      <button
                        onClick={() => handleOpenEditPanel(bill)}
                        className={`mt-2 w-full text-xs px-3 py-2 text-white rounded-lg focus:outline-none focus:ring-2 touch-manipulation ${bill.isPaid ? 'bg-gray-400 dark:bg-gray-600 hover:bg-gray-500 dark:hover:bg-gray-500 focus:ring-gray-300' : 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-400'}`}
                      >
                        {bill.isPaid ? 'View/Edit Details' : 'Edit / Mark Paid'}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              selectedDate && <p className="text-xs md:text-sm text-gray-500 dark:text-gray-400 mb-4 md:mb-6">No bills due on this day.</p>
            )}
            <div className="p-3 md:p-4 bg-gray-50 dark:bg-slate-700 rounded-lg shadow-sm">
              <h4 className="text-sm md:text-md font-semibold text-gray-600 dark:text-gray-300 mb-2 md:mb-3">Month Summary ({getMonthName(currentDate)})</h4>
              <div className="space-y-1 text-xs md:text-sm">
                <div className="flex justify-between"><span className="text-gray-600 dark:text-gray-400">Total Bills:</span><span className="font-medium text-gray-800 dark:text-gray-100">{currentMonthBills.length}</span></div>
                <div className="flex justify-between"><span className="text-gray-600 dark:text-gray-400">Paid:</span><span className="font-medium text-green-600 dark:text-green-400">{paidBillsCount}</span></div>
                <div className="flex justify-between"><span className="text-gray-600 dark:text-gray-400">Remaining:</span><span className="font-medium text-red-600 dark:text-red-400">{currentMonthBills.length - paidBillsCount}</span></div>
              </div>
            </div>
          </div>
        </aside>
      )}

      <EditBillPanel bill={editingBill} isOpen={isEditPanelOpen} onClose={handleCloseEditPanel} onSave={handleSaveBill} onDelete={handleDeleteBill} onOpenRenewModal={handleOpenRenewModal} />
      {billToRenewDetails && (<RenewBillModal isOpen={isRenewModalOpen} billName={billToRenewDetails.name} originalDueDate={billToRenewDetails.dueDate} onClose={handleCloseRenewModal} onConfirm={handleConfirmRenewal} />)}
      {showGoogleAuthModal && (<div className="fixed inset-0 z-[100] bg-black bg-opacity-50 flex justify-center items-center p-4"><div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full transition-transform transform duration-300 ease-out"><div className="flex items-center justify-between p-5 border-b border-gray-200 dark:border-gray-700"><h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Sign in with Google</h3><Image src="https://www.gstatic.com/images/branding/product/1x/google_2022_48dp.png" alt="Google Logo" width={24} height={24} /></div><div className="p-6"><p className="mb-4"><strong className="font-medium">PayDay Pilot</strong> wants to access your Google Account</p><p className="mb-2">This will allow PayDay Pilot to:</p><ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-300 pl-4 mb-4"><li>View your calendars</li><li>View and edit events on all your calendars</li></ul><p className="mb-1">Make sure you trust PayDay Pilot.</p><p><a href="https://support.google.com/accounts/answer/3466521" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline text-sm">Learn about the risks</a></p></div><div className="flex justify-end gap-3 p-5 border-t border-gray-200 dark:border-gray-700"><button onClick={handleGoogleAuthDeny} className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors">Deny</button><button onClick={handleGoogleAuthConfirm} className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">Allow</button></div></div></div>)}
      {statusMessage.text && statusMessage.type && (<div className={`fixed top-5 left-1/2 transform -translate-x-1/2 z-50 px-5 py-3 rounded-lg shadow-lg transition-opacity duration-300 max-w-md text-center ${statusMessage.type === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : statusMessage.type === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'}`}>{statusMessage.text}</div>)}

      {/* Floating button to show summary panel when hidden */}
      {!compact && !isSummaryPanelVisible && (
        <button
          onClick={() => setIsSummaryPanelVisible(true)}
          className="fixed bottom-4 right-4 z-50 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-105"
          aria-label="Show summary panel"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        </button>
      )}


    </div>
  );
};

export default CalendarApp;
