'use client';

import { BaseWidget } from './BaseWidget';
import { useState, useEffect } from 'react';
import { useUserPreferences } from '@/stores/userPreferencesStore';

interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
}

export default function RecentTransactionsWidget() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const { trackAction } = useUserPreferences();

  // Mock data fetch
  useEffect(() => {
    const timer = setTimeout(() => {
      // This would be an API call in production
      setTransactions([
        {
          id: '1',
          date: '2025-04-23',
          description: 'Grocery Store',
          amount: 85.42,
          type: 'expense',
          category: 'Groceries'
        },
        {
          id: '2',
          date: '2025-04-22',
          description: 'Salary Deposit',
          amount: 2500,
          type: 'income',
          category: 'Salary'
        },
        {
          id: '3',
          date: '2025-04-21',
          description: 'Gas Station',
          amount: 45.30,
          type: 'expense',
          category: 'Transportation'
        },
        {
          id: '4',
          date: '2025-04-20',
          description: 'Restaurant',
          amount: 32.50,
          type: 'expense',
          category: 'Dining'
        },
        {
          id: '5',
          date: '2025-04-19',
          description: 'Freelance Payment',
          amount: 350,
          type: 'income',
          category: 'Freelance'
        }
      ]);
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (dateString === today.toISOString().split('T')[0]) {
      return 'Today';
    } else if (dateString === yesterday.toISOString().split('T')[0]) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  return (
    <BaseWidget 
      title="Recent Transactions" 
      icon={
        <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
      }
    >
      {loading ? (
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse flex gap-3">
              <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="flex-1 space-y-2 py-1">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-1">
          {transactions.map((transaction) => (
            <div 
              key={transaction.id}
              className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-lg transition-colors cursor-pointer"
              onClick={() => trackAction('view_transaction_details')}
            >
              <div className="flex items-center gap-3">
                <div className={`
                  w-12 h-12 rounded-lg flex items-center justify-center
                  ${transaction.type === 'income' 
                    ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
                    : 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400'}
                `}>
                  {transaction.type === 'income' ? (
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>
                  ) : (
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                  )}
                </div>
                
                <div>
                  <div className="font-medium">{transaction.description}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(transaction.date)} • {transaction.category}
                  </div>
                </div>
              </div>
              
              <div className={`font-medium ${
                transaction.type === 'income'
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {transaction.type === 'income' ? '+' : '-'}
                {formatCurrency(transaction.amount)}
              </div>
            </div>
          ))}
          
          <button
            className="w-full mt-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:underline"
            onClick={() => trackAction('view_all_transactions')}
          >
            View all transactions
          </button>
        </div>
      )}
    </BaseWidget>
  );
}