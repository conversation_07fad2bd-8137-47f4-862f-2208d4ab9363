import { renderHook } from '@testing-library/react'
import { useAuth } from '../useAuth'

// We need to mock the authStore since useAuth is just a thin wrapper around it
jest.mock('@/stores/authStore', () => ({
  useAuthStore: jest.fn().mockReturnValue({
    user: {
      uid: 'test-user-id',
      email: '<EMAIL>',
      displayName: 'Test User'
    },
    isLoading: false,
    isAuthenticated: true,
    signOut: jest.fn().mockResolvedValue(undefined)
  })
}))

describe('useAuth', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('returns user authentication state', () => {
    const { result } = renderHook(() => useAuth())

    expect(result.current.user).toEqual({
      uid: 'test-user-id',
      email: '<EMAIL>',
      displayName: 'Test User'
    })
    expect(result.current.loading).toBe(false)
    expect(result.current.isAuthenticated).toBe(true)
  })
})
