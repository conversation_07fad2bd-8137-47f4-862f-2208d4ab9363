# System Patterns & Standards

*(This file consolidates project-specific patterns and standards, potentially overriding or specializing global rules.)*

## General Code Style & Formatting (Inherited from Global Rules)

*   Use functional and declarative programming patterns; avoid classes.
*   Prefer iteration and modularization over code duplication.
*   Use descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`).
*   Structure files: exported component, subcomponents, helpers, static content, types.

## Naming Conventions (Inherited from Global Rules)

*   Use lowercase with dashes for directories (e.g., `components/auth-wizard`).
*   Favor named exports for components.

## TypeScript Best Practices (Inherited from Global Rules)

*   Use TypeScript for all code; prefer interfaces over types.
*   Avoid `any` and `enums`; use explicit types and maps instead.
*   Use functional components with TypeScript interfaces.
*   Enable strict mode in TypeScript for better type safety.
*   **Firestore Snapshots:** Use explicit types for Firestore document snapshots in callbacks (Memory: 90796e67...):
    ```typescript
    snapshot.forEach((doc: FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>) => {
      // Implementation
    });
    ```

## Syntax & Formatting (Inherited from Global Rules)

*   Use the `function` keyword for pure functions.
*   Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
*   Use declarative JSX.
*   Use Prettier for consistent code formatting.

## Styling & UI

*   Use Tailwind CSS for styling.
*   Implement responsive design using Tailwind's responsive classes.
*   Ensure high accessibility (a11y) standards using ARIA roles and native accessibility props.
*   **Icon Sizing:** Ensure consistent icon sizing (text-sm/text-base preferred). Avoid disproportionately large icons, especially emojis. (Memory: 559755bb...)

## State Management

*   (To be defined - currently assumed React Context/State)

## Build/Deployment

*   **Firebase Admin Initialization:** Safely handle build-time vs. server-runtime initialization. Use dummy implementations during build. (Memory: 90796e67...)
    ```typescript
    const isServer = typeof window === 'undefined';
    const isBuildTime = process.env.NODE_ENV === 'production' && 
                      (process.env.NEXT_PHASE === 'phase-production-build' || process.env.NETLIFY);
    
    if (isServer && !isBuildTime) { // Real implementation } 
    else { // Dummy implementation }
    ```
*   **Deployment:** Use Netlify CLI: `netlify deploy --build --prod`. (Memory: 56237378..., 0326267a...)
*   Ensure `NEXT_PHASE` is set in `netlify.toml` for builds. (Memory: 90796e67...)

*Note: Add new project-specific patterns or deviations from global rules here.*
