import { ErrorBoundary } from "@/components/error/ErrorBoundary";
import { AppLayout } from "@/components/layout/AppLayout";
import MotionProvider from '@/components/layout/MotionProvider';
import { NotificationPermission } from "@/components/pwa/NotificationPermission";
import { PwaIcons } from "@/components/pwa/PwaIcons";
import { BillsProvider } from "@/stores/billsStore";
import { FinancialProvider } from "@/stores/financialStore";
import { ThemeProvider } from "@/stores/themeStore";
import type { Metadata, Viewport } from "next";
import { Inter, Lato, Playfair_Display, Roboto_Mono } from "next/font/google";
import React from 'react';
import "./globals.css";

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap'
});

const roboto_mono = Roboto_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap'
});

const lato = Lato({
  subsets: ['latin'],
  weight: ['400', '700'],
  variable: '--font-lato',
  display: 'swap'
});

const playfair = Playfair_Display({
  subsets: ['latin'],
  weight: ['400', '700'],
  variable: '--font-playfair',
  display: 'swap'
});

export const metadata: Metadata = {
  title: "PayDay Pilot",
  description: "Track, manage, and forecast your bills with ease",
  icons: {
    apple: "/apple-icon.png",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className={`${inter.variable} ${roboto_mono.variable} ${lato.variable} ${playfair.variable}`}>
      <head>
        <PwaIcons />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="theme-color" content="#f8f5e6" />
      </head>
      <body className="min-h-screen bg-cream">
        <ErrorBoundary>
          <ThemeProvider>
            <BillsProvider>
              <FinancialProvider>
                <MotionProvider>
                  <NotificationPermission reason="Get timely reminders about upcoming bills and important updates.">
                    <AppLayout>{children}</AppLayout>
                  </NotificationPermission>
                </MotionProvider>
              </FinancialProvider>
            </BillsProvider>
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
