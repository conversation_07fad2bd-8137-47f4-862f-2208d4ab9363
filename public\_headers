/*
  Cache-Control: public, max-age=0, must-revalidate
  Pragma: no-cache
  X-Frame-Options: DENY
  Strict-Transport-Security: max-age=63072000; includeSubDomains; preload
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  # TODO: Replace inline/unsafe policies with nonces/hashes in 2025
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.onesignal.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' https://www.googleapis.com https://fcm.googleapis.com https://firebase.googleapis.com https://apis.google.com https://www.google-analytics.com https://www.googletagmanager.com https://cdn.onesignal.com https://*.onesignal.com; frame-ancestors 'none';
  Permissions-Policy: geolocation=(), camera=(), microphone=(), usb=(), bluetooth=(), clipboard-read=(self), clipboard-write=(self), fullscreen=(self), payment=(), interest-cohort=(), browsing-topics=();

# Allow PWA assets to be cached
/sw.js
  Cache-Control: public, max-age=0, must-revalidate

/workbox-*.js
  Cache-Control: public, max-age=31536000

/icons/*
  Cache-Control: public, max-age=31536000

/manifest.json
  Cache-Control: public, max-age=0, must-revalidate

/custom-sw.js
  Cache-Control: public, max-age=0, must-revalidate

/firebase-messaging-sw.js
  Cache-Control: public, max-age=0, must-revalidate

/register-sw.js
  Cache-Control: public, max-age=0, must-revalidate

# Enable long-term caching for Next static assets
/_next/static/*
  Cache-Control: public, max-age=31536000, immutable

# Cache optimized images aggressively (ensure file names are content-hashed or stable)
/_next/image*
  Cache-Control: public, max-age=31536000, immutable

/images/optimized/*
  Cache-Control: public, max-age=31536000, immutable
