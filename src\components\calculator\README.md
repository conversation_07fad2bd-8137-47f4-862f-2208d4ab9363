# Financial Calculator Component

A comprehensive financial calculator component designed specifically for bill management and budgeting within the Payday Pilot application.

## Features

### Core Calculator Functions
- ✅ Basic arithmetic operations (+, -, ×, ÷)
- ✅ Decimal support with proper formatting
- ✅ Clear (AC) and backspace functionality
- ✅ Error handling for invalid operations (division by zero, etc.)

### Financial-Specific Features
- ✅ **Percentage calculations** - Perfect for calculating tips, taxes, and discounts
- ✅ **Memory functions** (M+, M-, MR, MC) - Store and recall bill amounts
- ✅ **Currency formatting** - Automatic number formatting with commas for large amounts
- ✅ **Smart decimal handling** - Removes trailing zeros, formats appropriately

### Modern UI Features
- ✅ **Touch-friendly design** - 44px minimum touch targets for mobile
- ✅ **Smooth animations** - Framer Motion powered button interactions
- ✅ **Large, readable display** - Clear number formatting and expression preview
- ✅ **Dark mode support** - Consistent with app's theme system
- ✅ **Responsive layout** - Works perfectly on both desktop and mobile

### History Function
- ✅ **Persistent calculation history** - Stored in localStorage
- ✅ **Reusable calculations** - Click any history item to reuse the result
- ✅ **Clear history option** - Remove all history with one click
- ✅ **Timestamped entries** - See when each calculation was performed
- ✅ **Animated history panel** - Smooth show/hide with staggered item animations

### Additional Features
- ✅ **Copy to clipboard** - Copy results with one click
- ✅ **Full keyboard support** - Use number keys, operators, Enter, Escape, etc.
- ✅ **Memory indicator** - Shows current memory value when stored
- ✅ **Expression preview** - See your calculation as you build it
- ✅ **Smart percentage handling** - Context-aware percentage calculations

## Usage

### In Dashboard Modal
The calculator is integrated into the dashboard as a modal card:

```tsx
import { FinancialCalculator } from '@/components/calculator/FinancialCalculator';

// Used in dashboard modal
<ClickableCard title="Financial Calculator" modalSize="lg">
  <FinancialCalculator />
</ClickableCard>
```

### Standalone Usage
```tsx
import { FinancialCalculator } from '@/components/calculator';

function MyComponent() {
  return (
    <div className="calculator-container">
      <FinancialCalculator />
    </div>
  );
}
```

## Keyboard Shortcuts

| Key | Action |
|-----|--------|
| `0-9` | Number input |
| `+` | Addition |
| `-` | Subtraction |
| `*` | Multiplication |
| `/` | Division |
| `.` or `,` | Decimal point |
| `=` or `Enter` | Calculate result |
| `Backspace` | Delete last digit |
| `Escape` | Clear all |
| `%` | Percentage |

## Financial Use Cases

### Tip Calculation
1. Enter bill amount: `45.50`
2. Press `×` then `15` then `%` for 15% tip
3. Result shows tip amount: `6.83`

### Tax Calculation
1. Enter subtotal: `100`
2. Press `×` then `8.25` then `%` for 8.25% tax
3. Result shows tax amount: `8.25`

### Discount Calculation
1. Enter original price: `200`
2. Press `×` then `20` then `%` for 20% discount
3. Result shows discount amount: `40`

### Memory for Bill Tracking
1. Calculate first bill: `125.50`
2. Press `M+` to store in memory
3. Calculate second bill: `89.75`
4. Press `M+` to add to memory
5. Press `MR` to recall total: `215.25`

## Technical Details

- **Framework**: React with TypeScript
- **Animations**: Framer Motion
- **Storage**: localStorage via useLocalStorage hook
- **Styling**: Tailwind CSS with custom calculator styles
- **Testing**: Jest with React Testing Library
- **Accessibility**: Full keyboard navigation, proper ARIA labels

## File Structure

```
src/components/calculator/
├── FinancialCalculator.tsx    # Main component
├── index.ts                   # Export file
├── README.md                  # This documentation
└── __tests__/
    └── FinancialCalculator.test.tsx  # Test suite
```
