'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';

interface NotificationPermissionProps {
  reason: string;
  children?: React.ReactNode;
}

const PROMPT_STORAGE_KEY = 'notification_prompt_timestamp';
const PROMPT_INTERVAL = 7 * 24 * 60 * 60 * 1000; // 7 days

export function NotificationPermission({ reason, children }: NotificationPermissionProps) {
  const [permissionState, setPermissionState] = useState<'default' | 'granted' | 'denied' | 'unsupported' | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    if (typeof window === 'undefined') return;
    if (!('Notification' in window)) {
      setPermissionState('unsupported');
      return;
    }

    const currentPermission = Notification.permission as 'default' | 'granted' | 'denied';
    setPermissionState(currentPermission);

    if (currentPermission === 'default') {
      try {
        const lastPromptValue = localStorage.getItem(PROMPT_STORAGE_KEY);
        const lastPrompt = lastPromptValue ? parseInt(lastPromptValue, 10) : 0;
        const now = Date.now();
        if (!lastPrompt || now - lastPrompt > PROMPT_INTERVAL) {
          setShowPrompt(true);
        }
      } catch (error) {
        console.warn('Unable to read notification prompt preference:', error);
        setShowPrompt(true);
      }
    }
  }, []);

  const requestPermission = async () => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return;
    }

    try {
      localStorage.setItem(PROMPT_STORAGE_KEY, Date.now().toString());
    } catch (error) {
      console.warn('Unable to persist notification prompt timestamp:', error);
    }

    try {
      const permission = await Notification.requestPermission();
      setPermissionState(permission as 'default' | 'granted' | 'denied');
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    } finally {
      setShowPrompt(false);
    }
  };

  const dismissPrompt = () => {
    try {
      localStorage.setItem(PROMPT_STORAGE_KEY, Date.now().toString());
    } catch (error) {
      console.warn('Unable to persist notification prompt dismissal:', error);
    }
    setShowPrompt(false);
  };

  if (!isMounted) {
    return <>{children}</>;
  }

  if (permissionState === 'granted' || permissionState === 'denied' || permissionState === 'unsupported') {
    return <>{children}</>;
  }

  if (showPrompt) {
    return (
      <div className="mb-4 rounded-lg border border-gray-200 bg-white p-4 shadow-md dark:border-gray-700 dark:bg-gray-800">
        <div className="flex flex-col space-y-3">
          <div className="flex items-start">
            <div className="flex-shrink-0 text-green-500 dark:text-green-400">
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Enable notifications?</h3>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {reason || 'Get timely updates about upcoming bills and reminders.'}
              </p>
              <div className="mt-3 flex space-x-2">
                <Button onClick={requestPermission} variant="primary" size="sm">
                  Enable
                </Button>
                <Button onClick={dismissPrompt} variant="outline" size="sm">
                  Not now
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
