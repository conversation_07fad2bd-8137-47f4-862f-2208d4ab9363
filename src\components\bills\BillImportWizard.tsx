'use client';

import { useBillsStore } from '@/stores/billsStore';
import {
  Bill,
  BILL_CATEGORIES,
  BillFormData
} from '@/types/bill';
import { suggestCategory } from '@/utils/billIntelligence';
import {
  cleanImportAmount,
  detectDuplicates,
  detectVendor,
  parseImportDate,
  suggestTags,
  trimImportText
} from '@/utils/importUtils';
import { Dialog } from '@headlessui/react';
import { addMonths, differenceInDays, format, isValid, parse } from 'date-fns';
import { useEffect, useMemo, useState } from 'react';

interface BillImportWizardProps {
  isOpen: boolean;
  onClose: () => void;
}

type ImportStep = 'upload' | 'mapColumns' | 'preview' | 'results';

interface ParsedRow {
  rowIndex: number;
  data: Record<string, any>;
}

interface UploadStepProps {
  onFileParsed: (headers: string[], data: ParsedRow[]) => void;
  onError: (message: string) => void;
}

function UploadStep({ onFileParsed, onError }: UploadStepProps) {
  // XLSX functionality has been removed. This step now informs the user.
  useEffect(() => {
    onError("Spreadsheet import functionality is temporarily unavailable due to library removal.");
  }, [onError]);

  return (
    <div className="p-6 text-center">
      <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100 mb-2">Upload Spreadsheet</h3>
      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-700 rounded-md">
        <p className="text-sm text-yellow-700 dark:text-yellow-200">
          Spreadsheet import functionality (Excel/CSV) is currently unavailable.
        </p>
        <p className="text-xs text-yellow-600 dark:text-yellow-300 mt-1">
          The &apos;xlsx&apos; library was removed to address security vulnerabilities. This feature will be re-evaluated in the future.
        </p>
      </div>
      {/* Optionally, you can keep the "Tips" section if it's generally useful, or remove it. */}
      {/* For now, let's keep it simple and focused on the unavailability message. */}
    </div>
  );
}

interface TargetField {
  key: keyof Bill | 'ignore';
  label: string;
  required?: boolean;
  commonHeaders: string[];
  validator?: (value: any) => string | null;
}

const TARGET_FIELDS: TargetField[] = [
  { 
    key: 'name', 
    label: 'Bill Name / Description', 
    required: true, 
    commonHeaders: ['name', 'bill', 'description', 'item', 'service', 'payee', 'vendor', 'merchant'],
    validator: (value) => !value || String(value).trim() === '' ? 'Bill name is required' : null
  },
  { 
    key: 'amount', 
    label: 'Amount', 
    required: true, 
    commonHeaders: ['amount', 'total', 'price', 'cost', 'payment', 'charge', 'fee', 'balance'],
    validator: (value) => {
      const num = cleanImportAmount(value);
      if (num === null) return 'Amount is required';
      if (num < 0) return 'Amount must be positive';
      return null;
    }
  },
  { 
    key: 'dueDate', 
    label: 'Due Date', 
    required: true, 
    commonHeaders: ['due date', 'date', 'due', 'payment date', 'deadline', 'bill date'],
    validator: (value) => {
      const date = parseImportDate(value);
      return !date ? 'Valid due date is required' : null;
    }
  },
  { 
    key: 'category', 
    label: 'Category', 
    commonHeaders: ['category', 'type', 'classification', 'group'],
    validator: (value) => {
      if (!value) return null; // Optional field
      const category = String(value).trim();
      return !BILL_CATEGORIES.includes(category) ? `Category '${category}' is not valid` : null;
    }
  },
  { 
    key: 'notes', 
    label: 'Notes / Memo', 
    commonHeaders: ['notes', 'memo', 'details', 'comment', 'description', 'remarks']
  },
  { 
    key: 'vendor' as keyof Bill, 
    label: 'Vendor / Merchant', 
    commonHeaders: ['vendor', 'merchant', 'payee', 'company', 'business', 'provider']
  },
  { 
    key: 'isRecurring', 
    label: 'Recurring', 
    commonHeaders: ['recurring', 'repeat', 'frequency', 'subscription', 'monthly']
  },
  { 
    key: 'reminderDays', 
    label: 'Reminder Days', 
    commonHeaders: ['reminder', 'alert', 'notification', 'days before']
  },
];

interface ColumnMapping {
  [key: string]: string | null | 'ignore';
}

interface MapColumnsStepProps {
  headers: string[];
  sampleData: ParsedRow[];
  initialMapping: ColumnMapping;
  onMappingConfirmed: (mapping: ColumnMapping) => void;
  onBack: () => void;
}

function MapColumnsStep({ headers, sampleData, initialMapping, onMappingConfirmed, onBack }: MapColumnsStepProps) {
  const [mapping, setMapping] = useState<ColumnMapping>(initialMapping);

  const handleMappingChange = (targetKey: string, sourceHeaderIndex: string) => {
    setMapping((prev) => ({
      ...prev,
      [targetKey]: sourceHeaderIndex === 'ignore' ? 'ignore' : sourceHeaderIndex !== '' ? sourceHeaderIndex : null,
    }));
  };

  const isMappingComplete = useMemo(() => {
    return TARGET_FIELDS.every((field) => !field.required || (mapping[field.key] !== null && mapping[field.key] !== 'ignore'));
  }, [mapping]);

  const sampleRows = sampleData.slice(0, 3).map((row) => {
    const rowData: string[] = [];
    headers.forEach((_, index) => {
      const value = row.data[headers[index]];
      rowData.push(value === null || value === undefined ? '' : String(value));
    });
    return rowData;
  });

  return (
    <div className="p-6">
      <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">Map Columns</h3>
      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
        Match the columns from your spreadsheet (left) to the corresponding bill fields in the application (right).
        We&apos;ve made some initial guesses based on your column headers.
      </p>

      <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900 rounded-md text-xs text-blue-800 dark:text-blue-200">
        <h4 className="font-medium mb-1">Column Mapping Help:</h4>
        <ul className="list-disc pl-4 space-y-1">
          <li><strong>Bill Name</strong>: The name or description of the bill (required)</li>
          <li><strong>Amount</strong>: The payment amount, with or without currency symbols (required)</li>
          <li><strong>Due Date</strong>: When the bill is due, in any standard date format (required)</li>
          <li><strong>Category</strong>: Bill category (e.g., Utilities, Housing, Entertainment)</li>
          <li><strong>Vendor</strong>: The company or service provider name</li>
          <li><strong>Notes</strong>: Any additional information about the bill</li>
          <li><strong>Recurring</strong>: Whether the bill repeats (values like &quot;yes&quot;, &quot;monthly&quot;, &quot;1&quot; work)</li>
        </ul>
        <p className="mt-2 italic">Fields marked with * are required. For other fields, select &quot;Ignore this field&quot; if your spreadsheet doesn&apos;t have matching data.</p>
      </div>

      <div className="mt-4 mb-6 overflow-x-auto">
        <p className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Sample Data Preview:</p>
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              {headers.map((header, index) => (
                <th key={index} className="px-3 py-1.5 text-left font-medium text-gray-500 dark:text-gray-300 tracking-wider truncate max-w-[150px]">
                  {header || `Column ${index + 1}`}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
            {sampleRows.map((row, rowIndex) => (
              <tr key={rowIndex}>
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex} className="px-3 py-1.5 whitespace-nowrap text-gray-700 dark:text-gray-300 truncate max-w-[150px]">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="space-y-4">
        {TARGET_FIELDS.map((targetField) => (
          <div key={targetField.key} className="grid grid-cols-2 gap-4 items-center">
            <label htmlFor={`map-${targetField.key}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {targetField.label}
              {targetField.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <select
              id={`map-${targetField.key}`}
              name={`map-${targetField.key}`}
              value={mapping[targetField.key] === null ? '' : mapping[targetField.key] || ''}
              onChange={(e) => handleMappingChange(targetField.key, e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
            >
              <option value="" disabled>
                Select Spreadsheet Column...
              </option>
              <option value="ignore">Ignore this field</option>
              {headers.map((header, index) => (
                <option key={index} value={header}>
                  {header}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>

      <div className="mt-8 pt-5 border-t border-gray-200 dark:border-gray-700 flex justify-between">
        <button onClick={onBack} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600">
          Back
        </button>
        <button
          onClick={() => onMappingConfirmed(mapping)}
          disabled={!isMappingComplete}
          className={`px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
            isMappingComplete ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed dark:bg-gray-600'
          }`}
        >
          Next (Preview Import)
        </button>
      </div>
      {!isMappingComplete && (
        <p className="mt-2 text-xs text-red-600 dark:text-red-400">
          Please map all required fields (*) before continuing.
        </p>
      )}
    </div>
  );
}

interface ProcessedBillPreview {
  originalRowIndex: number;
  rawData: Record<string, any>;
  processedData: {
    name?: string;
    amount?: number;
    dueDate?: Date | null; // Store as Date internally for easier comparison
    category?: string;
    notes?: string;
    isRecurring?: boolean;
    reminderDays?: number;
    vendor?: string;
    tags?: string[];
    description?: string;
    importSource?: string;
  };
  errors: string[];
  warnings: string[];
  isDuplicateInFile: boolean;
  isDuplicateExisting: boolean;
  duplicateScore?: number;
  potentialDuplicates?: { bill: Bill; score: number }[];
  isIgnored: boolean;
  canImport: boolean;
  autoDetectedCategory?: boolean;
}

interface PreviewStepProps {
  previewData: ProcessedBillPreview[];
  columnMapping: ColumnMapping;
  onConfirmImport: (dataToImport: ProcessedBillPreview[]) => void;
  onBack: () => void;
}

function PreviewStep({ previewData, onConfirmImport, onBack }: PreviewStepProps) {
  // Count rows with errors, warnings, and duplicates
  const rowsWithErrors = previewData.filter((row) => row.errors.length > 0 && !row.canImport); // Errors preventing import
  const rowsWithWarnings = previewData.filter((row) => row.warnings.length > 0 && row.canImport); // Importable but has warnings
  const fileDuplicates = previewData.filter((row) => row.isDuplicateInFile);
  const existingDuplicates = previewData.filter((row) => row.isDuplicateExisting && !row.isDuplicateInFile); // Avoid double counting

  // Determine which rows will actually be imported (must be importable and not a duplicate)
  const rowsToImport = previewData.filter((row) => row.canImport && !row.isDuplicateInFile && !row.isDuplicateExisting);
  const totalRowsSkipped = previewData.length - rowsToImport.length;

  const displayHeaders = ['Name', 'Amount', 'Due Date', 'Category', 'Vendor', 'Tags', 'Status'];

  return (
    <div className="p-4">
      <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">Preview Import</h3>
      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
        Review the cleaned data below. Rows with critical errors or marked as duplicates will be skipped.
        ({rowsToImport.length} out of {previewData.length} rows will be imported).
      </p>

      <div className="mt-3 p-2 bg-blue-50 dark:bg-blue-900 border border-blue-300 dark:border-blue-700 rounded-md">
        <p className="text-xs text-blue-800 dark:text-blue-200 font-medium">
          Import Summary:
        </p>
        <ul className="text-xs text-blue-700 dark:text-blue-300 mt-1 space-y-1">
          <li>• <strong>{rowsToImport.length}</strong> row(s) ready for import</li>
          {rowsWithWarnings.length > 0 && (
            <li>• {rowsWithWarnings.length} row(s) have warnings (will be imported)</li>
          )}
          {(fileDuplicates.length > 0 || existingDuplicates.length > 0) && (
             <li>• {fileDuplicates.length + existingDuplicates.length} duplicate row(s) will be skipped</li>
          )}
          {rowsWithErrors.length > 0 && (
            <li>• {rowsWithErrors.length} row(s) with critical errors will be skipped</li>
          )}
          <li>• {previewData.filter(row => row.autoDetectedCategory).length} row(s) with auto-detected categories</li>
          <li>• {previewData.filter(row => row.processedData.vendor).length} row(s) with vendor information</li>
          <li>• {previewData.filter(row => row.processedData.tags && row.processedData.tags.length > 0).length} row(s) with suggested tags</li>
        </ul>
      </div>

      <div className="mt-4 max-h-[400px] overflow-y-auto">
        <div className="overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
          <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
            <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
              <tr>
                {displayHeaders.map((header) => (
                  <th key={header} scope="col" className="px-3 py-2 text-left text-xs font-semibold text-gray-900 dark:text-gray-100">
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              {previewData.map((item) => {
                const hasError = item.errors.length > 0;
                const isDuplicate = item.isDuplicateInFile || item.isDuplicateExisting;
                // Simplified row class logic
                const rowClass = !item.canImport && hasError ? 'bg-red-50 dark:bg-red-900/20' : isDuplicate ? 'bg-orange-50 dark:bg-orange-900/20' : '';

                return (
                  <tr key={item.originalRowIndex} className={rowClass}>
                    <td className="whitespace-nowrap px-3 py-2 text-xs text-gray-500 dark:text-gray-300">
                      {item.processedData.name || '(Missing)'}
                      {item.autoDetectedCategory && (
                        <span className="ml-1 text-xs text-blue-500 dark:text-blue-400" title="Auto-detected category">
                          ✓
                        </span>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-3 py-2 text-xs text-gray-500 dark:text-gray-300">
                      {typeof item.processedData.amount === 'number'
                        ? `$${item.processedData.amount.toFixed(2)}`
                        : '(Invalid)'}
                    </td>
                    <td className="whitespace-nowrap px-3 py-2 text-xs text-gray-500 dark:text-gray-300">
                      {item.processedData.dueDate && isValid(item.processedData.dueDate)
                        ? format(item.processedData.dueDate, 'yyyy-MM-dd')
                        : '(Invalid)'}
                    </td>
                    <td className="whitespace-nowrap px-3 py-2 text-xs text-gray-500 dark:text-gray-300">
                      <span className={`${item.autoDetectedCategory ? 'text-blue-500 dark:text-blue-400 font-medium' : ''}`}>
                        {item.processedData.category || '(Auto)'}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-3 py-2 text-xs text-gray-500 dark:text-gray-300">
                      {item.processedData.vendor || '(Auto-detected)'}
                    </td>
                    <td className="px-3 py-2 text-xs text-gray-500 dark:text-gray-300">
                      <div className="flex flex-wrap gap-1 max-w-[150px]">
                        {item.processedData.tags && item.processedData.tags.length > 0 ? (
                          item.processedData.tags.map((tag, idx) => (
                            <span key={idx} className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              {tag}
                            </span>
                          ))
                        ) : '(None)'}
                      </div>
                    </td>
                    <td className="px-3 py-2 text-xs">
                      {item.isDuplicateInFile || item.isDuplicateExisting ? ( // Check duplicate flags directly
                        <div>
                          <span className="text-orange-600 dark:text-orange-400 font-medium">Skip (Duplicate)</span>
                          {item.duplicateScore && (
                            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                              Match: {Math.round(item.duplicateScore)}%
                            </div>
                          )}
                          {item.isDuplicateInFile && (
                            <div className="mt-1 text-xs text-orange-500 dark:text-orange-400">
                              Duplicate in file
                            </div>
                          )}
                          {item.isDuplicateExisting && item.potentialDuplicates && item.potentialDuplicates.length > 0 && (
                            <div className="mt-1 text-xs text-orange-500 dark:text-orange-400">
                              Matches: {item.potentialDuplicates[0].bill.name}
                            </div>
                          )}
                        </div>
                      ) : !item.canImport && item.errors.length > 0 ? ( // Check canImport flag for critical errors
                          <div>
                            <span className="text-red-600 dark:text-red-400 font-medium" title={item.errors.join(', ')}>
                              Error (Skipped)
                            </span>
                            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                              {item.errors.join(', ')}
                            </div>
                          </div>
                      ) : item.warnings.length > 0 ? (
                        <div>
                          <span className="text-yellow-600 dark:text-yellow-400 font-medium" title={item.warnings.join(', ')}>
                            Warning
                          </span>
                          <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            {item.warnings.length} issue{item.warnings.length !== 1 ? 's' : ''}
                          </div>
                        </div>
                      ) : (
                        <div>
                          <span className="text-green-600 dark:text-green-400 font-medium">OK</span>
                          {item.processedData.isRecurring && (
                            <div className="mt-1 text-xs text-blue-500 dark:text-blue-400">
                              Recurring
                            </div>
                          )}
                        </div>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 flex justify-between">
        <button
          onClick={onBack}
          className="px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
        >
          Back
        </button>
        
        {/* Primary Import Button - More prominent styling */}
        <button
          onClick={() => onConfirmImport(rowsToImport)} // Pass the correctly filtered rows
          disabled={rowsToImport.length === 0} // Disable only if NO rows will be imported
          className={`px-5 py-2 text-sm font-medium text-white border border-transparent rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${
            rowsToImport.length > 0 ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-400 cursor-not-allowed dark:bg-gray-600'
          }`}
        >
          <span className="flex items-center gap-1.5">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
            </svg>
            Import {rowsToImport.length} Bills
          </span>
        </button>
      </div>
      
      {rowsToImport.length === 0 && previewData.length > 0 && (
        <p className="mt-2 text-xs text-red-600 dark:text-red-400">
          No valid, non-duplicate bills found to import. Please check errors/duplicates or go back to adjust mappings.
        </p>
      )}
    </div>
  );
}

interface ResultsStepProps {
  importResults: {
    successCount: number;
    errorCount: number;
    duplicateSkippedCount: number;
    errors: { rowIndex: number; errors: string[] }[];
  };
  onClose: () => void;
}

function ResultsStep({ importResults, onClose }: ResultsStepProps) {
  return (
    <div className="p-6">
      <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">Import Results</h3>
      <div className="mt-4 space-y-3">
        <p className="text-sm text-green-600 dark:text-green-400">
          Successfully imported: {importResults.successCount} bills
        </p>
        <p className="text-sm text-red-600 dark:text-red-400">
          Rows with errors: {importResults.errorCount}
        </p>
        <p className="text-sm text-yellow-600 dark:text-yellow-400">
          Duplicate rows skipped: {importResults.duplicateSkippedCount}
        </p>

        {importResults.errors && importResults.errors.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-base font-medium text-gray-800 dark:text-gray-200">Error Details:</h4>
            <ul className="mt-2 space-y-1 max-h-40 overflow-y-auto text-sm text-red-700 dark:text-red-300">
              {importResults.errors.map(({ rowIndex, errors }, index) => (
                <li key={index}>
                  <span className="font-semibold">Row {rowIndex}:</span> {errors.join(', ')}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      <div className="mt-6 flex justify-end">
        <button
          type="button"
          onClick={onClose}
          className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-indigo-500 dark:hover:bg-indigo-600 dark:focus:ring-offset-gray-800"
        >
          Close
        </button>
      </div>
    </div>
  );
}

interface BillImportWizardProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function BillImportWizard({ isOpen, onClose }: BillImportWizardProps) {
  const [currentStep, setCurrentStep] = useState<ImportStep>('upload');
  const [headers, setHeaders] = useState<string[]>([]);
  const [parsedData, setParsedData] = useState<ParsedRow[]>([]);
  const [columnMapping, setColumnMapping] = useState<ColumnMapping>({});
  const [previewData, setPreviewData] = useState<ProcessedBillPreview[]>([]);
  const [isProcessingData, setIsProcessingData] = useState(false);
  const [importResults, setImportResults] = useState<ResultsStepProps['importResults'] | null>(null);
  const [error, setError] = useState<string>('');

  // Update the destructuring to match actual store structure (bills instead of state.bills)
  const { bills, addBill } = useBillsStore();
  
  // Use useMemo to prevent recreation of existingBills on every render
  const existingBills = useMemo(() => bills || [], [bills]);

  useEffect(() => {
    if (currentStep !== 'preview' || !parsedData.length || !Object.keys(columnMapping).length) {
      return;
    }

    console.log('Processing data for preview...');
    setIsProcessingData(true);
    const processed: ProcessedBillPreview[] = [];
    const fileDuplicateCheck = new Set<string>();

    parsedData.forEach((row) => {
      const currentPreview: ProcessedBillPreview = {
        originalRowIndex: row.rowIndex,
        rawData: row.data,
        processedData: { dueDate: null }, // Initialize dueDate as null
        errors: [],
        warnings: [],
        isDuplicateInFile: false,
        isDuplicateExisting: false,
        isIgnored: false, // Initialize as false
        canImport: true, // Assume importable by default
      };

      // --- Field Processing & Validation ---
      const rawName = columnMapping['name'] && columnMapping['name'] !== 'ignore' ? row.data[columnMapping['name']] : undefined;
      const rawAmount = columnMapping['amount'] && columnMapping['amount'] !== 'ignore' ? row.data[columnMapping['amount']] : undefined;
      const rawDate = columnMapping['dueDate'] && columnMapping['dueDate'] !== 'ignore' ? row.data[columnMapping['dueDate']] : undefined;
      const rawCategory = columnMapping['category'] && columnMapping['category'] !== 'ignore' ? row.data[columnMapping['category']] : undefined;
      const rawNotes = columnMapping['notes'] && columnMapping['notes'] !== 'ignore' ? row.data[columnMapping['notes']] : undefined;

      // Name
      const cleanedName = trimImportText(rawName);
      if (!cleanedName && columnMapping['name']) {
        if (row.data && Object.keys(row.data).length > 0) {
          // Try to use any non-empty field as name if available
          const anyValue = Object.values(row.data).find(val => val && String(val).trim() !== '');
          if (anyValue) {
            currentPreview.processedData.name = String(anyValue).trim();
            currentPreview.warnings.push('Used another field value as bill name');
          } else {
            currentPreview.processedData.name = `Bill #${row.rowIndex}`;
            currentPreview.warnings.push('Generated generic bill name');
          }
        } else {
          currentPreview.processedData.name = `Bill #${row.rowIndex}`;
          currentPreview.warnings.push('Generated generic bill name');
        }
      } else {
        currentPreview.processedData.name = cleanedName;
      }

      // Amount
      const cleanedAmount = cleanImportAmount(rawAmount);
      if (cleanedAmount === null && columnMapping['amount']) {
        // Try to extract a number from the string if possible
        const numberMatch = rawAmount ? String(rawAmount).match(/[\d,.]+/) : null;
        const extractedAmount = numberMatch ? cleanImportAmount(numberMatch[0]) : null;

        if (extractedAmount !== null) {
          currentPreview.processedData.amount = extractedAmount;
          currentPreview.warnings.push('Extracted amount from text');
        } else {
          // If we still can't find an amount, use a default value
          currentPreview.processedData.amount = 0;
          currentPreview.warnings.push('Using $0.00 as default amount');
        }
      } else {
        currentPreview.processedData.amount = cleanedAmount ?? 0;
      }

      // Due Date
      let dueDate = parseImportDate(rawDate);

      // Add safer logging for debugging
      console.log(`Raw date value: ${rawDate !== undefined && rawDate !== null ? String(rawDate) : 'undefined/null'} (type: ${typeof rawDate})`);
      console.log(`Parsed date: ${dueDate ? dueDate.toISOString() : 'null'}`);

      // If we couldn't parse the date but have a value, try some additional formats
      if (!dueDate && rawDate !== undefined && rawDate !== null) {
        // Try to extract a date-like pattern
        const dateStr = String(rawDate);
        const dateMatch = dateStr.match(/(\d{1,4}[\/-]\d{1,2}[\/-]\d{1,4})|(\w+ \d{1,2},? \d{4})/);
        if (dateMatch) {
          console.log(`Extracted date pattern: ${dateMatch[0]}`);
          dueDate = parseImportDate(dateMatch[0]);
          if (dueDate) {
            currentPreview.warnings.push('Extracted date from text');
          }
        }

        // If still no date, try to handle Excel serial date numbers directly
        if (!dueDate && !isNaN(Number(rawDate))) {
          const numericDate = Number(rawDate);
          // Excel dates are stored as days since 1900-01-01 (or 1904-01-01 on Mac)
          if (numericDate > 1000) { // Reasonable minimum for an Excel date (around 1903)
            console.log(`Trying to parse as Excel serial date: ${numericDate}`);
            try {
              // Try both 1900 and 1904 date systems
              const date1900 = new Date(1900, 0, 1);
              date1900.setDate(date1900.getDate() + numericDate - 2); // -2 to account for Excel's leap year bug

              const date1904 = new Date(1904, 0, 1);
              date1904.setDate(date1904.getDate() + numericDate);

              // Use the more recent date (likely the correct one)
              dueDate = date1900 > date1904 ? date1900 : date1904;

              if (isValid(dueDate)) {
                currentPreview.warnings.push('Converted Excel date format');
              } else {
                dueDate = null;
              }
            } catch (e) {
              console.error("Error converting Excel date:", e);
              dueDate = null;
            }
          }
        }
      }

      // If still no date, use today's date as fallback
      if (!dueDate) {
        const today = new Date();
        // Set due date to next month to give user time to pay
        dueDate = addMonths(today, 1);
        currentPreview.warnings.push('Using default due date (one month from today)');
      }

      currentPreview.processedData.dueDate = dueDate; // Store Date | null

      // Notes
      currentPreview.processedData.notes = trimImportText(rawNotes) ?? '';

      // Category
      const category = trimImportText(rawCategory);
      // Assign category, fallback to suggestion, then 'Other' if no suggestion
      currentPreview.processedData.category = category ?? 
        suggestCategory(currentPreview.processedData.name || '', []) ?? 
        'Other';

      // Set isRecurring to false by default
      currentPreview.processedData.isRecurring = false;

      // Set reminderDays to 3 by default
      currentPreview.processedData.reminderDays = 3;

      // Check if this row has all required data to be imported
      currentPreview.canImport = !!currentPreview.processedData.name && 
                               currentPreview.processedData.amount !== undefined && 
                               !!currentPreview.processedData.dueDate && 
                               isValid(currentPreview.processedData.dueDate);

      // --- Duplicate Checks ---
      if (currentPreview.canImport) {
        const dueDateStr = format(currentPreview.processedData.dueDate, 'yyyy-MM-dd');
        const uniqueKey = `${currentPreview.processedData.name.toLowerCase()}|${currentPreview.processedData.amount}|${dueDateStr}`;

        // Check for duplicates within the file
        if (fileDuplicateCheck.has(uniqueKey)) {
          currentPreview.isDuplicateInFile = true;
          currentPreview.warnings.push('Duplicate row found within the import file. Will be skipped.');
        } else {
          fileDuplicateCheck.add(uniqueKey);
        }

        // Check for duplicates against existing bills (only if not already a file duplicate)
        if (!currentPreview.isDuplicateInFile) {
          const existingDuplicate = existingBills.find((existing) => {
            const existingNameLower = existing.name?.toLowerCase();
            const existingAmount = existing.amount;
            const existingDueDateStr = existing.dueDate; // This is 'yyyy-MM-dd' string

            // Ensure processed data fields are valid before comparison
            if (currentPreview.processedData.name?.toLowerCase() === existingNameLower && 
                currentPreview.processedData.amount === existingAmount && 
                existingDueDateStr && 
                currentPreview.processedData.dueDate // Check if processed date is not null
            ) {
              try {
                const existingDate = parse(existingDueDateStr, 'yyyy-MM-dd', new Date());
                // Add validity checks before calling differenceInDays
                if (isValid(existingDate) && isValid(currentPreview.processedData.dueDate)) {
                  return Math.abs(differenceInDays(existingDate, currentPreview.processedData.dueDate)) <= 1;
                }
              } catch (e) {
                console.error("Error parsing date for duplicate check:", e);
                return false;
              }
            }
            return false;
          });

          currentPreview.isDuplicateExisting = !!existingDuplicate;
          if (currentPreview.isDuplicateExisting) {
            currentPreview.warnings.push('Potential duplicate of an existing bill. Will be skipped.');
          }
        }
      } else {
        // If essential data is missing, mark it as having errors that prevent import
        if (!currentPreview.processedData.name) currentPreview.errors.push('Missing Bill Name');
        if (currentPreview.processedData.amount === undefined) currentPreview.errors.push('Missing or invalid Amount');
        if (!currentPreview.processedData.dueDate || !isValid(currentPreview.processedData.dueDate)) currentPreview.errors.push('Missing or invalid Due Date');
      }


      processed.push(currentPreview);
    });

    console.log('Processed Preview Data:', processed);
    setPreviewData(processed);
    setIsProcessingData(false);
  }, [currentStep, parsedData, columnMapping, existingBills]); // Ensure existingBills is in dependency array

  const handleMappingConfirmed = (finalMapping: ColumnMapping) => {
    // Validate that we have the required fields mapped
    const hasMissingRequiredFields = TARGET_FIELDS.some(field => 
      field.required && !finalMapping[field.key as string]
    );

    if (hasMissingRequiredFields) {
      setError('Please map all required fields (Bill Name and Amount at minimum) before proceeding.');
      return;
    }

    setColumnMapping(finalMapping);
    setError(''); // Clear any previous errors
    setIsProcessingData(true);

    // Process the data with the confirmed mapping
    setTimeout(() => {
      const processedData = parsedData.map((row) => {
        const errors: string[] = [];
        const warnings: string[] = [];
        const processedRow: ProcessedBillPreview['processedData'] = {
          // Set default values
          isRecurring: false,
          reminderDays: 3,
          tags: [],
          importSource: 'spreadsheet-import'
        };

        // Process each field according to the mapping and validation rules
        Object.entries(finalMapping).forEach(([targetKey, sourceHeader]) => {
          if (!sourceHeader || targetKey === 'ignore') return;

          const rawValue = row.data[sourceHeader];
          const field = TARGET_FIELDS.find(f => f.key === targetKey);

          // Apply field-specific processing
          switch (targetKey) {
            case 'name':
              processedRow.name = trimImportText(rawValue);
              break;

            case 'amount':
              // Convert null to undefined to match the expected type
              const cleanedAmount = cleanImportAmount(rawValue);
              processedRow.amount = cleanedAmount !== null ? cleanedAmount : undefined;
              break;

            case 'dueDate':
              processedRow.dueDate = parseImportDate(rawValue);
              // Add warning for past due dates
              if (processedRow.dueDate && differenceInDays(processedRow.dueDate, new Date()) < -30) {
                warnings.push('Due date is more than 30 days in the past');
              }
              break;

            case 'category':
              processedRow.category = trimImportText(rawValue);
              break;

            case 'notes':
              processedRow.notes = trimImportText(rawValue);
              break;

            case 'vendor':
              processedRow.vendor = trimImportText(rawValue);
              break;

            case 'isRecurring':
              // Try to interpret various values as boolean
              const recurringValue = String(rawValue || '').toLowerCase().trim();
              processedRow.isRecurring = ['yes', 'true', '1', 'y', 'recurring', 'monthly', 'weekly', 'subscription'].includes(recurringValue);
              break;

            case 'reminderDays':
              const reminderValue = parseInt(String(rawValue || '').trim(), 10);
              processedRow.reminderDays = !isNaN(reminderValue) ? reminderValue : 3; // Default to 3 days
              break;
          }

          // Apply field validator if available
          if (field?.validator) {
            const validationError = field.validator(rawValue);
            if (validationError) {
              errors.push(validationError);
            }
          }
        });

        // Auto-detect missing information

        // 1. If vendor is not provided but name is, try to detect vendor
        if (!processedRow.vendor && processedRow.name) {
          processedRow.vendor = detectVendor(processedRow.name);
        }

        // 2. If category is not provided, try to suggest one based on the name
        let autoDetectedCategory = false;
        if (!processedRow.category && processedRow.name) {
          // Convert null to undefined to match the expected type
          const suggestedCategory = suggestCategory(processedRow.name || '');
          processedRow.category = suggestedCategory || undefined;
          autoDetectedCategory = true;
          if (processedRow.category) {
            warnings.push(`Category auto-detected as '${processedRow.category}' based on bill name`);
          }
        }

        // 3. Generate suggested tags based on name, category, and vendor
        if (processedRow.name) {
          const suggestedTags = suggestTags(
            processedRow.name, 
            processedRow.category || '', 
            processedRow.vendor
          );

          if (suggestedTags.length > 0) {
            processedRow.tags = suggestedTags;
          }
        }

        // Advanced duplicate detection within import file
        const isDuplicateInFile = parsedData.some((otherRow) => {
          if (otherRow.rowIndex === row.rowIndex) return false; // Skip self-comparison

          const otherName = trimImportText(otherRow.data[finalMapping.name as string]);
          const otherAmount = cleanImportAmount(otherRow.data[finalMapping.amount as string]);
          const otherDueDate = parseImportDate(otherRow.data[finalMapping.dueDate as string]);

          // More sophisticated matching
          const nameMatch = processedRow.name && otherName && 
                           (processedRow.name.toLowerCase() === otherName.toLowerCase() || 
                            processedRow.name.toLowerCase().includes(otherName.toLowerCase()) || 
                            otherName.toLowerCase().includes(processedRow.name.toLowerCase()));

          const amountMatch = typeof processedRow.amount === 'number' && otherAmount !== null && 
                             Math.abs(processedRow.amount - otherAmount) < 0.01; // Allow for rounding differences

          const dueDateMatch = processedRow.dueDate && otherDueDate && 
                             Math.abs(differenceInDays(processedRow.dueDate, otherDueDate)) <= 3; // Within 3 days

          return nameMatch && (amountMatch || dueDateMatch); // Either amount or date match with name
        });

        // Advanced duplicate detection in existing bills
        let potentialDuplicates: { bill: Bill; score: number }[] = [];
        let duplicateScore = 0;
        let isDuplicateExisting = false;

        if (bills?.length && processedRow.name && processedRow.amount && processedRow.dueDate) {
          // Create a partial bill object for comparison
          const partialBill: Partial<Bill> = {
            name: processedRow.name,
            amount: processedRow.amount,
            dueDate: processedRow.dueDate.toISOString().split('T')[0],
            category: processedRow.category,
          };

          // Get potential duplicates with similarity scores
          potentialDuplicates = detectDuplicates(partialBill, bills);

          // If we have high-confidence duplicates
          if (potentialDuplicates.length > 0) {
            duplicateScore = potentialDuplicates[0].score;
            isDuplicateExisting = duplicateScore > 70; // High confidence threshold

            if (isDuplicateExisting) {
              warnings.push(`This appears to be a duplicate of an existing bill: ${potentialDuplicates[0].bill.name}`);
            } else if (duplicateScore > 40) {
              warnings.push(`This might be similar to existing bill: ${potentialDuplicates[0].bill.name}`);
            }
          }
        }

        if (isDuplicateInFile) {
          warnings.push('This appears to be a duplicate of another bill in your import file');
        }

        return {
          originalRowIndex: row.rowIndex,
          rawData: row.data,
          processedData: processedRow,
          errors,
          warnings,
          isDuplicateInFile,
          isDuplicateExisting,
          duplicateScore,
          potentialDuplicates,
          isIgnored: false,
          canImport: errors.length === 0,
          autoDetectedCategory
        };
      });

      setPreviewData(processedData);
      setIsProcessingData(false);
      setCurrentStep('preview');
    }, 500); // Small delay to allow UI to update
  };

  const handleConfirmImport = (dataToImport: ProcessedBillPreview[]) => {
    // dataToImport is already filtered by PreviewStep to include only valid, non-duplicate rows
    if (dataToImport.length === 0) {
      // This case should ideally be prevented by the disabled button state, but handle defensively
      setError('No valid, non-duplicate bills selected for import.');
      setCurrentStep('preview'); // Stay on preview step
      return;
    }

    let successCount = 0;
    let importErrors = 0; // Errors during the actual import process
    const detailedErrors: { rowIndex: number; errors: string[] }[] = [];

    dataToImport.forEach((item) => { // Iterate directly over the passed dataToImport
      try {
        const billToAdd: Partial<Bill> = {
          name: item.processedData.name,
          amount: item.processedData.amount,
          // Format Date to string 'yyyy-MM-dd' for storage
          dueDate: item.processedData.dueDate ? format(item.processedData.dueDate, 'yyyy-MM-dd') : undefined,
          category: item.processedData.category || 'Other',
          isRecurring: item.processedData.isRecurring ?? false,
          reminderDays: item.processedData.reminderDays ?? 3,
          // Add vendor field which is in the Bill interface
          vendor: item.processedData.vendor,
          // Store additional metadata in notes
          notes: [
            item.processedData.notes || '',
            item.processedData.description ? `Description: ${item.processedData.description}` : '',
            item.processedData.tags?.length ? `Tags: ${item.processedData.tags.join(', ')}` : '',
            item.autoDetectedCategory ? 'Category was auto-detected' : '',
            'Imported from spreadsheet' + ` on ${new Date().toLocaleDateString()}`
          ].filter(Boolean).join('\n')
        };

        if (!billToAdd.name || billToAdd.amount === undefined || !billToAdd.dueDate) {
          // This check should ideally not fail if canImport was true, but good failsafe
          throw new Error('Missing essential data just before import.');
        }

        // Create a new object with the guaranteed correct types after validation
        const validatedBillData: Omit<BillFormData, 'id'> = {
          name: billToAdd.name,
          amount: billToAdd.amount,
          dueDate: billToAdd.dueDate,
          category: billToAdd.category as string,
          billType: 'regular',
          notes: billToAdd.notes as string,
          isRecurring: billToAdd.isRecurring as boolean,
          reminderDays: billToAdd.reminderDays as number,
          // Add vendor field which is in the BillFormData interface
          vendor: billToAdd.vendor
        };

        // Add the bill to the store
        addBill(validatedBillData);
        successCount++;
      } catch (err: any) {
        console.error(`Error importing row ${item.originalRowIndex}:`, err);
        importErrors++; // Count errors happening during this import loop
        detailedErrors.push({ rowIndex: item.originalRowIndex, errors: [err.message || 'Unknown import error'] });
      }
    });

    // Calculate final results based on the initial preview data and import outcomes
    const duplicateSkippedCount = previewData.filter((row) => row.isDuplicateInFile || row.isDuplicateExisting).length;
    const errorSkippedCount = previewData.filter((row) => !row.canImport && !row.isDuplicateInFile && !row.isDuplicateExisting).length;

    setImportResults({
      successCount,
      errorCount: errorSkippedCount + importErrors, // Combine pre-import errors and import-time errors
      duplicateSkippedCount,
      errors: detailedErrors, // Only show errors from the actual import attempt
    });
    setCurrentStep('results');
  };

  const handleClose = () => {
    setCurrentStep('upload');
    setHeaders([]);
    setParsedData([]);
    setColumnMapping({});
    setPreviewData([]);
    setImportResults(null);
    setError('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30 dark:bg-black/50" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4 overflow-y-auto">
        {/* Apply overflow-y-auto here and remove overflow-hidden */}
        <Dialog.Panel className="w-full max-w-2xl rounded-lg bg-white dark:bg-gray-800 shadow-xl max-h-[90vh] flex flex-col overflow-y-auto">
          <Dialog.Title className="sr-only">Import Bills</Dialog.Title>

          {error && (
            <div className="p-4 bg-red-100 dark:bg-red-900 border-l-4 border-red-500 dark:border-red-400">
              <p className="text-sm font-medium text-red-800 dark:text-red-200">Error</p>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">{error}</p>
            </div>
          )}

          {currentStep === 'upload' && (
            <UploadStep
              onFileParsed={(headers, data) => {
                setHeaders(headers);
                setParsedData(data);

                // Auto-generate initial column mapping based on header names
                const initialMapping: ColumnMapping = {};

                // Try to match headers to target fields
                TARGET_FIELDS.forEach(field => {
                  // First try exact matches
                  let matchIndex = headers.findIndex(header => 
                    field.commonHeaders.some(common => 
                      header.toLowerCase() === common.toLowerCase()
                    )
                  );

                  // If no exact match, try partial matches
                  if (matchIndex === -1) {
                    matchIndex = headers.findIndex(header => 
                      field.commonHeaders.some(common => 
                        header.toLowerCase().includes(common.toLowerCase())
                      )
                    );
                  }

                  if (matchIndex !== -1) {
                    initialMapping[field.key as string] = headers[matchIndex];
                  } else {
                    initialMapping[field.key as string] = null;
                  }
                });

                setColumnMapping(initialMapping);
                // Automatically advance to the column mapping step
                setCurrentStep('mapColumns');
              }}
              onError={(msg) => setError(msg)} // Wrap setError
            />
          )}

          {currentStep === 'mapColumns' && (
            <MapColumnsStep
              headers={headers}
              sampleData={parsedData}
              initialMapping={columnMapping}
              onMappingConfirmed={handleMappingConfirmed}
              onBack={() => setCurrentStep('upload')}
            />
          )}

          {currentStep === 'preview' && (
            isProcessingData ? (
              <div className="p-6 text-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-gray-500 dark:text-gray-400">Processing your data...</p>
              </div>
            ) : (
              <PreviewStep
                previewData={previewData}
                columnMapping={columnMapping}
                onConfirmImport={handleConfirmImport}
                onBack={() => setCurrentStep('mapColumns')}
              />
            )
          )}

          {currentStep === 'results' && importResults && (
            <ResultsStep importResults={importResults} onClose={handleClose} />
          )}
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
