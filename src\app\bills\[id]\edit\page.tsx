'use client';

import BillForm from '@/components/bills/BillForm'; // Assuming BillForm is default export
import { useBillsStore } from '@/stores/billsStore';
import { Bill, BillFormData } from '@/types/bill';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function EditBillPage() {
  const router = useRouter();
  const params = useParams();
  const { bills, updateBill, isLoading: billsLoading } = useBillsStore();
  const [billToEdit, setBillToEdit] = useState<Bill | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Wait for bills to finish loading before searching
    if (billsLoading) return;

    if (params.id && bills && bills.length > 0) {
      const billId = params.id.toString();
      const foundBill = bills.find(b => b.id?.toString() === billId);
      if (foundBill) {
        setBillToEdit(foundBill);
        setError(null);
      } else {
        setError('Bill not found.');
      }
      setIsLoading(false);
    } else if (!billsLoading) {
      setIsLoading(false);
      if (!params.id) setError('Bill ID is missing.');
      else if (bills && bills.length === 0) setError('No bills available.');
      else setError('Bill not found.');
    }
  }, [params.id, bills, billsLoading]);

  const handleSave = async (updatedBillData: BillFormData) => {
    if (!billToEdit) return;

    // Fix: Convert null category to undefined to match Bill type
    const safeBillData = {
      ...updatedBillData,
      category: updatedBillData.category === null ? undefined : updatedBillData.category,
    };

    try {
      await updateBill(billToEdit.id, safeBillData);
      router.push('/dashboard'); // Navigate back to dashboard after save completes
    } catch (e) {
      console.error('Failed to save bill updates', e);
    }
  };

  const handleCancel = () => {
    if (billToEdit) {
      router.push('/dashboard'); // Navigate back to dashboard
    } else {
      router.push('/dashboard'); // Fallback to dashboard if bill wasn't loaded
    }
  };

  if (isLoading || billsLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-6 flex justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Error</h2>
          <p>{error}</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="mt-4 px-4 py-2 bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700 rounded-lg transition-colors"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  if (!billToEdit) {
    // This case should ideally be covered by the error state, but added as a safeguard
    return (
      <div className="max-w-4xl mx-auto px-4 py-6 text-center">
        <p>Could not load bill data for editing.</p>
        <button
          onClick={() => router.push('/dashboard')}
          className="mt-4 px-4 py-2 bg-primary text-white rounded-lg"
        >
          Go to Dashboard
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-6">
      <h1 className="text-2xl font-semibold mb-6 text-gray-900 dark:text-white">Edit Bill</h1>
      <BillForm
        bill={billToEdit}
        onSave={handleSave}
        onCancel={handleCancel}
        allBills={bills || []} // Pass all bills for potential duplicate checks etc.
      />
    </div>
  );
}
