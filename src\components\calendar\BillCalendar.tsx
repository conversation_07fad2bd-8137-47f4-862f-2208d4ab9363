'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { useAuth } from '@/hooks/useAuth'; // Import useAuth
import useGoogleCalendarSync from '@/hooks/useGoogleCalendarSync';
import { useBillsStore } from '@/stores/billsStore';
import { useFinancialStore, useFinancialSummary } from '@/stores/financialStore';
import { Bill } from '@/types/bill';
import { assessBillRisk, BillUrgencyInfo, getBillUrgency } from '@/utils/billIntelligence';
import { calculateNextDueDate } from '@/utils/billRenewal';
import { formatDate, parseLocalDateString } from '@/utils/date';
import { addMonths, format, getDay, isPast, isToday, parse, startOfWeek } from 'date-fns';
import { enUS } from 'date-fns/locale';
import { useRouter } from 'next/navigation';
import { Calendar, dateFnsLocalizer, EventProps, View, Views } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import './calendar-styles.css';

import useMediaQuery from '@/hooks/useMediaQuery';
import dynamic from 'next/dynamic';
import BillAgendaEvent from './BillAgendaEvent';
import { useBillCalendarContext } from './BillCalendarContext';
import { BillPaymentModal } from './BillPaymentModal';
import GoogleCalendarSync from './GoogleCalendarSync';

// Set up the localizer
const locales = {
  'en-US': enUS,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

// Define formats for React Big Calendar date formatting
const formats = {
  dateFormat: 'd', // Only show day number in month cells
  dayFormat: 'eee', // Short weekday names
  monthFormat: 'MMMM yyyy',
  dayHeaderFormat: 'eee', // Short weekday names in header
};

// Define the calendar event interface
interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  allDay: boolean;
  resource: Bill;
  status: 'paid' | 'upcoming' | 'overdue' | 'future';
  riskScore: number;
  urgency: BillUrgencyInfo;
  mobileIndicator?: boolean;
}

// Export the PaidBillsArchive component with dynamic loading
export const PaidBillsArchive = dynamic(() => import('@/components/bills/PaidBillsArchive'), { ssr: false });

// Custom Tooltip Component for Events
const EventTooltip = ({ event, children }: { event: CalendarEvent; children?: React.ReactNode }) => {
  const bill = event.resource;
  const isPaid = !!bill.paidDate;
  const amount = bill.amount;
  const dueDate = new Date(bill.dueDate);
  const isOverdue = !isPaid && isPast(dueDate) && !isToday(dueDate);
  const status = isPaid ? 'Paid' : isOverdue ? 'Overdue' : 'Upcoming';

  // Paper Maps & Blueprints theme colors
  const statusColor = isPaid ? 'text-green-600 dark:text-green-400' :
    isOverdue ? 'text-muted-red dark:text-muted-red' :
      'text-blueprint-blue dark:text-blueprint-blue';
  const statusBg = isPaid ? 'bg-light-bg dark:bg-dark-sepia/20' :
    isOverdue ? 'bg-light-bg dark:bg-dark-sepia/20' :
      'bg-light-bg dark:bg-dark-sepia/20';

  return (
    <div className="bg-light-bg dark:bg-dark-sepia/90 rounded-lg shadow-sm p-2 sm:p-5 overflow-hidden h-[700px] border border-dark-sepia/20 dark:border-white/20">
      <h3 className="font-serif font-semibold text-lg mb-3 text-dark-sepia dark:text-white">{event.title}</h3>
      <div className="space-y-3 text-sm text-dark-sepia dark:text-white/90">
        <div className="flex justify-between">
          <span className="text-dark-sepia/70 dark:text-white/70">Amount:</span>
          <span className="font-medium text-dark-sepia dark:text-white font-mono">${amount.toFixed(2)}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-dark-sepia/70 dark:text-white/70">Due Date:</span>
          <span className="font-medium text-dark-sepia dark:text-white">{formatDate(event.resource.dueDate, 'medium')}</span>
        </div>
        {isPaid && (
          <div className="flex justify-between">
            <span className="text-dark-sepia/70 dark:text-white/70">Payment Date:</span>
            <span className="font-medium text-green-600 dark:text-green-400">
              {formatDate(event.resource.paidDate || '', 'medium')}
            </span>
          </div>
        )}
        <div className="flex justify-between">
          <span className="text-dark-sepia/70 dark:text-white/70">Status:</span>
          <span className={`font-medium ${statusColor}`}>{status}</span>
        </div>

        {/* Risk Level */}
        <div className="flex justify-between">
          <span className="text-dark-sepia/70 dark:text-white/70">Risk Level:</span>
          <span className="font-medium">
            {event.urgency.level === 'high' && (
              <span className="text-muted-red dark:text-muted-red">High</span>
            )}
            {event.urgency.level === 'medium' && (
              <span className="text-orange-500 dark:text-orange-400">Medium</span>
            )}
            {event.urgency.level === 'low' && (
              <span className="text-green-600 dark:text-green-400">Low</span>
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

// Enhanced Event component with better visual hierarchy and AI-driven insights
interface CustomEventProps extends EventProps<CalendarEvent> {
  isMultipleEventsDay?: boolean;
  onOpenPaymentModal: (bill: Bill) => void;
  currentView: View;
}

const CustomEvent = ({ event, isMultipleEventsDay, onOpenPaymentModal, currentView }: CustomEventProps) => {
  const isPaid = !!event.resource.paidDate;
  const isOverdue = event.status === 'overdue';

  // Handle click on bill name to open side panel
  const handleBillNameClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubble to calendar which would select the day
    // openSidePanelForBill(event.resource);
  };

  const isMonthView = currentView === 'month';

  return (
    <div className={`h-full w-full px-1 ${isMonthView ? 'py-0' : 'py-0.5'} flex items-center ${isMonthView ? 'justify-start' : 'justify-between'} gap-1 overflow-hidden text-xs
      ${isPaid ? 'text-green-600 dark:text-green-400' :
        isOverdue ? 'text-muted-red dark:text-muted-red' :
          'text-blueprint-blue dark:text-blueprint-blue'}`}>
      <span
        className={`font-medium ${isMonthView ? 'truncate w-full' : 'truncate cursor-pointer hover:underline'}`}
        onClick={isMonthView ? undefined : handleBillNameClick} // In month view, click is handled by event wrapper
        title={event.title}
      >
        {event.title}
      </span>
      {!isMonthView && (
        <>
          <span className="font-mono whitespace-nowrap">${event.resource.amount.toFixed(0)}</span>
          {!isPaid && (
            <button
              onClick={(e) => {
                e.stopPropagation(); // Prevent calendar event click if needed
                onOpenPaymentModal(event.resource);
              }}
              className="px-2 py-0.5 text-xs font-medium bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              Mark Paid
            </button>
          )}
        </>
      )}
    </div>
  );
};

// Helper component to wrap the CustomEvent with multi-event detection
const EventWrapper = (props: any) => {
  const allEventsForDay = props.components?.eventContainerWrapper?.props?._pevents || [];
  const isMultipleEventsDay = allEventsForDay.length > 1;

  // Get the background color for the event
  const style = {
    ...props.style,
    borderRadius: '2px',
    backgroundColor: 'transparent',
    borderLeft: '3px solid',
    borderColor: props.event.status === 'paid' ? '#16a34a' :
      props.event.status === 'overdue' ? '#ef4444' :
        '#0066cc',
  };

  return (
    <div style={style}>
      <CustomEvent {...props} isMultipleEventsDay={isMultipleEventsDay} onOpenPaymentModal={props.handleOpenPaymentModal} currentView={props.currentView} />
    </div>
  );
};

// Custom day cell wrapper compatible with DateHeaderProps interface
const CustomDayCell = (props: any) => {
  const { date, label, isOffRange, drilldownView, onDrillDown } = props;
  const today = new Date();
  const isToday = date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear();

  const cellClasses = `rbc-day-bg ${isToday ? 'rbc-today' : ''} ${isOffRange ? 'rbc-off-range' : ''} hover:bg-light-gray/30 dark:hover:bg-dark-sepia/20 transition-colors`;

  return (
    <div
      className={`rbc-date-cell ${isToday ? 'rbc-now rbc-current' : ''} ${isOffRange ? 'rbc-off-range' : ''}`}
      style={{ textAlign: 'center' }}
    >
      <button
        type="button"
        className={`rbc-button-link ${isToday ? 'font-semibold bg-blueprint-blue/20 dark:bg-blueprint-blue/30 text-blueprint-blue dark:text-white' : 'text-dark-sepia dark:text-white/90'} w-6 h-6 rounded-full hover:bg-light-gray/40 dark:hover:bg-dark-sepia/40 flex items-center justify-center text-sm mx-auto`}
        onClick={onDrillDown}
      >
        {label}
      </button>
    </div>
  );
};

// Custom toolbar component
const CustomToolbar = ({ label, onNavigate }: any) => {
  const goToBack = () => {
    onNavigate('PREV');
  };

  const goToNext = () => {
    onNavigate('NEXT');
  };

  const goToCurrent = () => {
    onNavigate('TODAY');
  };

  return (
    <div className="hidden sm:block">
      <div className="flex justify-between items-center p-3 bg-blueprint-blue/10 dark:bg-blueprint-blue/20 border-b border-dark-sepia/10 dark:border-white/10">
        <div className="flex gap-2">
          <button
            onClick={goToCurrent}
            className="px-3 py-1.5 text-xs font-medium bg-white dark:bg-dark-sepia/50 border border-dark-sepia/20 dark:border-white/20 rounded text-dark-sepia dark:text-white hover:bg-light-gray transition-colors"
          >
            Today
          </button>
          <div className="flex rounded overflow-hidden border border-dark-sepia/20 dark:border-white/20">
            <button
              onClick={goToBack}
              className="px-2 py-1.5 text-xs bg-white dark:bg-dark-sepia/50 text-dark-sepia dark:text-white hover:bg-light-gray transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </button>
            <button
              onClick={goToNext}
              className="px-2 py-1.5 text-xs bg-white dark:bg-dark-sepia/50 text-dark-sepia dark:text-white hover:bg-light-gray transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
        <h2 className="font-serif text-lg font-medium text-dark-sepia dark:text-white">{label}</h2>
        <div className="w-20"></div> {/* Spacer to balance the layout */}
      </div>
    </div>
  );
};

// Interface for the bill event structure
interface EventWithBillResource {
  id: string;
  title: string;
  start: Date;
  end: Date;
  allDay: boolean;
  resource: Bill & { id: string; dueDate: string };
  status: 'paid' | 'upcoming' | 'overdue' | 'future';
  riskScore: number;
  urgency: BillUrgencyInfo;
  mobileIndicator?: boolean;
}

// Type predicate for event structure validation
function isBillEvent(event: any): event is EventWithBillResource {
  return (
    event &&
    typeof event.id === 'string' &&
    event.resource &&
    typeof event.resource.id === 'string' &&
    typeof event.resource.dueDate === 'string'
  );
}

export function BillCalendar() {
  const router = useRouter();
  const { bills, isLoading, markBillPaid, markBillUnpaid, updateBill } = useBillsStore();
  console.log('[BillCalendar] isLoading:', isLoading, 'bills:', bills);
  const { transactions } = useFinancialStore();
  const { summary } = useFinancialSummary();
  const { user: authUser, loading: authLoading } = useAuth(); // Get user and auth loading state
  const user = authUser === undefined ? null : authUser; // Ensure user is User | null

  // Google Calendar sync integration
  const { syncSettings, syncBillsToCalendar } = useGoogleCalendarSync(user); // Pass user to the hook

  // State for calendar management
  const [view, setView] = useState<View>(Views.MONTH);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showPaid, setShowPaid] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedDateForAdd, setSelectedDateForAdd] = useState(new Date());
  const [isPaidArchiveOpen, setIsPaidArchiveOpen] = useState(false);

  // Pull-to-refresh functionality (mobile)
  const [isPulling, setIsPulling] = useState(false);
  const [pullStartY, setPullStartY] = useState(0);
  const [pullMoveY, setPullMoveY] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleAddBillClick = () => {
    // Use the globally selectedDate for the calendar,
    // or default to a new Date if somehow not set.
    setSelectedDateForAdd(selectedDate || new Date());
    setIsAddModalOpen(true);
  };

  // Destructure from bill calendar context
  const {
    // isSidePanelOpen,
    // editingBill,
    // openSidePanelForBill
  } = useBillCalendarContext();

  // Detect screen size for mobile view adjustments
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isFoldable = useMediaQuery('(min-width: 641px) and (max-width: 840px)');

  const calendarView = useMemo<View>(() => {
    if (isMobile && !isFoldable) {
      return 'agenda' as View;
    }
    return view;
  }, [isMobile, isFoldable, view]);

  // Use effect to load bills on component mount
  useEffect(() => {
    // Bills will be loaded automatically by the store
  }, [bills]);

  // Sync bills with Google Calendar when they change
  useEffect(() => {
    if (syncSettings.syncEnabled && bills && bills.length > 0) {
      // Only sync automatically if it's been more than 1 hour since last sync
      const lastSync = syncSettings.lastSyncedAt ? new Date(syncSettings.lastSyncedAt) : null;
      const shouldSync = !lastSync || (new Date().getTime() - lastSync.getTime() > 3600000);

      if (shouldSync) {
        syncBillsToCalendar();
      }
    }
  }, [bills, syncSettings.syncEnabled, syncSettings.lastSyncedAt, syncBillsToCalendar]);

  // Generate future bill instances for recurring bills
  const generateFutureBillInstances = useCallback((bills: Bill[], monthsAhead: number = 3): Bill[] => {
    const futureBills: Bill[] = [];
    const today = new Date();

    bills.forEach(bill => {
      if (!bill.isRecurring || !bill.frequency || bill.isRenewedOriginal) return;

      let currentDate = bill.dueDate;
      for (let i = 0; i < monthsAhead * 2; i++) { // Generate more instances for weekly/biweekly
        const nextDate = calculateNextDueDate(currentDate, bill.frequency);
        if (!nextDate) break;

        const nextDueDate = parseLocalDateString(nextDate);
        if (!nextDueDate || nextDueDate <= today) {
          currentDate = nextDate;
          continue;
        }

        // Stop if we've gone too far ahead
        const monthsFromNow = (nextDueDate.getFullYear() - today.getFullYear()) * 12 +
          (nextDueDate.getMonth() - today.getMonth());
        if (monthsFromNow > monthsAhead) break;

        // Create future bill instance
        futureBills.push({
          ...bill,
          id: `${bill.id}-future-${i}`,
          dueDate: nextDate,
          isPaid: false,
          paidDate: undefined,
          renewalOfBillId: bill.id,
          isRenewedOriginal: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });

        currentDate = nextDate;
      }
    });

    return futureBills;
  }, []);

  // Transform Bill data into CalendarEvent format, ensuring string ID
  const sortedEvents = useMemo(() => {
    if (!bills || isLoading) return [];

    // Combine current bills with future instances
    const allBills = [...bills];
    if (showPaid) {
      // Only show future instances when showing paid bills (to avoid clutter)
      const futureBills = generateFutureBillInstances(bills, 3);
      allBills.push(...futureBills);
    }

    return allBills
      .filter((bill: Bill) => showPaid || !bill.paidDate)
      .map((bill: Bill) => {
        // Use consistent date parsing throughout
        const startDate = parseLocalDateString(bill.dueDate);
        if (!startDate || isNaN(startDate.getTime())) {
          console.warn(`Invalid due date for bill ${bill.name}: ${bill.dueDate}`);
          return null; // Skip bills with invalid dates
        }

        const isPaid = !!bill.paidDate;
        const isOverdue = !isPaid && isPast(startDate) && !isToday(startDate);
        const isFuture = bill.id.includes('-future-'); // Check if this is a future instance

        const status = isPaid ? 'paid' : isOverdue ? 'overdue' : isFuture ? 'future' : 'upcoming';
        const riskScore = assessBillRisk(bill);
        const urgency = getBillUrgency(bill);

        return {
          id: bill.id,
          title: bill.name,
          start: startDate,
          end: startDate,
          allDay: true,
          resource: bill,
          status,
          riskScore,
          urgency,
        };
      })
      .filter((event): event is CalendarEvent => event !== null)
      .sort((a, b) => {
        if (!a || !b) return 0;
        return b.riskScore - a.riskScore;
      });
  }, [bills, isLoading, showPaid, generateFutureBillInstances]);

  // Apply final transformations to events with consistent date parsing
  const transformedEvents = useMemo<CalendarEvent[]>(() => {
    return sortedEvents.map((event: any) => {
      if (!event?.resource || typeof event.resource.id !== 'string' || typeof event.resource.dueDate !== 'string') {
        console.error('Invalid event in transformedEvents:', event);
        return null;
      }

      const bill = event.resource;
      const isPaid = !!bill.paidDate;

      // Use consistent date parsing - parseLocalDateString instead of new Date()
      const dueDate = parseLocalDateString(bill.dueDate);
      if (!dueDate || isNaN(dueDate.getTime())) {
        console.warn(`Invalid due date in transformedEvents for bill ${bill.name}: ${bill.dueDate}`);
        return null;
      }

      const isOverdue = !isPaid && isPast(dueDate) && !isToday(dueDate);

      const status = isPaid ? 'paid' : isOverdue ? 'overdue' : 'upcoming';
      const riskScore = assessBillRisk(bill);
      const urgency = getBillUrgency(bill);

      const finalEvent: CalendarEvent = {
        id: bill.id,
        title: bill.name,
        start: dueDate,
        end: dueDate,
        allDay: true,
        resource: bill,
        status,
        riskScore,
        urgency,
        mobileIndicator: isMobile ? true : false
      };
      return finalEvent;
    }).filter((ev): ev is CalendarEvent => !!ev);
  }, [sortedEvents, isMobile]);

  // Helper: get events for selected date
  const selectedDateEvents = useMemo(() => {
    return transformedEvents.filter((ev: CalendarEvent) =>
      ev.start.toDateString() === selectedDate.toDateString()
    ).sort((a: CalendarEvent, b: CalendarEvent) => a.start.getTime() - b.start.getTime());
  }, [transformedEvents, selectedDate]);

  // State for payment modal
  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  const handleOpenPaymentModal = useCallback((bill: Bill) => {
    setSelectedBill(bill);
    setIsPaymentModalOpen(true);
  }, [setSelectedBill, setIsPaymentModalOpen]);

  const handleClosePaymentModal = () => {
    setSelectedBill(null);
    setIsPaymentModalOpen(false);
  };

  const handleMarkPaid = async (billId: string, updateDueDate: boolean, newDueDate?: string) => {
    try {
      await markBillPaid(billId);
      if (updateDueDate && newDueDate) {
        await updateBill(billId, { dueDate: newDueDate });
      }
      handleClosePaymentModal();
    } catch (error) {
      console.error("Failed to mark bill as paid:", error);
    }
  };

  // Handler to open the payment modal
  const handleOpenPaymentModalCallback = useCallback((billToOpen: Bill) => {
    handleOpenPaymentModal(billToOpen);
  }, [handleOpenPaymentModal]);

  // Handler for adding a bill
  const handleAddBillClickCallback = useCallback(() => {
    console.log('Opening Add Bill Modal');
    setSelectedDateForAdd(selectedDate);
    setIsAddModalOpen(true);
  }, [selectedDate]);

  // Pull-to-refresh handlers for mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    if (window.scrollY === 0) {
      setIsPulling(true);
      setPullStartY(e.touches[0].clientY);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (isPulling) {
      setPullMoveY(e.touches[0].clientY);
    }
  };

  const handleTouchEnd = () => {
    if (isPulling) {
      const pullDistance = pullMoveY - pullStartY;
      setIsPulling(false);
      setPullStartY(0);
      setPullMoveY(0);

      // If pulled enough, trigger refresh
      if (pullDistance > 100) {
        handleRefresh();
      }
    }
  };

  // Refresh bills data
  const handleRefresh = async () => {
    if (!isRefreshing) {
      setIsRefreshing(true);

      // Simulate refresh - in real app, you would call a data refresh function here
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsRefreshing(false);
    }
  };

  // Custom rendering for day cells to show bill indicators on mobile
  const renderDayCell = useCallback(({ date }: { date: Date }) => {
    const dayEvents = transformedEvents.filter(event =>
      event.start && format(event.start, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
    );

    const hasBills = dayEvents.length > 0;
    const hasOverdue = dayEvents.some(event => event.status === 'overdue');
    const hasPaid = dayEvents.some(event => event.status === 'paid');
    const hasUpcoming = dayEvents.some(event => event.status === 'upcoming');

    return (
      <div className="relative h-full w-full flex flex-col items-center justify-center">
        <span className="text-sm">{format(date, 'd')}</span>
        {isMobile && hasBills && (
          <div className="absolute bottom-1 w-1 h-1 rounded-full flex flex-col items-center justify-center space-y-1">
            {hasOverdue && <span className="w-1 h-1 bg-muted-red rounded-full" />}
            {hasUpcoming && !hasOverdue && <span className="w-1 h-1 bg-orange-500 rounded-full" />}
            {hasPaid && !hasOverdue && !hasUpcoming && <span className="w-1 h-1 bg-green-600 rounded-full" />}
          </div>
        )}
      </div>
    );
  }, [transformedEvents, isMobile]);

  // Function to handle edit bill
  const handleEditBill = useCallback((bill: Bill) => {
    console.log('Opening side panel to edit bill:', bill.name);
    // openSidePanelForBill(bill);
  }, []);

  // Memoized calculation of risk score and urgency level for a bill
  const assessBillRiskMemo = useCallback((bill: Bill) => {
    return assessBillRisk(bill);
  }, []);

  const getBillUrgencyMemo = useCallback((bill: Bill) => {
    return getBillUrgency(bill);
  }, []);

  const components = {
    eventWrapper: (props: any) => (
      <EventTooltip {...props} />
    ),
    event: (props: any) => (
      <EventWrapper {...props} handleOpenPaymentModal={handleOpenPaymentModal} currentView={calendarView} />
    )
  };

  return (
    <div
      className={`flex flex-col w-full h-full relative ${isPulling || isRefreshing ? 'calendar-pull-active' : ''}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* BillPaymentModal for event payment */}
      {isPaymentModalOpen && selectedBill && (
        <BillPaymentModal
          isOpen={isPaymentModalOpen}
          onClose={handleClosePaymentModal}
          bill={selectedBill}
          onMarkPaid={(billId: string, updateDueDate: boolean, newDueDate?: string) => handleMarkPaid(billId, updateDueDate, newDueDate)}
        />
      )}
      {/* Pull-to-refresh indicator */}
      <div className="calendar-pull-indicator" aria-hidden="true"></div>

      {/* Mobile View */}
      {isMobile ? (
        <div>
          {/* Sticky Mobile Header */}
          <CustomToolbar
            label={format(selectedDate, 'MMMM yyyy')}
            onNavigate={(action: string) => {
              if (action === 'TODAY') setSelectedDate(new Date());
              if (action === 'PREV') setSelectedDate(addMonths(selectedDate, -1));
              if (action === 'NEXT') setSelectedDate(addMonths(selectedDate, 1));
            }}
          />

          {/* Calendar Grid (mobile-optimized) */}
          <div className="bg-light-bg dark:bg-dark-sepia rounded-b-lg shadow-sm px-1 pt-2 pb-1">
            <Calendar
              localizer={localizer}
              events={transformedEvents}
              startAccessor="start"
              endAccessor="end"
              view={Views.MONTH}
              views={[Views.MONTH]}
              date={selectedDate}
              onNavigate={(newDate: Date) => setSelectedDate(newDate)}
              components={components}
              formats={formats}
              selectable={true}
              popup={true}
              onSelectSlot={(slotInfo) => setSelectedDate(slotInfo.start)}
              onSelectEvent={(event) => setSelectedDate(event.start)}
              eventPropGetter={(event) => {
                // Type guard to validate our event structure
                if (isBillEvent(event)) {
                  const isPaid = !!event.resource.paidDate;

                  // Use consistent date parsing
                  const dueDate = parseLocalDateString(event.resource.dueDate);
                  if (!dueDate || isNaN(dueDate.getTime())) {
                    return { className: 'invalid-event' };
                  }

                  const today = new Date();
                  const isDueToday = !isPaid &&
                    dueDate.getDate() === today.getDate() &&
                    dueDate.getMonth() === today.getMonth() &&
                    dueDate.getFullYear() === today.getFullYear();

                  let customClasses = '';

                  if (isPaid) {
                    customClasses = 'paid-event';
                  } else if (event.status === 'overdue') {
                    customClasses = 'overdue-event';
                  } else if (isDueToday) {
                    customClasses = 'due-today-event';
                  }

                  return {
                    className: customClasses,
                  };
                }
                return {};
              }}
              className="rbc-calendar-mobile"
              style={{ minHeight: 320 }}
            />
          </div>

          {/* Mobile Selected Date Header */}
          <div className="mt-4 mb-2 px-3 flex justify-between items-center">
            <h3 className="font-serif text-dark-sepia dark:text-white text-lg font-semibold">
              {format(selectedDate, 'MMMM d, yyyy')}
            </h3>
            <span className="text-xs font-medium text-dark-sepia/70 dark:text-white/70 bg-light-bg dark:bg-dark-sepia/10 px-2 py-1 rounded-full">
              {selectedDateEvents.length} {selectedDateEvents.length === 1 ? 'bill' : 'bills'}
            </span>
          </div>

          {/* Mobile Agenda List */}
          <section className="overflow-y-auto max-h-[45vh] px-3 pb-4">
            {selectedDateEvents.length === 0 ? (
              <div className="text-center py-8 bg-light-bg/50 dark:bg-dark-sepia/5 rounded-lg">
                <p className="text-dark-sepia/60 dark:text-white/50 text-sm">No bills due this day.</p>
                <button
                  onClick={handleAddBillClick}
                  className="mt-2 px-3 py-1 text-xs font-medium bg-blueprint-blue/80 text-white rounded-full hover:bg-blueprint-blue transition-colors"
                >
                  Add Bill
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-sm font-medium text-dark-sepia dark:text-white">Bills Due</h4>
                  <button
                    onClick={handleAddBillClick}
                    className="px-2.5 py-1 text-xs font-medium bg-blueprint-blue/80 text-white rounded-full hover:bg-blueprint-blue transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-0.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    Add Bill
                  </button>
                </div>
                {selectedDateEvents.map((ev: CalendarEvent) => (
                  <BillAgendaEvent key={ev.id} event={ev} />
                ))}
              </div>
            )}
          </section>
        </div>
      ) : (
        /* Desktop/Webview - Full-Page Layout */
        <div className="flex flex-row h-[calc(100vh-120px)] w-full px-0" style={{ marginTop: '-60px' }}>
          {/* Left Side - Calendar */}
          <div className="flex-grow-[5] min-w-0 pr-3">
            <div className="h-full bg-light-bg dark:bg-dark-sepia rounded-lg overflow-hidden flex flex-col">
              {/* Calendar Header/Controls */}
              <div className="px-4 py-3 bg-blueprint-blue/10 dark:bg-blueprint-blue/20 border-b border-dark-sepia/10 dark:border-white/10 flex justify-between items-center">
                <div className="flex gap-2">
                  <button
                    onClick={() => setSelectedDate(new Date())}
                    className="px-3 py-1.5 bg-white dark:bg-dark-sepia/50 border border-dark-sepia/20 dark:border-white/20 rounded text-sm font-medium text-dark-sepia dark:text-white hover:bg-light-gray transition-colors"
                  >
                    Today
                  </button>
                  <div className="flex rounded overflow-hidden border border-dark-sepia/20 dark:border-white/20">
                    <button
                      onClick={() => setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() - 1, 1))}
                      className="px-2 py-1.5 bg-white dark:bg-dark-sepia/50 text-dark-sepia dark:text-white hover:bg-light-gray transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                    <button
                      onClick={() => setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 1))}
                      className="px-2 py-1.5 bg-white dark:bg-dark-sepia/50 text-dark-sepia dark:text-white hover:bg-light-gray transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>

                <h2 className="font-serif text-lg font-medium text-dark-sepia dark:text-white">
                  {format(selectedDate, 'MMMM yyyy')}
                </h2>

                <div className="flex gap-2">
                  <div className="flex items-center">
                    <label htmlFor="showPaid" className="text-xs mr-2 text-dark-sepia/70 dark:text-white/70">
                      Show Paid
                    </label>
                    <input
                      type="checkbox"
                      id="showPaid"
                      checked={showPaid}
                      onChange={(e) => setShowPaid(e.target.checked)}
                      className="form-checkbox h-3.5 w-3.5 text-blueprint-blue rounded border-dark-sepia/30 dark:border-white/30 focus:ring-blueprint-blue"
                    />
                  </div>

                  <button
                    onClick={() => setIsPaidArchiveOpen(!isPaidArchiveOpen)}
                    className="px-2 py-1.5 bg-white dark:bg-dark-sepia/50 border border-dark-sepia/20 dark:border-white/20 rounded text-xs font-medium text-dark-sepia dark:text-white hover:bg-light-gray transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z" />
                      <path fillRule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                    Archives
                  </button>
                </div>
              </div>

              {/* Calendar Grid */}
              <div className="flex-grow overflow-y-auto">
                <Calendar
                  localizer={localizer}
                  events={transformedEvents}
                  startAccessor="start"
                  endAccessor="end"
                  view={view}
                  views={{
                    month: true,
                    week: true,
                    agenda: true,
                  }}
                  date={selectedDate}
                  onNavigate={(newDate: Date) => setSelectedDate(newDate)}
                  onView={(newView: View) => setView(newView)}
                  onSelectSlot={(slotInfo) => setSelectedDate(slotInfo.start)}
                  onSelectEvent={(event) => setSelectedDate(event.start)}
                  selectable={true}
                  popup={true}
                  components={components}
                  formats={formats}
                  eventPropGetter={(event) => {
                    // Type guard to validate our event structure
                    if (isBillEvent(event)) {
                      const isPaid = !!event.resource.paidDate;

                      // Use consistent date parsing
                      const dueDate = parseLocalDateString(event.resource.dueDate);
                      if (!dueDate || isNaN(dueDate.getTime())) {
                        return { className: 'invalid-event' };
                      }

                      const today = new Date();
                      const isDueToday = !isPaid &&
                        dueDate.getDate() === today.getDate() &&
                        dueDate.getMonth() === today.getMonth() &&
                        dueDate.getFullYear() === today.getFullYear();

                      let customClasses = '';

                      if (isPaid) {
                        customClasses = 'paid-event';
                      } else if (event.status === 'overdue') {
                        customClasses = 'overdue-event';
                      } else if (event.status === 'future') {
                        customClasses = 'future-event';
                      } else if (isDueToday) {
                        customClasses = 'due-today-event';
                      }

                      return {
                        className: customClasses,
                      };
                    }
                    return {};
                  }}
                />
              </div>
            </div>
          </div>

          {/* Desktop - Right Side Panel */}
          <div className="flex-grow-[2] min-w-[300px] max-w-[450px] pl-3">
            {/* Selected Date Panel */}
            <div className="h-full bg-light-bg dark:bg-dark-sepia rounded-lg overflow-hidden flex flex-col">
              {/* Date Header */}
              <div className="px-4 py-3 bg-blueprint-blue/10 dark:bg-blueprint-blue/20 border-b border-dark-sepia/10 dark:border-white/10 flex justify-between items-center">
                <h3 className="font-serif text-lg font-medium text-dark-sepia dark:text-white">
                  {format(selectedDate, 'MMMM d, yyyy')}
                </h3>
                <div className="flex gap-2">
                  <button
                    onClick={handleAddBillClick}
                    className="px-3 py-1.5 bg-blueprint-blue text-white rounded text-xs font-medium hover:bg-blueprint-blue/90 transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    Add Bill
                  </button>
                  {/* Google Calendar Sync Button/Status */}
                  <GoogleCalendarSync bills={bills || []} />
                </div>
              </div>

              {/* Desktop Bill Detail Section */}
              <div className="overflow-y-auto flex-grow p-4">
                {selectedDateEvents.length === 0 ? (
                  <div className="text-center py-8 bg-light-gray/30 dark:bg-dark-sepia/30 rounded-lg flex flex-col items-center justify-center h-[150px]">
                    <p className="text-dark-sepia/70 dark:text-white/60 text-sm mb-3">No bills due on this date</p>
                    <button
                      onClick={handleAddBillClick}
                      className="px-3 py-1.5 text-xs font-medium bg-blueprint-blue text-white rounded hover:bg-blueprint-blue/90 transition-colors flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                      Add Bill
                    </button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {selectedDateEvents.map((ev: CalendarEvent) => (
                      <BillAgendaEvent key={ev.id} event={ev} />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
