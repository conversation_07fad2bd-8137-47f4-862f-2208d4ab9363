// netlify/functions/cleanup-data.ts
import type { <PERSON><PERSON> } from '@netlify/functions';
import { Timestamp } from 'firebase-admin/firestore';
import { db } from './utils/firebase-admin';

const handler: Handler = async () => {
    console.log('Running automated data cleanup...');
    const now = new Date();
    const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
    const oneYearAgoTimestamp = Timestamp.fromDate(oneYearAgo);
    let deletedCount = 0;

    // 1. Remove bills paid over a year ago
    const paidOldBillsSnap = await db.collection('bills')
        .where('paidDate', '<', oneYearAgoTimestamp)
        .get();
    for (const doc of paidOldBillsSnap.docs) {
        await doc.ref.delete();
        deletedCount++;
    }

    // 2. Remove bills with missing userId (orphans)
    const orphanBillsSnap = await db.collection('bills')
        .where('userId', '==', null)
        .get();
    for (const doc of orphanBillsSnap.docs) {
        await doc.ref.delete();
        deletedCount++;
    }

    // 3. (Optional) Deduplicate bills with same name, amount, and dueDate for the same user
    // (Not implemented here, but can be added if needed)

    return {
        statusCode: 200,
        body: `Cleanup complete. Deleted ${deletedCount} old or orphaned bills.`
    };
};

export { handler };
