# Payday Pilot Next - AI Agent Instructions

## Project Overview
Payday Pilot is a Next.js application for bill management with dual-mode storage (cloud/offline), smart bill renewal, and comprehensive financial features.

## Key Architecture Patterns

### Dual-Mode Storage System
- **Guest/Offline**: Bills stored in localStorage via `src/lib/billsStore.ts`
- **Authenticated/Cloud**: Bills stored in Firebase
- **Universal Access Point**: Always use `useBillsStore` hook for bill CRUD operations
- **Example:** `const { bills, addBill, updateBill } = useBillsStore();`

### Bill Renewal System
- Core logic in `src/utils/billRenewal.ts` handles automatic bill recurrence
- Bills can be renewed individually or in batch with `processAutomaticRenewal()` or `processBatchRenewal()`
- Smart due date calculation handles edge cases (month-end dates, leap years)
- Bill type detection automatically identifies recurring patterns

```typescript
// Example bill renewal
const result = processAutomaticRenewal(bill, {
  advanceMonths: 1,
  respectBusinessDays: false,
  autoDetectRecurring: true,
  skipOneTime: true
});
```

### Notification System
- Multi-channel system in `src/services/notificationService.ts`
- Supports Web Notifications API, Push API, and OneSignal
- Netlify functions handle scheduled notifications (`netlify/functions/send-reminders.ts`)

### State Management
- Context-based store pattern in `src/stores/`
- Authentication: `useAuth()` from `src/hooks/useAuth.ts`
- Bills: `useBillsStore()` from `src/stores/billsStore.tsx`
- Preferences: `useUserPreferences()` from `src/stores/userPreferencesStore.tsx`
- Theme: `useTheme()` from `src/stores/themeStore.tsx`

## Development Workflow

### Running Development Server
```bash
# Standard development
npm run dev

# Optimized dev server with more memory
npm run dev:optimized

# Clean cache and restart
npm run dev:clean
```

### Testing
```bash
# Run all tests
npm test

# Run specific tests
npm test src/utils/__tests__/billRenewal.test.ts

# Run with coverage
npm test:coverage
```

### Performance Monitoring
```bash
# Monitor performance while developing
npm run perf:dev

# Test performance of specific feature
npm run perf:test
```

### Versioning
```bash
# Bump version (updates in multiple places)
npm run bump:patch
npm run bump:minor
npm run bump:major
```

## Coding Conventions

### Component Organization
- UI components in `src/components/`
- Pages in `src/app/` (Next.js App Router)
- Reusable hooks in `src/hooks/`
- Type definitions in `src/types/`
- Services in `src/services/`

### Bill Object Structure
Bills must include these core properties:
```typescript
interface Bill {
  id: string;
  name: string;
  amount: number;
  dueDate: string; // ISO format date string
  isPaid: boolean;
  category?: string;
  // See src/types/bill.ts for complete definition
}
```

### PWA Features
- Service worker registration in `public/register-sw.js`
- Custom service worker in `public/custom-sw.js`
- Firebase messaging worker in `public/firebase-messaging-sw.js`
- OneSignal worker in `public/OneSignalSDKWorker.js`

## Important Integration Points

### Firebase Integration
- Firebase setup in `src/lib/firebase.ts`
- User authentication via `useAuth()`
- Firestore for bill storage when authenticated

### OneSignal Notifications
- OneSignal setup in `src/services/oneSignalService.ts`
- Push subscription handled via `notificationService.subscribeToPushNotifications()`

### Netlify Functions
- Scheduled functions in `netlify/functions/`
- Configured via `netlify.toml` with cron schedules
- Handle background tasks like bill reminders and data cleanup

## Common Pitfalls
- Always check authentication state before accessing user data
- Bills must be accessed via `useBillsStore()`, never directly from localStorage/Firebase
- PWA requires HTTPS for notifications to work
- Keep state updates immutable in React components
- Memory Bank system for AI context management (see `.windsurfrules`)
