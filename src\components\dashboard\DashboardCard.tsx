import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { cn } from '@/utils/cn';
import { HTMLAttributes, ReactNode } from 'react';
import './dashboard.mobile.css';

interface DashboardCardProps extends HTMLAttributes<HTMLDivElement> {
    children: ReactNode;
    className?: string;
    title?: string;
    action?: ReactNode;
    onClick?: () => void;
}

export const DashboardCard = ({ children, className = '', title, action, onClick, ...rest }: DashboardCardProps) => {
    const cardProps = onClick ? {
        role: 'button',
        tabIndex: 0,
        'aria-label': title ? `Open ${title} modal` : 'Open modal',
        onKeyDown: (e: React.KeyboardEvent) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onClick();
            }
        },
    } : {};

    return (
        <Card
            variant={onClick ? 'interactive' : 'default'}
            padding="md"
            className={cn(
                'dashboard-card-mobile text-center overflow-hidden h-full flex flex-col',
                onClick && 'min-h-[44px] cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                className
            )}
            onClick={onClick}
            {...cardProps}
            {...rest}
        >
            {(title || action) && (
                <CardHeader padding="none" className="mb-3 sm:mb-4 md:mb-5 flex-shrink-0 relative">
                    <div className="flex items-center justify-center">
                        {title && (
                            <CardTitle
                                level={2}
                                className="text-base sm:text-lg md:text-xl lg:text-2xl font-semibold text-center break-words line-clamp-2"
                            >
                                {title}
                            </CardTitle>
                        )}
                        {action && <div className="absolute right-0">{action}</div>}
                    </div>
                </CardHeader>
            )}
            <CardContent padding="none" className="flex-1 flex flex-col">
                {children}
            </CardContent>
        </Card>
    );
};
