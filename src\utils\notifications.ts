// src/utils/notifications.ts

/**
 * Converts a VAPID public key string to a Uint8Array.
 */
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/\-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

/**
 * Subscribes the user to push notifications.
 * This should only be called in response to a user action.
 */
export async function subscribeToPushNotifications(): Promise<void> {
  if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
    console.warn('Push notifications are not supported by this browser.');
    return;
  }

  try {
    // Check current permission without requesting it
    // This avoids the "Only request notification permission in response to a user gesture" violation
    const currentPermission = Notification.permission;
    
    if (currentPermission === 'default') {
      console.info('Notification permission not yet requested. Will request on user interaction.');
      return;
    }

    if (currentPermission !== 'granted') {
      console.info('Push notification permission denied.');
      return;
    }

    const registration = await navigator.serviceWorker.ready; // Ensure SW is active
    console.log('Service Worker ready for push subscription.');

    // Check if already subscribed
    let existingSubscription = await registration.pushManager.getSubscription();
    if (existingSubscription) {
      console.log('User is already subscribed to push notifications.');
      // Optional: You might want to send the subscription to the backend again
      //           in case it was missed or needs updating.
      // await sendSubscriptionToBackend(existingSubscription);
      return;
    }

    // Subscribe the user
    const vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;
    if (!vapidPublicKey) {
      console.error('VAPID public key is not defined in environment variables.');
      return;
    }

    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true, // Required
      // Cast to BufferSource to satisfy TS typing across versions
      applicationServerKey: urlBase64ToUint8Array(vapidPublicKey) as unknown as BufferSource,
    });

    console.log('Successfully subscribed to push notifications:', subscription);

    // Send the subscription object to your backend
    await sendSubscriptionToBackend(subscription);

  } catch (error) {
    console.error('Error subscribing to push notifications:', error);
  }
}

/**
 * Sends the push subscription object to the backend.
 * Replace '/api/subscribe' with your actual endpoint.
 */
async function sendSubscriptionToBackend(subscription: PushSubscription): Promise<void> {
  try {
    const response = await fetch('/api/subscribe', { // Your backend endpoint
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscription),
    });

    if (!response.ok) {
      throw new Error(`Failed to send subscription: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Subscription sent to backend successfully:', result);

  } catch (error) {
    console.error('Error sending subscription to backend:', error);
  }
}
