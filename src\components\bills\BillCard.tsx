'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Bill } from '@/types/bill';
import { formatDate, isPastDate, parseLocalDateString } from '@/utils/date';

interface BillCardProps {
  bill: Bill;
  isPending?: boolean;
  deleteMode?: boolean;
  onDelete: (billId: string) => void;
  onPay: (billId: string) => void;
  onEdit: (bill: Bill) => void;
}

function getCategoryColor(category: string): string {
  const categoryMap: Record<string, string> = {
    Housing: '#4A98F1',
    Utilities: '#7856ff',
    Transportation: '#60d394',
    Food: '#e76f51',
    Health: '#ef476f',
    Insurance: '#118ab2',
    Personal: '#f26430',
    Entertainment: '#8338ec',
    Education: '#3a86ff',
    Loans: '#fb8500',
    'Credit Cards': '#9d4edd',
    Debt: '#dc2f02',
    Savings: '#06d6a0',
    Subscriptions: '#9d4edd',
    Technology: '#0077b6',
    Other: '#adb5bd'
  };

  return categoryMap[category] || categoryMap.Other;
}

function getCategoryIcon(category: string): React.ReactNode {
  // Return different icons based on category
  switch (category.toLowerCase()) {
    case 'housing':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      );
    case 'utilities':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      );
    case 'transportation':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
        </svg>
      );
    default:
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
      );
  }
}

function getRecurringText(): string {
  return 'Recurring';
}

function getDueDateColor(dueDate: string, paidDate?: string): string {
  if (paidDate) return 'text-green-500';
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const billDueDate = parseLocalDateString(dueDate);
  if (!billDueDate) return 'text-gray-500';
  billDueDate.setHours(0, 0, 0, 0);
  
  const diffDays = Math.floor((billDueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) return 'text-red-500'; // Overdue
  if (diffDays <= 3) return 'text-yellow-500'; // Due soon
  if (diffDays <= 7) return 'text-blue-500'; // Upcoming
  return 'text-gray-500'; // Future
}

export function BillCard({ bill, isPending = false, deleteMode = false, onDelete, onPay, onEdit }: BillCardProps) {
  const [isLongPressing, setIsLongPressing] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const isOverdue = isPastDate(bill.dueDate) && !bill.paidDate;
  
  // Swipe functionality
  const [isSwiping, setIsSwiping] = useState(false);
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [showDeleteButton, setShowDeleteButton] = useState(false);
  const touchStartX = useRef<number | null>(null);
  const touchEndX = useRef<number | null>(null);
  const currentOffsetX = useRef<number>(0);
  const cardRef = useRef<HTMLDivElement>(null);
  const swipeThreshold = -80; // Amount of pixels to swipe before showing delete button
  
  // Handle swipe reset when not interacting with the card
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (cardRef.current && !cardRef.current.contains(e.target as Node) && showDeleteButton) {
        resetSwipe();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDeleteButton]);
  
  const resetSwipe = () => {
    setIsSwiping(false);
    setSwipeOffset(0);
    setShowDeleteButton(false);
    currentOffsetX.current = 0;
  };
  
  const startLongPress = useCallback(() => {
    longPressTimer.current = setTimeout(() => {
      setIsLongPressing(true);
      onDelete(bill.id);
    }, 800); // Long press for 800ms to delete
  }, [bill.id, onDelete]);
  
  const endLongPress = useCallback(() => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
    setIsLongPressing(false);
  }, []);
  
  // Handle card click - toggle expanded state
  const handleCardClick = () => {
    if (!deleteMode && !isSwiping && !showDeleteButton) {
      setIsExpanded(!isExpanded);
    }
  };

  // Fix for edit functionality to navigate to the correct route
  const handleEditBill = (e: React.MouseEvent, bill: Bill) => {
    e.stopPropagation();
    // Navigate to the edit page with the bill ID
    window.location.href = `/bills/${bill.id}/edit`;
  };
  
  // Touch event handlers for swiping
  const handleTouchStart = (e: React.TouchEvent) => {
    if (deleteMode) return;
    
    touchStartX.current = e.touches[0].clientX;
    setIsSwiping(true);
    
    // Also start the long press timer for backwards compatibility
    startLongPress();
  };
  
  const handleTouchMove = (e: React.TouchEvent) => {
    if (deleteMode || touchStartX.current === null) return;
    
    touchEndX.current = e.touches[0].clientX;
    const diff = touchEndX.current - touchStartX.current;
    
    // Only allow left swipe (negative diff)
    if (diff <= 0) {
      currentOffsetX.current = diff;
      setSwipeOffset(diff);
    }
    
    // Clear long press timer if user is swiping
    if (Math.abs(diff) > 10 && longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  };
  
  const handleTouchEnd = () => {
    endLongPress();
    
    if (deleteMode || touchStartX.current === null || touchEndX.current === null) {
      setIsSwiping(false);
      return;
    }
    
    const diff = touchEndX.current - touchStartX.current;
    
    // If swiped left beyond threshold
    if (diff < swipeThreshold) {
      setShowDeleteButton(true);
      setSwipeOffset(swipeThreshold);
      currentOffsetX.current = swipeThreshold;
    } else {
      resetSwipe();
    }
    
    touchStartX.current = null;
    touchEndX.current = null;
  };
  
  // Mouse event handlers for swiping (desktop)
  const handleMouseDown = (e: React.MouseEvent) => {
    if (deleteMode) return;
    
    touchStartX.current = e.clientX;
    setIsSwiping(true);
    
    startLongPress();
  };
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (deleteMode || touchStartX.current === null || !isSwiping) return;
    
    touchEndX.current = e.clientX;
    const diff = touchEndX.current - touchStartX.current;
    
    // Only allow left swipe (negative diff)
    if (diff <= 0) {
      currentOffsetX.current = diff;
      setSwipeOffset(diff);
    }
    
    // Clear long press timer if user is swiping
    if (Math.abs(diff) > 10 && longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  };
  
  const handleMouseUp = () => {
    endLongPress();
    
    if (deleteMode || touchStartX.current === null || touchEndX.current === null) {
      setIsSwiping(false);
      return;
    }
    
    const diff = touchEndX.current - touchStartX.current;
    
    // If swiped left beyond threshold
    if (diff < swipeThreshold) {
      setShowDeleteButton(true);
      setSwipeOffset(swipeThreshold);
      currentOffsetX.current = swipeThreshold;
    } else {
      resetSwipe();
    }
    
    setIsSwiping(false);
    touchStartX.current = null;
    touchEndX.current = null;
  };
  
  const handleMouseLeave = () => {
    endLongPress();
    
    if (isSwiping) {
      setIsSwiping(false);
      
      if (currentOffsetX.current < swipeThreshold / 2) {
        setShowDeleteButton(true);
        setSwipeOffset(swipeThreshold);
        currentOffsetX.current = swipeThreshold;
      } else {
        resetSwipe();
      }
      
      touchStartX.current = null;
      touchEndX.current = null;
    }
  };
  
  // Get the progress percentage for due date
  const getDueDateProgress = () => {
    const today = new Date();
    const dueDate = parseLocalDateString(bill.dueDate);
    if (!dueDate) return 0; // Defensive: if dueDate is null, progress is 0
    const createdDate = new Date(bill.createdAt);
    const totalDuration = dueDate.getTime() - createdDate.getTime();
    const elapsedDuration = today.getTime() - createdDate.getTime();
    const progress = Math.min(Math.max(elapsedDuration / totalDuration, 0), 1);
    return progress;
  };
  
  const dueDateColor = getDueDateColor(bill.dueDate, bill.paidDate);
  
  return (
    <div className="relative overflow-hidden">
      {/* Delete button that shows when swiped */}
      <div 
        className={`absolute inset-y-0 right-0 flex items-center justify-center bg-red-500 text-white w-20 transition-transform ${showDeleteButton ? 'translate-x-0' : 'translate-x-full'}`}
        style={{ zIndex: 1 }}
      >
        <button
          onClick={() => onDelete(bill.id)}
          className="h-full w-full flex items-center justify-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          <span className="ml-1">Delete</span>
        </button>
      </div>
      
      {/* Bill card */}
      <div 
        ref={cardRef}
        className={`
          bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border-l-4 relative transition-transform
          ${isOverdue ? 'border-red-500' : 'border-primary'}
          ${isExpanded ? 'expanded' : ''}
          ${isLongPressing ? 'opacity-70' : ''}
          ${deleteMode ? 'bg-red-50 dark:bg-red-900/20' : ''}
        `}
        style={{ 
          transform: `translateX(${swipeOffset}px)`,
          touchAction: 'pan-y'
        }}
        onClick={handleCardClick}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
      >
        {/* Delete mode overlay */}
        {deleteMode && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/5 z-10">
            <button 
              className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-6 rounded-lg flex items-center gap-2 transition-colors shadow-lg"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(bill.id);
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              <span>Delete</span>
            </button>
          </div>
        )}

        {/* Card Header */}
        <div className="flex items-center p-4">
          <div 
            className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
            style={{ backgroundColor: `${getCategoryColor(bill.category)}15` }}
          >
            <div style={{ color: getCategoryColor(bill.category) }}>
              {getCategoryIcon(bill.category)}
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 dark:text-white truncate">
              {bill.name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {bill.category}
            </p>
          </div>
          
          <div className="text-right">
            <div className="text-lg font-bold">${bill.amount.toFixed(2)}</div>
            {bill.isRecurring && (
              <div className="text-xs px-2 py-0.5 rounded-full bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200 inline-block">
                {getRecurringText()}
              </div>
            )}
          </div>
        </div>

        {/* Due Date */}
        <div className="px-4 pb-2">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs text-gray-500 dark:text-gray-400">Due date</span>
            <span className={`text-sm font-medium ${dueDateColor}`}>
              {bill.dueDate ? formatDate(bill.dueDate, 'medium') : 'No due date'}
            </span>
          </div>
          <div className="h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div 
              className={`h-full rounded-full ${isOverdue ? 'bg-red-500' : 'bg-blue-500'}`}
              style={{ width: `${getDueDateProgress() * 100}%` }}
            ></div>
          </div>
        </div>
        
        {/* Expanded content */}
        {isExpanded && (
          <div className="px-4 pt-2 pb-4 border-t border-gray-100 dark:border-gray-700 mt-2">
            <div className="flex flex-wrap gap-2 mt-2">
              <button
                onClick={(e) => handleEditBill(e, bill)}
                className="flex items-center gap-1 py-1.5 px-3 text-sm bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                <span>Edit</span>
              </button>
              
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onPay(bill.id);
                }}
                disabled={isPending || !!bill.paidDate}
                className={`flex items-center gap-1 py-1.5 px-3 text-sm rounded-lg transition-colors ${
                  bill.paidDate 
                    ? 'bg-green-100 text-green-700 dark:bg-green-800 dark:text-green-200 cursor-default'
                    : 'bg-green-500 text-white hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700'
                }`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>{bill.paidDate ? 'Paid' : 'Pay'}</span>
              </button>
              
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(bill.id);
                }}
                className="flex items-center gap-1 py-1.5 px-3 text-sm bg-red-500 text-white rounded-lg hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 transition-colors ml-auto"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                <span>Delete</span>
              </button>
            </div>
            
            {/* Simplified bill structure - no loan details */}
            
            {/* Hint text */}
            <div className="mt-4 flex flex-col gap-1">
              <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Swipe left to quickly delete this bill</span>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Long-press to quickly delete this bill</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
