'use client';

import BillForm from '@/components/bills/BillForm';
import { useBillsStore } from '@/stores/billsStore';
import { BillFormData } from '@/types/bill';
import { TrashIcon, XMarkIcon } from '@heroicons/react/24/outline';
import React, { useEffect, useRef, useState } from 'react';
import { useBillCalendarContext } from './BillCalendarContext';

export const SidePanel = () => {
  const { selectedBill, isSidePanelOpen, closeSidePanel, refreshCalendar } = useBillCalendarContext();
  const { bills, updateBill, deleteBill } = useBillsStore();
  const panelRef = useRef<HTMLDivElement>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Close on escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isSidePanelOpen) {
        closeSidePanel();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isSidePanelOpen, closeSidePanel]);

  // Handle clicking outside to close
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (panelRef.current && 
          !panelRef.current.contains(e.target as Node) && 
          isSidePanelOpen) {
        closeSidePanel();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isSidePanelOpen, closeSidePanel]);

  const handleSave = (billData: BillFormData) => {
    if (!selectedBill) return;
    // Normalize BillFormData to Partial<Bill> (e.g., map null category to undefined)
    const normalizedUpdate = {
      ...billData,
      category: billData.category ?? undefined,
    } as Partial<import('@/types/bill').Bill>;
    // Update the bill
    updateBill(selectedBill.id, normalizedUpdate);
    
    // Refresh the calendar data
    refreshCalendar();
    
    // Close the panel
    closeSidePanel();
  };

  const handleCancel = () => {
    setShowDeleteConfirm(false);
    closeSidePanel();
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedBill) return;
    
    try {
      await deleteBill(selectedBill.id);
      refreshCalendar();
      setShowDeleteConfirm(false);
      closeSidePanel();
    } catch (error) {
      console.error('Error deleting bill:', error);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
  };

  return (
    <>
      {/* Overlay */}
      <div 
        className={`calendar-edit-overlay ${isSidePanelOpen ? 'active' : ''}`} 
        onClick={closeSidePanel}
      />
      
      {/* Side Panel */}
      <div 
        ref={panelRef}
        className={`calendar-side-panel ${isSidePanelOpen ? 'open' : ''}`}
      >
        <div className="calendar-side-panel-header">
          <h2 className="calendar-side-panel-title">Edit Bill</h2>
          <div className="flex items-center gap-2">
            <button 
              className="calendar-action-button text-muted-red dark:text-muted-red" 
              onClick={handleDeleteClick}
              aria-label="Delete bill"
            >
              <TrashIcon className="h-5 w-5" />
            </button>
            <button 
              className="calendar-close-button" 
              onClick={closeSidePanel}
              aria-label="Close edit panel"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
        
        {/* Delete Confirmation Dialog */}
        {showDeleteConfirm && selectedBill && (
          <div className="absolute inset-0 bg-white dark:bg-dark-sepia/90 z-10 flex flex-col items-center justify-center p-6">
            <div className="bg-light-bg dark:bg-dark-sepia/50 p-6 rounded-lg shadow-lg border border-dark-sepia/20 dark:border-white/20 w-full max-w-md">
              <h3 className="font-serif text-xl text-dark-sepia dark:text-white mb-4">Confirm Deletion</h3>
              <p className="text-dark-sepia/80 dark:text-white/80 mb-6">
                Are you sure you want to delete <span className="font-serif font-medium">{selectedBill.name}</span>? This action cannot be undone.
              </p>
              <div className="flex justify-end gap-3">
                <button 
                  onClick={handleDeleteCancel}
                  className="px-4 py-2 text-dark-sepia dark:text-white bg-light-gray dark:bg-dark-sepia/30 hover:bg-light-gray/70 dark:hover:bg-dark-sepia/50 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button 
                  onClick={handleDeleteConfirm}
                  className="px-4 py-2 text-white bg-muted-red hover:bg-muted-red/90 rounded-lg transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* Bill Form */}
        {selectedBill && !showDeleteConfirm && (
          <div className="overflow-y-auto">
            <BillForm
              bill={selectedBill}
              onSave={handleSave}
              onCancel={handleCancel}
              allBills={bills || []}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default SidePanel;
