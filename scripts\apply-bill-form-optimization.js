#!/usr/bin/env node
/**
 * This script automatically applies the bill form and financial calculations optimizations.
 * It performs the following tasks:
 * 1. Installs required dependencies
 * 2. Backs up original files
 * 3. Applies optimized implementations
 * 4. Updates imports in relevant files
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const BACKUP_DIR = path.join(__dirname, '../backup-before-optimization');
const FILES_TO_BACKUP = [
    'src/utils/financialCalculations.ts',
    'src/components/bills/BillForm.tsx'
];
const OPTIMIZATION_FILES = [
    {
        source: 'src/utils/financialCalculations.optimized.ts',
        destination: 'src/utils/financialCalculations.ts',
        backupOriginal: true
    },
    {
        source: 'src/components/bills/BillForm.optimized.tsx',
        destination: 'src/components/bills/BillForm.tsx',
        backupOriginal: true
    }
];
const IMPORTS_TO_UPDATE = [
    {
        file: 'src/app/bills/[id]/edit/page.tsx',
        oldImport: "import BillForm from '@/components/bills/BillForm';",
        newImport: "import BillForm from '@/components/bills/BillForm';"
    },
    {
        file: 'src/components/calendar/BillCalendar.new.tsx',
        oldImport: "import { calculateLoanPayment } from '@/utils/financialCalculations';",
        newImport: "import { calculateLoanPayment, formatCurrency } from '@/utils/financialCalculations';"
    }
];

console.log('📊 Starting Bill Form and Financial Calculations Optimization...');

// Step 1: Create backup directory if it doesn't exist
if (!fs.existsSync(BACKUP_DIR)) {
    console.log('📁 Creating backup directory...');
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Step 2: Backup original files
console.log('💾 Backing up original files...');
FILES_TO_BACKUP.forEach(file => {
    const sourcePath = path.join(__dirname, '..', file);
    const backupPath = path.join(BACKUP_DIR, path.basename(file));

    if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, backupPath);
        console.log(`  ✅ Backed up ${file} to ${backupPath}`);
    } else {
        console.log(`  ⚠️ Warning: Could not find ${file} to back up`);
    }
});

// Step 3: Install decimal.js dependency if not already installed
console.log('📦 Checking for required dependencies...');
try {
    // Try to require decimal.js to see if it's installed
    require.resolve('decimal.js');
    console.log('  ✅ decimal.js is already installed');
} catch (e) {
    console.log('  ⚙️ Installing decimal.js...');
    try {
        execSync('npm install decimal.js@10.4.3', { stdio: 'inherit' });
        console.log('  ✅ decimal.js installed successfully');
    } catch (err) {
        console.error('  ❌ Failed to install decimal.js', err);
        process.exit(1);
    }
}

// Step 4: Apply optimized implementations
console.log('🔄 Applying optimized implementations...');
OPTIMIZATION_FILES.forEach(({ source, destination, backupOriginal }) => {
    const sourcePath = path.join(__dirname, '..', source);
    const destPath = path.join(__dirname, '..', destination);

    if (fs.existsSync(sourcePath)) {
        if (backupOriginal && fs.existsSync(destPath) && !FILES_TO_BACKUP.includes(destination)) {
            const backupPath = path.join(BACKUP_DIR, path.basename(destination));
            fs.copyFileSync(destPath, backupPath);
            console.log(`  ✅ Backed up ${destination} to ${backupPath}`);
        }

        fs.copyFileSync(sourcePath, destPath);
        console.log(`  ✅ Applied ${source} to ${destination}`);
    } else {
        console.log(`  ❌ Error: Could not find optimized file ${source}`);
    }
});

// Step 5: Update imports in relevant files
console.log('📝 Updating imports in relevant files...');
IMPORTS_TO_UPDATE.forEach(({ file, oldImport, newImport }) => {
    const filePath = path.join(__dirname, '..', file);

    if (fs.existsSync(filePath)) {
        let content = fs.readFileSync(filePath, 'utf8');
        if (content.includes(oldImport) && oldImport !== newImport) {
            content = content.replace(oldImport, newImport);
            fs.writeFileSync(filePath, content);
            console.log(`  ✅ Updated imports in ${file}`);
        } else {
            console.log(`  ℹ️ No import changes needed in ${file}`);
        }
    } else {
        console.log(`  ⚠️ Warning: Could not find ${file} to update imports`);
    }
});

console.log('\n🎉 Bill Form and Financial Calculations Optimization Complete!');
console.log('\nOptimization Summary:');
console.log('✅ Improved math precision with Decimal.js');
console.log('✅ Enhanced performance with memoization and caching');
console.log('✅ Added comprehensive validation and error handling');
console.log('✅ Improved user experience with better formatting');
console.log('✅ Modernized bill type detection algorithms');
console.log('\nPlease restart your development server for changes to take effect.');
