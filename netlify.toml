[build]
  command = "next build"
  publish = ".next"

[build.environment]
  NETLIFY_NEXT_PLUGIN_SKIP = "false"
  NEXT_PHASE = "phase-production-build"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[functions]
  node_bundler = "esbuild"

[functions."send-reminders"]
  schedule = "0 9 * * *"

[functions."scheduled-gmail-import"]
  schedule = "0 2 * * *"

[functions."cleanup-data"]
  schedule = "0 3 * * 0"

[functions."send-due-date-notifications"]
  schedule = "0 9 * * *"

[functions."send-onesignal-notifications"]
  schedule = "5 9 * * *" # Runs daily at 9:05 AM UTC
