'use client';

import { useUserPreferences } from '@/stores/userPreferencesStore';
import { useEffect } from 'react';
import DashboardHome from './DashboardHome';

export default function DashboardPage() {
  const trackAction = useUserPreferences(state => state.trackAction);

  // Track page view for analytics
  useEffect(() => {
    trackAction('view_dashboard');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run once on mount

  // Render the modal-based dashboard with all cards visible
  return <DashboardHome />;
}
