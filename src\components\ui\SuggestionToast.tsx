import React from 'react';

interface SuggestionToastProps {
    billName: string;
    suggestedCategory: string;
    onAccept: () => void;
    onDismiss: () => void;
}

const SuggestionToast: React.FC<SuggestionToastProps> = ({ billName, suggestedCategory, onAccept, onDismiss }) => {
    return (
        <div className="fixed bottom-4 right-4 z-50 max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start">
                <div className="flex-shrink-0 pt-0.5">
                    {/* Optional: Add an icon here */}
                    <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
                <div className="ml-3 w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                        Automate Category?
                    </p>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-300">
                        Set category to <strong>{suggestedCategory}</strong> for bills like &quot;<strong>{billName}</strong>&quot;?
                    </p>
                    <div className="mt-3 flex space-x-3">
                        <button
                            type="button"
                            className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                            onClick={onAccept}
                        >
                            Accept
                        </button>
                        <button
                            type="button"
                            className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onClick={onDismiss}
                        >
                            Dismiss
                        </button>
                    </div>
                </div>
                <div className="ml-4 flex-shrink-0 flex">
                    <button
                        className="inline-flex text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                        onClick={onDismiss}
                    >
                        <span className="sr-only">Close</span>
                        {/* Optional: Add a close icon here */}
                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    );
};

export default SuggestionToast;
