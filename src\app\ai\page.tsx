'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { useBillsStore } from '@/stores/billsStore';
import { analyzeBillData, generateResponse, AIResponse } from '@/utils/aiUtils';
import { useUserPreferences } from '@/stores/userPreferencesStore';
import BillInsights from '@/components/calendar/BillInsights';

// Define interfaces for AI functionality
interface AiMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  responseData?: AIResponse;
}

interface AiSuggestion {
  title: string;
  prompt: string;
}

export default function AiPage() {
  // Get bills data from the bills store
  const { bills, isLoading: billsLoading } = useBillsStore();
  const { notificationPreferences } = useUserPreferences();
  
  const [messages, setMessages] = useState<AiMessage[]>([
    {
      role: 'assistant',
      content: 'Hello! I\'m your financial assistant. How can I help you with your bills and finances today?',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [billAnalysis, setBillAnalysis] = useState<any>(null);

  // Analyze bills data when it's loaded
  useEffect(() => {
    if (bills && bills.length > 0) {
      const analysis = analyzeBillData(bills);
      setBillAnalysis(analysis);
    }
  }, [bills]);

  // Predefined suggestions for common financial questions
  const suggestions: AiSuggestion[] = [
    {
      title: 'Upcoming Bills',
      prompt: 'What bills do I have coming up soon?'
    },
    {
      title: 'Overdue Bills',
      prompt: 'Do I have any overdue bills I need to pay?'
    },
    {
      title: 'Spending Analysis',
      prompt: 'Analyze my spending patterns and suggest a better budget'
    },
    {
      title: 'Bill Optimization',
      prompt: 'Which bills could I potentially reduce or eliminate?'
    }
  ];

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: AiMessage = {
      role: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Generate AI response based on bill data and user query
    setTimeout(() => {
      let responseContent: AIResponse = {
        text: 'I\'m still loading your bill data. Please try again in a moment.',
        type: 'text'
      };

      if (billAnalysis) {
        responseContent = generateResponse(userMessage.content, billAnalysis);
      }

      const assistantMessage: AiMessage = {
        role: 'assistant',
        content: responseContent.text,
        timestamp: new Date(),
        responseData: responseContent
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 800);
  };

  const handleSuggestionClick = (suggestion: AiSuggestion) => {
    setInputValue(suggestion.prompt);
  };

  // Format currency for display
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
  };

  // Render chart if the response type is chart
  const renderResponseExtra = (message: AiMessage) => {
    if (!message.responseData) return null;
    
    const { type, data } = message.responseData;
    
    if (type === 'chart' && Array.isArray(data)) {
      return (
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h4 className="font-serif text-sm font-medium mb-2">Spending by Category</h4>
          <div className="space-y-2">
            {data.map((item, idx) => (
              <div key={idx} className="flex items-center">
                <div className="text-xs w-1/4">{item.category}</div>
                <div className="flex-1 mx-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-finance-green" 
                      style={{ width: `${Math.min(100, (item.total / billAnalysis.totalDue) * 100)}%` }}
                    ></div>
                  </div>
                </div>
                <div className="text-xs text-right w-1/5">{formatCurrency(item.total)}</div>
              </div>
            ))}
          </div>
        </div>
      );
    }
    
    if (type === 'list' && Array.isArray(data)) {
      return (
        <div className="mt-4 overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" className="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Bill</th>
                <th scope="col" className="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                <th scope="col" className="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Due Date</th>
                <th scope="col" className="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
              {data.map((bill, idx) => {
                const dueDate = new Date(bill.dueDate);
                const today = new Date();
                const isOverdue = dueDate < today && !bill.isPaid;
                const isDueToday = dueDate.getDate() === today.getDate() && 
                                  dueDate.getMonth() === today.getMonth() && 
                                  dueDate.getFullYear() === today.getFullYear();
                
                return (
                  <tr key={idx}>
                    <td className="py-2 px-3 text-xs">{bill.name}</td>
                    <td className="py-2 px-3 text-xs">{formatCurrency(bill.amount || 0)}</td>
                    <td className="py-2 px-3 text-xs">{format(new Date(bill.dueDate), 'MMM dd, yyyy')}</td>
                    <td className="py-2 px-3 text-xs">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        isOverdue ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                        isDueToday ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      }`}>
                        {isOverdue ? 'Overdue' : isDueToday ? 'Due Today' : 'Upcoming'}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      );
    }
    
    return null;
  };

  return (
    <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="mb-6">
        <h1 className="text-2xl font-serif font-bold mb-2">Financial AI Assistant</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Get personalized financial insights and recommendations based on your billing data.
          {billsLoading && ' (Loading your bills data...)'}
        </p>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden mb-4">
        <div className="p-4 bg-finance-green/5 border-b border-finance-green/10">
          <h2 className="font-serif text-lg font-medium text-finance-green">Ask me anything about your finances</h2>
        </div>

        {/* Messages container */}
        <div className="p-4 h-96 overflow-y-auto flex flex-col space-y-4">
          {messages.map((message, index) => (
            <div 
              key={index} 
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div 
                className={`max-w-3/4 p-3 rounded-lg ${
                  message.role === 'user' 
                    ? 'bg-finance-green text-white' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                }`}
              >
                <div className="whitespace-pre-line">{message.content}</div>
                {message.role === 'assistant' && renderResponseExtra(message)}
                <span className="text-xs opacity-70 mt-1 block">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="max-w-3/4 p-3 rounded-lg bg-gray-100 dark:bg-gray-700">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce delay-75"></div>
                  <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce delay-150"></div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Input area */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Ask about your bills, budgeting, or financial advice..."
              className="flex-1 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-finance-green"
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className={`ml-2 px-4 py-2 rounded-lg ${
                inputValue.trim() && !isLoading
                  ? 'bg-finance-green text-white hover:bg-finance-green/90' 
                  : 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              }`}
            >
              Send
            </button>
          </div>
        </div>
      </div>

      {/* Suggestions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <h3 className="font-serif text-lg font-medium mb-3">Suggested Questions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              className="text-left p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <span className="font-medium text-finance-green">{suggestion.title}</span>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{suggestion.prompt}</p>
            </button>
          ))}
        </div>
      </div>
      
      {/* Financial Insights from Dashboard */}
      <div className="mt-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <h3 className="font-serif text-lg font-medium mb-3">Your Financial Insights</h3>
        <BillInsights />
      </div>
    </div>
  );
}
