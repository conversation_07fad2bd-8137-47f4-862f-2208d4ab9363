import { renderHook } from '@testing-library/react';
import { useBillsStore } from '../billsStore';

// Mock Firebase
jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  getDocs: jest.fn(() => Promise.resolve({ docs: [] })),
  addDoc: jest.fn(() => Promise.resolve({ id: 'mock-id' })),
  updateDoc: jest.fn(() => Promise.resolve()),
  deleteDoc: jest.fn(() => Promise.resolve()),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  onSnapshot: jest.fn(),
}))

jest.mock('firebase/auth', () => ({
  onAuthStateChanged: jest.fn((auth, callback) => {
    callback(null); // Simulate unauthenticated user
    return jest.fn(); // Return unsubscribe function
  }),
}))

jest.mock('@/lib/firebase', () => ({
  db: {},
  auth: {},
}))

jest.mock('@/lib/billsStore', () => ({
  __esModule: true,
  default: {
    getBills: jest.fn(() => []),
    addBill: jest.fn(),
    updateBill: jest.fn(),
    deleteBill: jest.fn(),
    getBillById: jest.fn(),
  }
}))

describe('useBillsStore', () => {
  it('provides context interface', () => {
    const { result } = renderHook(() => useBillsStore())

    // Just verify that the basic interface is there
    expect(typeof result.current.addBill).toBe('function')
    expect(typeof result.current.updateBill).toBe('function')
    expect(typeof result.current.deleteBill).toBe('function')
    expect(typeof result.current.markBillPaid).toBe('function')
    expect(typeof result.current.markBillUnpaid).toBe('function')
    expect(typeof result.current.renewBill).toBe('function')
    expect(Array.isArray(result.current.bills)).toBeTruthy()
    expect(typeof result.current.isLoading).toBe('boolean')
    expect(result.current.error === null || typeof result.current.error === 'string').toBeTruthy()
  })
})
