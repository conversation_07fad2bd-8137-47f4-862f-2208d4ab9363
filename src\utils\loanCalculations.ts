import { LoanInfo } from '@/types/bill';
import { parseLocalDateString } from '@/utils/date';

export function calculateLoanPayment(loanInfo: LoanInfo): {
  monthlyPayment: number;
  biweeklyPayment: number;
  weeklyPayment: number;
  totalInterest: number;
  totalPayment: number;
} {
  const P = loanInfo.originalAmount; // Principal
  const r = (loanInfo.interestRate / 100) / 12; // Monthly interest rate
  const n = loanInfo.loanTerm; // Total number of months

  // Calculate monthly payment using amortization formula
  let monthlyPayment: number;
  if (r === 0) {
    monthlyPayment = P / n;
  } else {
    monthlyPayment = (P * r * Math.pow(1 + r, n)) / (Math.pow(1 + r, n) - 1);
  }

  // Calculate other payment frequencies
  const biweeklyPayment = (monthlyPayment * 12) / 26;
  const weeklyPayment = (monthlyPayment * 12) / 52;

  // Calculate total interest
  const totalPayment = monthlyPayment * n;
  const totalInterest = totalPayment - P;

  return {
    monthlyPayment,
    biweeklyPayment,
    weeklyPayment,
    totalInterest,
    totalPayment
  };
}

export function calculateAmortizationSchedule(loanInfo: LoanInfo): {
  date: string;
  payment: number;
  principal: number;
  interest: number;
  remainingBalance: number;
}[] {
  const schedule = [];
  const monthlyPayment = calculateLoanPayment(loanInfo).monthlyPayment;
  let balance = loanInfo.originalAmount;
  const monthlyRate = (loanInfo.interestRate / 100) / 12;
  const startDate = parseLocalDateString(loanInfo.startDate);
  if (!startDate) return [];

  for (let month = 0; month < loanInfo.loanTerm; month++) {
    const interest = balance * monthlyRate;
    const principal = monthlyPayment - interest;
    balance = balance - principal;

    const paymentDate = new Date(startDate);
    paymentDate.setMonth(paymentDate.getMonth() + month);

    schedule.push({
      date: paymentDate.toISOString().split('T')[0],
      payment: monthlyPayment,
      principal: principal,
      interest: interest,
      remainingBalance: Math.max(0, balance)
    });
  }

  return schedule;
}

export function calculateProgress(loanInfo: LoanInfo): {
  percentagePaid: number;
  amountPaid: number;
  totalInterestPaid: number;
  remainingPayments: number;
} {
  const schedule = calculateAmortizationSchedule(loanInfo);
  const currentDate = new Date();
  const startDate = parseLocalDateString(loanInfo.startDate);
  if (!startDate) {
    return {
      percentagePaid: 0,
      amountPaid: 0,
      totalInterestPaid: 0,
      remainingPayments: loanInfo.loanTerm
    };
  }

  // Calculate months elapsed
  const monthsElapsed = (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
    (currentDate.getMonth() - startDate.getMonth());

  // Get all payments made up to current date
  const paymentsMade = schedule.slice(0, monthsElapsed);

  // Calculate totals
  const amountPaid = paymentsMade.reduce((sum, payment) => sum + payment.payment, 0);
  const totalInterestPaid = paymentsMade.reduce((sum, payment) => sum + payment.interest, 0);

  // Calculate percentage paid of original amount
  const percentagePaid = (amountPaid / loanInfo.originalAmount) * 100;

  // Calculate remaining payments
  const remainingPayments = loanInfo.loanTerm - monthsElapsed;

  return {
    percentagePaid,
    amountPaid,
    totalInterestPaid,
    remainingPayments: Math.max(0, remainingPayments)
  };
}