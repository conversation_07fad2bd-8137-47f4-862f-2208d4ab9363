'use client';

import { useBillsStore } from '@/stores/billsStore';
import { Bill } from '@/types/bill';
import { formatDate, getNextOccurrence, getRelativeTimeString, isPastDate, parseLocalDateString } from '@/utils/date';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function BillDetailsPage() {
  const { bills, markBillPaid, markBillUnpaid, deleteBill, isLoading: billsLoading } = useBillsStore();
  const router = useRouter();
  const params = useParams();
  const [bill, setBill] = useState<Bill | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Find the bill to display
  useEffect(() => {
    if (params.id && bills && bills.length > 0) {
      const foundBill = bills.find(b => b.id === params.id);

      if (foundBill) {
        setBill(foundBill);
      } else {
        setError('Bill not found');
      }

      setIsLoading(false);
    }
  }, [params.id, bills]);

  // Handle payment toggle
  const handleTogglePaid = async () => {
    if (!bill) return;
    try {
      if (bill.paidDate) {
        await markBillUnpaid(bill.id);
      } else {
        await markBillPaid(bill.id);
      }
    } catch (e) {
      console.error('Failed to toggle paid status', e);
    }
  };

  // Handle bill deletion
  const handleDelete = () => {
    if (!bill) return;
    deleteBill(bill.id);
    router.push('/dashboard');
  };

  // Get category color
  const getCategoryColor = (category: string): string => {
    const categoryMap: Record<string, string> = {
      Housing: '#4A98F1',
      Utilities: '#7856ff',
      Transportation: '#60d394',
      Food: '#e76f51',
      Health: '#ef476f',
      Insurance: '#118ab2',
      Personal: '#f26430',
      Entertainment: '#8338ec',
      Education: '#3a86ff',
      Debt: '#fb8500',
      Savings: '#06d6a0',
      Other: '#adb5bd'
    };

    return categoryMap[category] || categoryMap.Other;
  };

  // Get status label and color
  const getStatusInfo = (bill: Bill) => {
    if (bill.paidDate) {
      return { label: 'Paid', color: 'bg-green-500' };
    }

    if (isPastDate(bill.dueDate)) {
      return { label: 'Overdue', color: 'bg-red-500' };
    }

    const dueDate = parseLocalDateString(bill.dueDate);
    if (!dueDate) return { label: 'Upcoming', color: 'bg-blue-500' };
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 3) {
      return { label: 'Due Soon', color: 'bg-yellow-500' };
    }

    return { label: 'Upcoming', color: 'bg-blue-500' };
  };

  // Calculate payment progress for loans/debt
  const calculateProgress = (bill: Bill) => {
    // Simplified bill structure doesn't include loan info
    return 0;
  };

  // Format recurring text
  const getRecurringText = (): string => {
    return 'Recurring';
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-6 flex justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (error || !bill) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Error</h2>
          <p>{error || 'Bill not found'}</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="mt-4 px-4 py-2 bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700 rounded-lg transition-colors"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(bill);
  const isLoanOrDebt = bill.category.toLowerCase() === 'loan' || bill.category.toLowerCase() === 'debt';

  return (
    <div className="max-w-4xl mx-auto px-4 py-6">
      <div className="mb-6 flex justify-between items-center">
        <button
          onClick={() => router.back()}
          className="flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back
        </button>

        <div className="flex gap-2">
          <Link
            href={`/bills/${bill.id}/edit`}
            className="flex items-center gap-1 px-3 py-1.5 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors text-sm"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            <span>Edit</span>
          </Link>

          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="flex items-center gap-1 px-3 py-1.5 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 text-red-700 dark:text-red-300 rounded-lg transition-colors text-sm"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            <span>Delete</span>
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div className="flex items-center">
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
              style={{ backgroundColor: `${getCategoryColor(bill.category)}15` }}
            >
              <div style={{ color: getCategoryColor(bill.category) }}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">{bill.name}</h1>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-gray-500 dark:text-gray-400">{bill.category}</span>
                {bill.isRecurring && (
                  <span className="text-xs px-2 py-0.5 rounded-full bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200">
                    {getRecurringText()}
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className={`px-3 py-1 rounded-full text-white text-sm font-medium ${statusInfo.color}`}>
            {statusInfo.label}
          </div>
        </div>

        {/* Main Content */}
        <div className="p-6">
          {/* Amount and Due Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Amount</div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">${bill.amount.toFixed(2)}</div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Due Date</div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {bill.dueDate ? formatDate(bill.dueDate, 'medium') : 'No due date'}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {bill.dueDate ? getRelativeTimeString(bill.dueDate) : ''}
              </div>
            </div>
          </div>

          {/* Recurring Details */}
          {bill.isRecurring && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Recurring Details</div>
              <div className="text-gray-900 dark:text-white">
                This bill repeats {getRecurringText().toLowerCase()}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Next occurrence: {formatDate(getNextOccurrence(bill.dueDate, 'monthly'), 'medium')}
              </div>
            </div>
          )}

          {/* Reminder Settings */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Reminder</div>
            <div className="text-gray-900 dark:text-white">
              {bill.reminderDays && bill.reminderDays > 0
                ? `Reminder set for ${bill.reminderDays} day${bill.reminderDays > 1 ? 's' : ''} before due date`
                : 'No reminder set'}
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-wrap gap-4 mt-8">
            <button
              onClick={handleTogglePaid}
              className={`px-6 py-3 rounded-lg flex items-center gap-2 transition-colors ${bill.paidDate
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  : 'bg-green-500 hover:bg-green-600 text-white'
                }`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>{bill.paidDate ? 'Already Paid' : 'Mark as Paid'}</span>
            </button>

            <Link
              href={`/bills/${bill.id}/edit`}
              className="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center gap-2 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
              <span>Edit Bill</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6 shadow-xl">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Confirm Deletion</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-6">
              Are you sure you want to delete this bill? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-lg transition-colors"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                onClick={handleDelete}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
