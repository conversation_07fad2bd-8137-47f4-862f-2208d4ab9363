import { BILL_CATEGORIES } from '@/types/bill';
import { UserRule } from '@/types/rules';
import { addMonths, addQuarters, addWeeks, addYears, differenceInDays, format, isValid, parse } from 'date-fns';
import { parseLocalDateString } from './date'; // Use consistent date parsing
import { detectBillFrequency } from './financialIntelligence'; // Import frequency detection

// --- START: Category Keyword Map ---
const categoryKeywords: Record<string, string[]> = {
  'Housing': ['rent', 'mortgage', 'property tax', 'hoa'],
  'Utilities': ['electric', 'electricity', 'gas', 'water', 'sewer', 'trash', 'utility'],
  'Transportation': ['car payment', 'gasoline', 'fuel', 'bus pass', 'uber', 'lyft', 'auto insurance'],
  'Food': ['grocery', 'groceries', 'restaurant', 'dining'],
  'Insurance': ['health insurance', 'life insurance', 'home insurance', 'renters insurance'],
  'Healthcare': ['doctor', 'dentist', 'pharmacy', 'medical', 'hospital'],
  'Debt': ['credit card', 'loan', 'student loan', 'amex', 'visa', 'mastercard', 'discover'],
  'Entertainment': ['netflix', 'hulu', 'spotify', 'disney+', 'hbo', 'streaming', 'movies', 'concert'],
  'Internet & Phone': ['internet', 'wifi', 'cable', 'phone', 'mobile', 'verizon', 'at&t', 't-mobile', 'comcast', 'xfinity'],
  'Subscriptions': ['subscription', 'membership', 'amazon prime'],
  'Other': []
};

// Function to suggest a category based on the bill name
/**
 * Suggests a bill category based on the bill name and user rules.
 * Checks user rules first, then falls back to keyword-based logic.
 * @param billName The bill name to analyze
 * @param userRules Optional user rules for custom categorization
 * @returns The suggested category, or null if none found
 */
export function suggestCategory(
  billName: string,
  userRules: UserRule[] = []
): string | null {
  const nameLower = billName.toLowerCase();

  // --- START: Process User Rules --- prioritized
  const sortedRules = userRules
    .filter(rule => rule.action.type === 'setCategory' && rule.condition.field === 'name')
    .sort((a, b) => (b.priority || 0) - (a.priority || 0));

  for (const rule of sortedRules) {
    const conditionValueLower = rule.condition.value.toLowerCase();
    let match = false;
    switch (rule.condition.operator) {
      case 'contains':
        match = nameLower.includes(conditionValueLower);
        break;
      case 'equals':
        match = nameLower === conditionValueLower;
        break;
      case 'startsWith':
        match = nameLower.startsWith(conditionValueLower);
        break;
      case 'endsWith':
        match = nameLower.endsWith(conditionValueLower);
        break;
    }
    if (match) {
      // Ensure the category exists in our defined categories
      if (BILL_CATEGORIES.includes(rule.action.value)) {
        return rule.action.value;
      }
    }
  }
  // --- END: Process User Rules ---

  // --- START: Default Keyword Logic (Fallback if no user rule matched) ---
  // Check if billName is not empty or just whitespace
  if (!nameLower || nameLower.trim() === '') {
    return null;
  }

  // Iterate through the defined categories and their keywords
  for (const category of Object.keys(categoryKeywords)) {
    const keywords = categoryKeywords[category];
    if (keywords.some(keyword => nameLower.includes(keyword.toLowerCase()))) {
      // Only return the category if it's a valid, recognized category from BILL_CATEGORIES
      if (BILL_CATEGORIES.includes(category)) {
        return category;
      }
      // If the category from categoryKeywords is not in BILL_CATEGORIES,
      // do not suggest it. Loop continues to check other keywords/categories.
    }
  }

  return null;
}

// Function to extract a potential bill amount from description text
/**
 * Extracts a potential bill amount from a description string.
 * Supports patterns like "$120.00", "Amount: 99.99", "pay 50", etc.
 * Handles various currency symbols and formats.
 * @param description The text to search for an amount
 * @returns The extracted amount as a number, or null if not found
 */
export function extractAmountFromDescription(description: string): number | null {
  if (!description) return null;

  // Regular expressions to find common amount patterns
  // Handles: $120.00, 120.00, Amount: 99.99, Total Due: 50, balance of 35.50, pay 50, charge of 25
  // Allows for optional currency symbols ($, €, £), commas, and decimals.
  const amountPatterns = [
    /(?:amount is|amount:|total due:|balance of:?|pay|charge of|fee:?)\s*[$€£]?\s*([\d,]+\.?\d*)/i, // Keywords followed by amount
    /[$€£]\s*([\d,]+\.?\d*)/, // Currency symbol followed by amount
    /(?:^|\s)([\d,]+\.\d{2})(?:$|\s)/, // Standalone amount with two decimal places
    /(?:^|\s)([\d,]+)(?:$|\s)/ // Standalone integer amount (less precise, lower priority)
  ];

  for (const pattern of amountPatterns) {
    const match = description.match(pattern);
    if (match && match[1]) {
      // Remove commas and convert to number
      const amountStr = match[1].replace(/,/g, '');
      const amount = parseFloat(amountStr);
      if (!isNaN(amount) && amount > 0) {
        return amount; // Return the first valid amount found
      }
    }
  }

  return null;
}

// Function to extract a potential due date from description or name text
// Returns date in 'YYYY-MM-DD' format if found and valid
/**
 * Extracts a due date from a string using common date patterns.
 * Returns date in 'YYYY-MM-DD' format if found and valid. Handles missing years by assuming current/next year.
 * @param text The text to search for a date
 * @returns The extracted date string, or null if not found
 */
export function extractDueDateFromString(text: string): string | null {
  if (!text) return null;

  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth(); // 0-indexed

  // Regex patterns for various date formats
  // Order matters: more specific or common formats first
  const datePatterns = [
    // MM/DD/YYYY or MM-DD-YYYY
    { regex: /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/, formatStr: 'MM/dd/yyyy', hasYear: true },
    // DD Month YYYY (e.g., 25 Jan 2024)
    { regex: /(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{4})/i, formatStr: 'dd MMM yyyy', hasYear: true },
    // Month DD, YYYY (e.g., Jan 25, 2024)
    { regex: /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{1,2}),?\s+(\d{4})/i, formatStr: 'MMM dd, yyyy', hasYear: true },
    // YYYY-MM-DD (ISO format)
    { regex: /(\d{4})\-(\d{2})\-(\d{2})/, formatStr: 'yyyy-MM-dd', hasYear: true },
    // MM/DD or MM-DD (assume current/next year)
    { regex: /(?:due(?: on)?\s+)?(\d{1,2})[\/\-](\d{1,2})(?!\d)/, formatStr: 'MM/dd', hasYear: false },
    // DD Month (e.g., 25 Jan) (assume current/next year)
    { regex: /(?:due(?: on)?\s+)?(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*/i, formatStr: 'dd MMM', hasYear: false },
    // Month DD (e.g., Jan 25) (assume current/next year)
    { regex: /(?:due(?: on)?\s+)?(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{1,2})(?!,?\s*\d{4})/i, formatStr: 'MMM dd', hasYear: false },
  ];


  for (const { regex, formatStr, hasYear } of datePatterns) {
    const match = text.match(regex);
    if (match) {
      let dateStrToParse = match[0];
      let yearToUse = currentYear;
      let effectiveFormatStr = formatStr; // Use a mutable variable for the format string

      // If year is missing, determine if it should be current or next year
      if (!hasYear) {
        // Reconstruct date string with current year for parsing
        // Need to handle different formatStr structures carefully
        let tempDateStr = '';
        if (formatStr === 'MM/dd') tempDateStr = `${match[1]}/${match[2]}/${currentYear}`;
        else if (formatStr === 'dd MMM') tempDateStr = `${match[1]} ${match[2]} ${currentYear}`;
        else if (formatStr === 'MMM dd') tempDateStr = `${match[1]} ${match[2]}, ${currentYear}`;
        else continue; // Should not happen with current patterns

        // Use a specific format for parsing with year added
        const parseFormat = formatStr === 'MM/dd' ? 'MM/dd/yyyy' :
          formatStr === 'dd MMM' ? 'dd MMM yyyy' :
            formatStr === 'MMM dd' ? 'MMM dd, yyyy' : '';

        if (!parseFormat) continue;

        const parsedWithCurrentYear = parse(tempDateStr, parseFormat, new Date());

        if (isValid(parsedWithCurrentYear)) {
          // If the parsed date (with current year) is in the past relative to today's month/day, assume next year
          // Exception: Allow dates within the current month even if slightly past day
          const parsedMonth = parsedWithCurrentYear.getMonth();
          if (parsedMonth < currentMonth || (parsedMonth === currentMonth && parsedWithCurrentYear.getDate() < new Date().getDate())) {
            // Check if it's significantly in the past (e.g., more than a month ago) to avoid edge cases near year end
            if (differenceInDays(new Date(), parsedWithCurrentYear) > 30) {
              yearToUse = currentYear + 1;
              tempDateStr = tempDateStr.replace(String(currentYear), String(yearToUse)); // Update year in string
            }
          }
          dateStrToParse = tempDateStr; // Use the string with the determined year
          effectiveFormatStr = parseFormat; // Use the format that includes the year
        } else {
          continue; // Skip if parsing fails
        }
      }

      // Use effectiveFormatStr for parsing
      const parsedDate = parse(dateStrToParse, effectiveFormatStr, new Date());

      if (isValid(parsedDate)) {
        // Basic validation: Ensure year is reasonable
        const year = parsedDate.getFullYear();
        if (year >= currentYear - 1 && year <= currentYear + 10) { // Allow previous year for flexibility
          return format(parsedDate, 'yyyy-MM-dd');
        }
      }
    }
  }

  // Removed the old fallback as the main loop now handles MM/DD format

  return null;
}

// Updated suggestTags function
export function suggestTags(
  billName: string | undefined,
  description: string | undefined,
  category: string | undefined,
  isRecurring: boolean | undefined,
  userRules: UserRule[] = []
): string[] {
  const tags = new Set<string>();
  const nameLower = billName?.toLowerCase() || '';
  const descriptionLower = description?.toLowerCase() || '';

  // --- START: Process User Rules for Tags ---
  const tagRules = userRules.filter(rule => rule.action.type === 'addTag');

  for (const rule of tagRules) {
    const conditionValueLower = rule.condition.value.toLowerCase();
    let textToCheck = '';
    if (rule.condition.field === 'name') {
      textToCheck = nameLower;
    } else if (rule.condition.field === 'description') {
      textToCheck = descriptionLower;
    }

    if (textToCheck) {
      let match = false;
      switch (rule.condition.operator) {
        case 'contains':
          match = textToCheck.includes(conditionValueLower);
          break;
        case 'equals':
          match = textToCheck === conditionValueLower;
          break;
        case 'startsWith':
          match = textToCheck.startsWith(conditionValueLower);
          break;
        case 'endsWith':
          match = textToCheck.endsWith(conditionValueLower);
          break;
      }
      if (match) {
        tags.add(rule.action.value);
      }
    }
  }
  // --- END: Process User Rules for Tags ---

  // --- START: Default Tagging Logic ---
  // Rule 1: Recurring tag (based on isRecurring input)
  if (isRecurring) {
    tags.add('recurring');
  }

  // Rule 2: Category-based tags
  switch (category?.toLowerCase()) {
    case 'housing':
    case 'utilities':
    case 'insurance':
    case 'healthcare':
    case 'internet & phone':
      tags.add('essential');
      break;
    case 'debt':
      tags.add('debt');
      if (nameLower.includes('credit card')) tags.add('credit-card');
      if (nameLower.includes('student loan')) tags.add('student-loan');
      break;
    case 'entertainment':
    case 'subscriptions':
      tags.add('discretionary');
      if (isRecurring === true) tags.add('recurring'); // Only if explicitly true
      break;
    case 'food':
      if (nameLower.includes('grocery') || nameLower.includes('groceries')) {
        tags.add('groceries');
        tags.add('essential');
      } else {
        tags.add('dining');
        tags.add('discretionary');
      }
      break;
    // Add more category rules as needed
  }

  // Rule 3: Keyword-based tags from name
  if (nameLower.includes('netflix') || nameLower.includes('spotify') || nameLower.includes('hulu')) {
    tags.add('streaming');
    tags.add('subscription');
    if (isRecurring === true) tags.add('recurring'); // Only if explicitly true
  }
  if (nameLower.includes('amazon prime')) {
    tags.add('subscription');
    if (isRecurring === true) tags.add('recurring'); // Only if explicitly true
  }
  // Add more keyword rules as needed

  return Array.from(tags);
}

// AI-powered bill risk assessment
// Returns a score from 0-100 indicating payment priority
/**
 * AI-powered bill risk assessment.
 * Returns a score from 0-100 indicating payment priority based on due date, amount, category, and payment history.
 * @param bill The bill object to assess
 * @returns Risk score (0-100)
 */
export function assessBillRisk(
  bill: {
    name: string;
    dueDate: string;
    amount: number;
    category?: string;
    isRecurring?: boolean;
    paidDate?: string;
    reminderDays?: number;
    paymentHistory?: Array<{ date: string; onTime: boolean }>;
  }
): number {
  if (bill.paidDate) return 0; // Already paid bills have no risk

  const now = new Date();
  // Use consistent and robust date parsing
  const dueDate = parseLocalDateString(bill.dueDate); 

  if (!dueDate || isNaN(dueDate.getTime())) {
    // console.warn(`assessBillRisk: Invalid due date for bill '${bill.name}'. Returning 0 risk.`);
    return 0; // Invalid due date means cannot assess time-based risk accurately
  }

  // Base factors
  let riskScore = 0;

  // Factor 1: Time remaining (40% of score)
  // Ensure 'now' is also normalized to start of day for consistent 'differenceInDays' if dueDate is start of day
  const todayNormalized = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const daysRemaining = differenceInDays(dueDate, todayNormalized);

  if (daysRemaining < 0) {
    // Overdue bills get maximum time priority
    riskScore += 40;
  } else if (daysRemaining === 0) {
    // Due today
    riskScore += 38;
  } else if (daysRemaining <= 3) {
    // Due very soon
    riskScore += 35;
  } else if (daysRemaining <= 7) {
    // Due this week
    riskScore += 30;
  } else if (daysRemaining <= 14) {
    // Due within two weeks
    riskScore += 20;
  } else {
    // Due later
    riskScore += Math.max(0, 15 - Math.floor(daysRemaining / 7));
  }

  // Factor 2: Bill amount (30% of score)
  // Higher amounts get higher priority
  const amountFactor = Math.min(30, Math.floor(bill.amount / 50) * 3);
  riskScore += amountFactor;

  // Factor 3: Bill category importance (20% of score)
  const essentialCategories = ['Housing', 'Utilities', 'Insurance', 'Healthcare', 'Transportation'];
  const importantCategories = ['Debt', 'Credit Cards', 'Loans', 'Food'];

  if (bill.category && essentialCategories.includes(bill.category)) {
    riskScore += 20;
  } else if (bill.category && importantCategories.includes(bill.category)) {
    riskScore += 15;
  } else {
    riskScore += 5;
  }

  // Factor 4: Payment history (10% of score)
  // If there's a payment history, consider late payments as higher risk
  if (bill.paymentHistory && bill.paymentHistory.length > 0) {
    const latePayments = bill.paymentHistory.filter(p => !p.onTime).length;
    const lateRatio = latePayments / bill.paymentHistory.length;
    riskScore += Math.floor(lateRatio * 10);
  } else {
    // Without history, assume medium risk
    riskScore += 5;
  }

  return Math.min(100, riskScore);
}

// Define a minimal PaymentHistoryEntry type matching financialIntelligence
interface PaymentHistoryEntry {
  date: string;
  amount: number; // Amount might not be needed here, but keep structure consistent
}

// Get next predicted bill date based on history
/**
 * Predicts the next bill due date based on current due date and historical payment dates.
 * Uses detected frequency (weekly, monthly, etc.) if history is available,
 * otherwise falls back to adding one month.
 * @param currentDueDate The most recent due date (YYYY-MM-DD)
 * @param paymentHistory Optional array of payment history entries { date: string, amount: number }
 * @returns Predicted next due date as a string (YYYY-MM-DD), or null if prediction fails
 */
export function predictNextBillDate(
  currentDueDate: string,
  paymentHistory?: PaymentHistoryEntry[]
): string | null {
  const lastDueDate = parseLocalDateString(currentDueDate);
  if (!lastDueDate) {
    console.error('Invalid currentDueDate provided to predictNextBillDate:', currentDueDate);
    return null;
  }

  // If history is available, try to detect frequency
  if (paymentHistory && paymentHistory.length >= 2) {
    try {
      // Ensure history has valid dates for frequency detection
      const validHistory = paymentHistory.filter(h => parseLocalDateString(h.date));
      if (validHistory.length < 2) {
        throw new Error("Not enough valid dates in history for frequency detection.");
      }

      const frequency = detectBillFrequency(validHistory);
      let nextDate: Date | null = null;

      switch (frequency) {
        case 'weekly':
          nextDate = addWeeks(lastDueDate, 1);
          break;
        case 'bi-weekly':
          nextDate = addWeeks(lastDueDate, 2);
          break;
        case 'monthly':
          nextDate = addMonths(lastDueDate, 1);
          break;
        case 'quarterly':
          nextDate = addQuarters(lastDueDate, 1);
          break;
        case 'annual':
          nextDate = addYears(lastDueDate, 1);
          break;
        case 'irregular':
        default:
          // Fallback for irregular or if detection fails
          console.warn(`Irregular frequency detected for bill based on due date ${currentDueDate}. Falling back to monthly.`);
          nextDate = addMonths(lastDueDate, 1);
          break;
      }
      
      if (!nextDate || isNaN(nextDate.getTime())) {
        // This should ideally not happen if lastDueDate is valid and addX functions work as expected
        console.error('predictNextBillDate: nextDate became invalid after frequency calculation.');
        throw new Error('nextDate calculation resulted in an invalid date.');
      }
      return format(nextDate, 'yyyy-MM-dd');

    } catch (error) {
      console.error('Error during frequency-based date prediction:', error);
      // Fallback to simple monthly prediction on error
      try {
        return format(addMonths(lastDueDate, 1), 'yyyy-MM-dd');
      } catch (fallbackError) {
        console.error('Error in fallback date prediction:', fallbackError);
        return null;
      }
    }
  }

  // Simple fallback: if no sufficient history, just add one month
  try {
    return format(addMonths(lastDueDate, 1), 'yyyy-MM-dd');
  } catch (error) {
    console.error('Error predicting next bill date (simple fallback):', error);
    return null;
  }
}

// Get payment urgency status with smart contextual information
export type UrgencyLevel = 'critical' | 'high' | 'medium' | 'low' | 'paid';
export interface BillUrgencyInfo {
  level: UrgencyLevel;
  score: number;
  message: string;
  color: string;
  action?: string;
  riskLevel?: 'high' | 'medium' | 'low';
}

/**
 * Returns a payment urgency status for a bill, with contextual info and color coding.
 * Considers due date, available balance, and upcoming income.
 * @param bill The bill object
 * @param financialContext Optional financial context (balance, income)
 * @returns BillUrgencyInfo object with level, score, message, and color
 */
export function getBillUrgency(
  bill: {
    name: string;
    dueDate: string;
    amount: number;
    category?: string;
    paidDate?: string;
  },
  financialContext?: {
    availableBalance?: number; // Can be provided directly or derived from summary.balance
    balance?: number; // Alternative property name
    upcomingIncome?: { date: string, amount: number }[];
  }
): BillUrgencyInfo {
  if (bill.paidDate) {
    return {
      level: 'paid',
      score: 0,
      message: 'Paid',
      color: '#10b981' // Green
    };
  }

  const riskScore = assessBillRisk(bill);
  const dueDate = parseLocalDateString(bill.dueDate);
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Normalize today to start of day for accurate diff

  if (!dueDate) {
    // Handle invalid due date - perhaps return a default low urgency or error state
    return {
      level: 'low',
      score: 0,
      message: 'Invalid due date',
      color: '#6b7280' // Gray
    };
  }

  const daysRemaining = differenceInDays(dueDate, today);

  // Check if we have balance info and if bill amount exceeds available balance
  let insufficientFundsNow = false;
  // Use availableBalance if provided, otherwise fall back to balance property
  const availableBalance = financialContext?.availableBalance ?? financialContext?.balance;
  if (availableBalance !== undefined) {
    insufficientFundsNow = bill.amount > availableBalance;
  }

  // Check if income is expected before the due date
  let incomeBeforeDue = false;
  let nextIncomeDate: Date | null = null;
  if (financialContext?.upcomingIncome) {
    const relevantIncome = financialContext.upcomingIncome
      .map(income => ({ ...income, dateObj: parseLocalDateString(income.date) }))
      .filter(income => income.dateObj && differenceInDays(income.dateObj, today) >= 0 && differenceInDays(dueDate, income.dateObj) >= 0)
      .sort((a, b) => a.dateObj!.getTime() - b.dateObj!.getTime());

    if (relevantIncome.length > 0) {
      incomeBeforeDue = true;
      nextIncomeDate = relevantIncome[0].dateObj ?? null;
    }
  }

  // Critical - Overdue OR due today/tomorrow with insufficient funds NOW and NO income expected before due
  if (daysRemaining < 0 || (daysRemaining <= 1 && insufficientFundsNow && !incomeBeforeDue)) {
    let message = daysRemaining < 0
      ? `Overdue by ${Math.abs(daysRemaining)} day${Math.abs(daysRemaining) !== 1 ? 's' : ''}`
      : `Due ${daysRemaining === 0 ? 'today' : 'tomorrow'}`; // Adjusted for <= 1

    if (insufficientFundsNow && !incomeBeforeDue && daysRemaining >= 0) {
      message += ' - Funds low, no income expected before due.';
    }
    return {
      level: 'critical',
      score: riskScore,
      message: message,
      color: '#ef4444', // Red
      action: 'Pay ASAP'
    };
  }

  // High - Due within 3 days OR insufficient funds now but income expected
  if (daysRemaining <= 3 || (insufficientFundsNow && incomeBeforeDue)) {
    let message = `Due in ${daysRemaining} day${daysRemaining !== 1 ? 's' : ''}`;
    if (insufficientFundsNow && incomeBeforeDue && nextIncomeDate) {
      message += ` - Funds low, income expected ${format(nextIncomeDate, 'MMM d')}.`;
    } else if (insufficientFundsNow) {
      // This case should be less common now due to the critical check above
      message += ' - Funds low.';
    }

    return {
      level: 'high',
      score: riskScore,
      message: message,
      color: '#f59e0b', // Amber
      action: 'Prepare payment'
    };
  }

  // Medium - Due within a week (4-7 days)
  if (daysRemaining <= 7) {
    return {
      level: 'medium',
      score: riskScore,
      message: `Due in ${daysRemaining} days`,
      color: '#3b82f6', // Blue
      action: 'Schedule payment'
    };
  }

  // Low - Due later (more than 7 days)
  return {
    level: 'low',
    score: riskScore,
    message: `Due in ${daysRemaining} days`,
    color: '#6b7280', // Gray
    action: 'Budget for later'
  };
}
