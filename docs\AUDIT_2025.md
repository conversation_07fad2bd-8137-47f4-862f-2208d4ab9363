# PayDay Pilot – 2025 Website/PWA Audit

Last updated: 2025-08-09

## Executive summary

Overall foundation is strong: Next.js App Router, service workers (Workbox + custom), Netlify functions, Firebase, and PWA manifest present. Main gaps: redundant SW registration, CSP too permissive, manifest fields incomplete for 2025, potential double SWs, notification permission UX, image pipeline, test coverage, and observability.

## Priority checklist (P0 first)

- P0 Security
  - Tighten Content-Security-Policy; remove 'unsafe-eval' and restrict script/style sources; add connect-src entries for Firebase, OneSignal, Netlify, and analytics actually used.
  - Add Permissions-Policy header to limit powerful APIs.
  - Ensure SameSite/Lax cookies and secure flags for any custom cookies (if applicable).
- P0 PWA correctness
  - Avoid dual service workers (next-pwa sw.js + custom-sw.js + on-demand register in PwaIcons and in notificationService). Standardize to a single controller strategy.
  - Ensure FCM/OneSignal works under the chosen SW and aligns with push event handlers.
- P0 Performance
  - Enable image AVIF (done in this PR); adopt next/image widely; audit large bundles and enable code-splitting where needed.
  - Confirm production sourcemaps disabled (already off) and analyze bundle when ANALYZE=true.
- P1 PWA enhancements
  - <PERSON>ifest updated in this PR with id, display_override, lang/dir, protocol_handlers, shortcuts, screenshots; add proper iOS splash if needed.
  - Ensure offline fallback route and 404 handling are covered (offline.html exists; ensure linked from custom SW if that is the active one).
- P1 Accessibility
  - Run automated checks (axe) and fix color contrast; ensure skip links and focus states.
- P1 SEO/Meta
  - Add robots.txt and sitemap.xml; configure Open Graph/Twitter meta; verify canonical URLs.
- P2 Observability
  - Add Web Vitals reporting; add error monitoring (Sentry or similar) with DSN via env.
- P2 DX & Testing
  - Add Playwright for e2e (install, basic PWA install flow test); fill unit tests for stores and util functions.
- P3 Hosting
  - Review Netlify publish dir (.next) vs Next on Netlify plugin expectations; consider output: 'standalone' if moving to edge or SSR via Netlify adapter. Verify scheduled functions timezones.

## Findings by area

### PWA
- Manifest was missing id, display_override, lang/dir, screenshots; added.
- Multiple SW registration paths:
  - public/register-sw.js tries sw.js then falls back to custom-sw.js.
  - src/services/notificationService.ts registers '/custom-sw.js' directly.
  - PwaIcons dynamically injects register-sw.js if none.
  This can cause races or unexpected controllers. Action: consolidate to a single registration entry point (recommend register-sw.js only), and remove direct registration from notificationService.
- custom-sw.js contains caching strategies, offline, and background sync. Ensure there is no overlap with Workbox precache in sw.js to avoid cache bloat.

### Security headers
- _headers includes CSP with 'unsafe-inline' and 'unsafe-eval'. For 2025, use hashed/nonce scripts; avoid eval. Expand connect-src for firebase, onesignal, googleapis, gstatic, netlify, and any endpoints used. Add Permissions-Policy and COOP/COEP if using SharedArrayBuffer.

### Performance
- next.config.js: added AVIF; consider image remotePatterns for Firebase Storage if used. Use next/script with strategy and CSP nonce.
- Consider critters already present; ensure it’s only used with SSR HTML and not causing FOUC. Tailwind JIT is fine.
- Audit bundles with ANALYZE=true and split large vendors (recharts, react-big-calendar, firebase SDK). Consider modular Firebase imports (already optimizedPackageImports configured).

### Accessibility
- App uses multiple fonts; ensure sufficient contrast in dark mode. Verify semantic headings and aria labels. Add focus-visible styles.

### SEO
- Add structured data (Organization / WebApplication). Ensure metadata in app router includes alternates and openGraph/twitter.

### Hosting / Netlify
- Netlify publish points to .next; with @netlify/plugin-nextjs this is valid but verify SSR/ISR routing is configured. Ensure function bundling uses node_bundler=esbuild as set.

## Recommended concrete changes

1) Consolidate SW registration
- Remove direct register('/custom-sw.js') from src/services/notificationService.ts; rely on public/register-sw.js injection via PwaIcons or load once in app layout head.
- Decide primary SW: If Workbox sw.js is stable on Netlify, prefer it and integrate custom features via workbox plugins or importScripts. Otherwise, ship only custom-sw.js.

2) Harden CSP in public/_headers
- Replace script-src/style-src with nonces/hashes; add strict-dynamic if using nonces. Expand connect-src to include: https://firebase.googleapis.com https://www.googleapis.com https://fcm.googleapis.com https://apis.google.com https://cdn.onesignal.com https://*.onesignal.com https://www.googletagmanager.com https://www.google-analytics.com and self.
- Add:
  Permissions-Policy: geolocation=(), camera=(), microphone=(), notifications=(self), push=(self), payment=(), fullscreen=(self)

3) Metadata/SEO
- Add robots.txt and sitemap.xml under public/.
- Expand app router Metadata with openGraph/twitter fields and canonical.

4) Observability
- Add Next.js web-vitals report hook and a minimal endpoint to receive metrics (or use Google Analytics gtag event).

5) Testing
- Add Playwright with a basic PWA installability test (manifest OK, SW registered, offline works).

## How to verify
- Lighthouse (PWA + Best Practices + SEO + Accessibility) on production build.
- Check SW controller in Application panel; only one active.
- Test notifications and background sync after offline changes.

## Appendix: Quick tasks
- [ ] Remove duplicate SW registration calls.
- [ ] Update _headers CSP and add Permissions-Policy.
- [ ] Add robots.txt and sitemap.xml.
- [ ] Add web-vitals hook.
- [ ] Add Playwright basic tests.
