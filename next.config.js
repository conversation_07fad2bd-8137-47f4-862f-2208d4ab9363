// Import optimization utilities
const {
  optimizeModuleResolution,
  optimizeForDevelopment,
  optimizeForProduction,
  addCaching,
  externalizeServerDependencies,
} = require('./webpack.optimization');

// Development environment check
const isDev = process.env.NODE_ENV !== 'production';

// Optimized PWA configuration - only load in production
const withPWA = !isDev
  ? require('next-pwa')({
    dest: 'public',
    register: true,
    skipWaiting: true,
    // Use a single, custom service worker instead of next-pwa generated one
    disable: true,
    buildExcludes: [/middleware-manifest\.json$/, /app-build-manifest\.json$/],
    runtimeCaching: [
      {
        urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
        handler: 'CacheFirst',
        options: {
          cacheName: 'google-fonts-cache',
          expiration: {
            maxEntries: 10,
            maxAgeSeconds: 60 * 60 * 24 * 365, // 365 days
          },
          cacheableResponse: {
            statuses: [0, 200],
          },
        },
      },
    ],
  })
  : (config) => config; // No-op in development

// Bundle analyzer (only when explicitly enabled)
const withBundleAnalyzer = process.env.ANALYZE === 'true'
  ? require('@next/bundle-analyzer')({
    enabled: true,
    openAnalyzer: false // Don't auto-open browser
  })
  : (config) => config;

// Development mode fast refresh time
const fastRefreshTimeout = 500; // milliseconds

/** @type {import('next').NextConfig} */

const nextConfig = {
  reactStrictMode: isDev ? false : true, // Disable strict mode in dev for speed
  // Environment variables
  env: {
    NEXT_PUBLIC_APP_VERSION: require('./package.json').version,
  },
  // Completely disable TypeScript checking in development for maximum speed
  typescript: {
    ignoreBuildErrors: true, // Always ignore for speed
    tsconfigPath: './tsconfig.json',
  },
  // Completely disable ESLint in development
  eslint: {
    ignoreDuringBuilds: true, // Always ignore for maximum speed
    dirs: [], // Empty dirs array to skip ESLint completely in dev
  },
  // Image optimization
  images: {
    // Prefer modern formats first for better compression
    formats: !isDev ? ['image/avif', 'image/webp'] : ['image/webp'],
    minimumCacheTTL: 60,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'www.gstatic.com',
      },
    ],
  },
  // Development optimizations
  experimental: {
    serverComponentsExternalPackages: process.env.NODE_ENV === 'production' ? ['firebase-functions', 'firebase-admin', 'googleapis'] : undefined,
    // Optimize package imports only in production to reduce dev transform overhead
    optimizePackageImports: process.env.NODE_ENV === 'production' ? [
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'firebase/messaging',
      'firebase/analytics',
      '@headlessui/react',
      '@heroicons/react',
      'date-fns',
      'framer-motion',
      'recharts',
      'react-big-calendar',
      'zustand',
    ] : undefined,
    // Enable CSS optimization only in production
    optimizeCss: process.env.NODE_ENV === 'production',
    // SWC optimizations for faster development (only in production to avoid Turbopack conflicts)
    swcMinify: process.env.NODE_ENV === 'production',
    // Turbopack config (no custom webpack loaders in dev to keep turbopack enabled)
    turbo: process.env.NODE_ENV === 'production' ? {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    } : {},
  },

  // Move these out of experimental as they're now top-level options
  skipTrailingSlashRedirect: isDev,
  skipMiddlewareUrlNormalize: isDev,
  // Rewrites to avoid missing asset errors in dev
  async rewrites() {
    return [
      { source: '/favicon.ico', destination: '/icons/icon-512x512.svg' },
      { source: '/images/splash-icon.png', destination: '/images/logo.png' },
    ];
  },

  // Webpack optimizations (disabled in development to allow Turbopack)
  webpack: process.env.NODE_ENV === 'production' ? (config, { dev, isServer }) => {
    // Add fallbacks for Node.js modules
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      dns: false,
      child_process: false,
      crypto: false,
      stream: false,
      util: false,
      url: false,
      zlib: false,
      http: false,
      https: false,
      assert: false,
      os: false,
      path: false,
    };

    // Apply optimization utilities
    config = optimizeModuleResolution(config);
    config = addCaching(config);
    config = externalizeServerDependencies(config, isServer);

    // Apply environment-specific optimizations
    if (dev) {
      config = optimizeForDevelopment(config);
    } else {
      config = optimizeForProduction(config);
    }

    return config;
  } : undefined,
  // Source maps configuration
  productionBrowserSourceMaps: false,
  // Enable HTTP/2 server push
  httpAgentOptions: {
    keepAlive: true,
  },
  // Security and caching headers for all routes (applies in Next dev/prod server)
  async headers() {
    return [
      {
        // Global security headers
        source: '/:path*',
        headers: [
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'Referrer-Policy', value: 'strict-origin-when-cross-origin' },
          // Note: HSTS only effective over HTTPS; safe to include
          { key: 'Strict-Transport-Security', value: 'max-age=63072000; includeSubDomains; preload' },
          { key: 'X-Frame-Options', value: 'DENY' },
        ],
      },
      {
        // Long-term caching for Next static assets
        source: '/_next/static/:path*',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        ],
      },
      {
        // Long-term caching for Next image optimizer responses
        source: '/_next/image',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        ],
      },
      {
        // Aggressive caching for app icons
        source: '/icons/:path*',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        ],
      },
      {
        // Aggressive caching for optimized images
        source: '/images/optimized/:path*',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        ],
      },
    ];
  },
};

// Only enable bundle analyzer when explicitly requested
if (process.env.ANALYZE === 'true') {
  const withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: true,
  });
  module.exports = withBundleAnalyzer(withPWA(nextConfig));
} else {
  module.exports = withPWA(nextConfig);
}
