// Custom Next.js cache handler for improved performance
// This file implements a more aggressive caching strategy for development

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Cache configuration
const CACHE_DIR = path.join(process.cwd(), 'node_modules/.cache/next-cache');
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Create cache directory if it doesn't exist
if (!fs.existsSync(CACHE_DIR)) {
    fs.mkdirSync(CACHE_DIR, { recursive: true });
}

// Simple hash function for keys
function hashKey(key) {
    return crypto.createHash('md5').update(key).digest('hex');
}

// Cache handler implementation
module.exports = {
    // Get a value from the cache
    get: async (key) => {
        try {
            const hashedKey = hashKey(key);
            const cacheFile = path.join(CACHE_DIR, `${hashedKey}.json`);

            if (fs.existsSync(cacheFile)) {
                const cacheData = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));

                // Check if cache is still valid
                if (Date.now() - cacheData.timestamp < CACHE_TTL) {
                    return cacheData.value;
                }
            }
        } catch (error) {
            console.warn('Cache read error:', error.message);
        }

        return null;
    },

    // Set a value in the cache
    set: async (key, value) => {
        try {
            const hashedKey = hashKey(key);
            const cacheFile = path.join(CACHE_DIR, `${hashedKey}.json`);

            const cacheData = {
                timestamp: Date.now(),
                value,
            };

            fs.writeFileSync(cacheFile, JSON.stringify(cacheData));
            return true;
        } catch (error) {
            console.warn('Cache write error:', error.message);
            return false;
        }
    },
};
