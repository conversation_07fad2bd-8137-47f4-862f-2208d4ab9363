# Product Context for PayDay Pilot

## Overview

PayDay Pilot is a Next.js application designed to help users manage their bills and finances.

## Core Components/Features

*   Dashboard
*   Bill Management (Viewing, Adding, Importing)
*   Upcoming Bills Tracking
*   Firebase Integration (Authentication, Firestore)
*   Netlify Deployment

## Project Organization

*   Framework: Next.js
*   Styling: Tailwind CSS
*   State Management: (Likely React Context/State, needs confirmation)
*   Backend: Firebase (Auth, Firestore), Netlify Functions

## Coding Standards & Conventions

Refer to `systemPatterns.md` and global `.windsurfrules`.

*Note: This file should be updated when the high-level project scope, goals, or architecture changes significantly.*
