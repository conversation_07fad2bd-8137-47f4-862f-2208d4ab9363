import type { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HandlerContext } from '@netlify/functions';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import './utils/firebase-admin'; // Correct import for initialization side effect
import crypto from 'crypto';

// Expected request body structure
interface SubscribeRequestBody {
  subscription: PushSubscriptionJSON;
}

// Helper function to create a hash from the endpoint
function createEndpointHash(endpoint: string): string {
  return crypto.createHash('sha256').update(endpoint).digest('hex');
}

const handler: Handler = async (event: HandlerEvent, context: HandlerContext) => {
  // 1. Check if it's a POST request
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: { Allow: 'POST' },
      body: JSON.stringify({ error: `Method ${event.httpMethod} Not Allowed` }),
    };
  }

  try {
    // 2. Get user authentication token from headers
    const authHeader = event.headers.authorization;
    const idToken = authHeader?.split('Bearer ')[1];
    if (!idToken) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: 'Unauthorized: No token provided' }),
      };
    }

    // 3. Verify the token and get the user ID (uid)
    let userId: string;
    try {
      const decodedToken = await getAuth().verifyIdToken(idToken);
      userId = decodedToken.uid;
      if (!userId) throw new Error('No UID in token'); // Should not happen if verifyIdToken succeeds
    } catch (authError: any) {
      console.error('Auth Error:', authError);
      let statusCode = 401;
      let message = 'Unauthorized: Invalid token';
      if (authError.code === 'auth/id-token-expired') {
        message = 'Unauthorized: Token expired';
      }
      return { statusCode, body: JSON.stringify({ error: message }) };
    }

    // 4. Parse the subscription object from the request body
    if (!event.body) {
        return { statusCode: 400, body: JSON.stringify({ error: 'Bad Request: Missing request body' }) };
    }
    const { subscription } = JSON.parse(event.body) as SubscribeRequestBody;
    if (!subscription || !subscription.endpoint) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Bad Request: Missing subscription data' }),
      };
    }

    // 5. Create a unique ID for the subscription document (hash of endpoint)
    const subscriptionId = createEndpointHash(subscription.endpoint);

    // 6. Get Firestore instance
    const db = getFirestore();

    // 7. Define the Firestore path
    const subscriptionRef = db.collection('users').doc(userId)
                             .collection('webPushSubscriptions').doc(subscriptionId);

    // 8. Save the subscription data to Firestore
    //    Store the full subscription object to have keys for sending later.
    await subscriptionRef.set(subscription, { merge: true }); // Use merge to update if exists

    console.log(`Subscription ${subscriptionId} saved for user ${userId}`);
    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'Subscription saved successfully' }),
    };

  } catch (error: any) {
    console.error('Error saving push subscription:', error);
    // Check for JSON parsing errors specifically
    if (error instanceof SyntaxError) {
        return { statusCode: 400, body: JSON.stringify({ error: 'Bad Request: Invalid JSON in body' }) };
    }
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal Server Error' }),
    };
  }
};

export { handler };
