// src/components/calendar/AddBillModal.tsx
import React from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button'; 

interface AddBillModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date | null;
}

function AddBillModal({ isOpen, onClose, selectedDate }: AddBillModalProps) {
  const router = useRouter();
  const [date, setDate] = React.useState<Date | null>(selectedDate);

  React.useEffect(() => {
    setDate(selectedDate);
  }, [selectedDate]);

  if (!isOpen) {
    return null;
  }

  const formattedDate = date ? format(date, 'yyyy-MM-dd') : '';
  const displayDate = date ? format(date, 'MMMM d, yyyy') : '';

  function handleAddBill() {
    if (!date) return;
    router.push(`/bills/new?date=${formattedDate}`);
    onClose();
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm transition-opacity duration-300 ease-in-out">
      <div className="relative w-full max-w-md transform rounded-lg bg-white dark:bg-gray-800 p-6 shadow-xl transition-all duration-300 ease-in-out scale-100">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none"
          aria-label="Close modal"
        >
          <XMarkIcon className="h-6 w-6" />
        </button>

        <h3 className="text-lg font-semibold leading-6 text-gray-900 dark:text-white mb-4">
          Add Bill
        </h3>

        {!date && (
          <div className="mb-6">
            <label htmlFor="add-bill-date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Choose a date:</label>
            <input
              id="add-bill-date"
              type="date"
              className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
              onChange={e => setDate(e.target.value ? new Date(e.target.value + 'T00:00:00') : null)}
              autoFocus
            />
          </div>
        )}
        {date && (
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-6">
            Do you want to add a new bill for{' '}
            <span className="font-medium text-primary">{displayDate}</span>?
          </p>
        )}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleAddBill} disabled={!date}>
            Add Bill
          </Button>
        </div>
      </div>
    </div>
  );
}


export default AddBillModal;
