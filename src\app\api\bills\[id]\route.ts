// Local-only mode: Bills API disabled. Use client-side store instead.
import { NextResponse } from 'next/server';

export function GET() {
  return NextResponse.json(
    { message: 'Bills API disabled in local-only mode' },
    { status: 404 }
  );
}

export function PUT() {
  return NextResponse.json(
    { message: 'Bills API disabled in local-only mode' },
    { status: 404 }
  );
}

export function DELETE() {
  return NextResponse.json(
    { message: 'Bills API disabled in local-only mode' },
    { status: 404 }
  );
}
