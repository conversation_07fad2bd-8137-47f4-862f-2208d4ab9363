'use client';

import { useAuth } from '@/hooks/useAuth';
import useGoogleCalendarSync from '@/hooks/useGoogleCalendarSync';
import { Bill } from '@/types/bill';
import { CalendarIcon } from '@heroicons/react/24/outline';
// import { useRouter, useSearchParams } from 'next/navigation'; // No longer needed here
import { useEffect, useState } from 'react';

interface GoogleCalendarSyncProps {
  bills: Bill[];
  className?: string;
}

export default function GoogleCalendarSync({ bills, className = '' }: GoogleCalendarSyncProps) {
  const { user: authUser, loading: authLoading } = useAuth(); // Get user and auth loading state
  const user = authUser === undefined ? null : authUser;

  // Call hooks unconditionally at the top level
  const {
    syncSettings,
    isLoading: isSyncLoading,
    syncStatus,
    error: syncError,
    connectGoogleCalendar,
    syncBillsToCalendar,
    fetchCalendarSettings
  } = useGoogleCalendarSync(user);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<string | null>(null);

  // Update the last sync time display when settings change
  useEffect(() => {
    if (!authLoading && syncSettings.lastSyncedAt) { // Ensure not authLoading
      const syncDate = new Date(syncSettings.lastSyncedAt);
      setLastSyncTime(syncDate.toLocaleString());
    }
  }, [authLoading, syncSettings.lastSyncedAt]);
  
  // Check if settings need to be refreshed when component mounts or user changes
  // Only fetch if auth is not loading and user is available
  // const searchParams = useSearchParams(); // No longer needed
  // const router = useRouter(); // No longer needed

  // Log component rendering
  console.log('[GoogleCalendarSync] Rendering. AuthLoading:', authLoading, 'User available:', !!user);
  
  useEffect(() => {
    console.log('[GoogleCalendarSync] Auth/User useEffect triggered. AuthLoading:', authLoading, 'User available:', !!user);
    if (!authLoading && user) {
      console.log('[GoogleCalendarSync] Auth resolved and user available. UID:', user.uid, 'Calling fetchCalendarSettings.');
      fetchCalendarSettings();
    } else if (!authLoading && !user) {
      console.log('[GoogleCalendarSync] Auth resolved but no user.');
    } else {
      console.log('[GoogleCalendarSync] Auth is still loading.');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authLoading, user]); // Dependencies: authLoading, user. fetchCalendarSettings is memoized.

  const handleConnectClick = () => {
    if (authLoading || !user) return; // Prevent action if auth loading or no user
    setShowConfirmationModal(true);
  };

  const handleConfirmConnect = () => {
    if (authLoading || !user) return; // Prevent action if auth loading or no user
    connectGoogleCalendar();
    setShowConfirmationModal(false);
  };

  const handleCancelConnect = () => {
    setShowConfirmationModal(false);
  };
  
  // Auto-sync bills with Google Calendar if enabled
  useEffect(() => {
    if (!authLoading && user && syncSettings.syncEnabled && bills && bills.length > 0) { // Ensure not authLoading and user is available
      // Only sync automatically if it's been more than 1 hour since last sync
      const lastSync = syncSettings.lastSyncedAt ? new Date(syncSettings.lastSyncedAt) : null;
      const shouldSync = !lastSync || (new Date().getTime() - lastSync.getTime() > 3600000);
      
      if (shouldSync) {
        syncBillsToCalendar();
      }
    }
  }, [authLoading, user, bills, syncSettings.syncEnabled, syncSettings.lastSyncedAt, syncBillsToCalendar]);

  // Render loading state or actual component
  if (authLoading) {
    return <div className={className}><p className="text-xs text-dark-sepia/70 dark:text-white/70">Loading calendar settings...</p></div>;
  }

  return (
    <div className={className}>
      {syncSettings.calendarConnected ? (
        <div className="flex flex-col gap-1">
          <button 
            onClick={() => syncBillsToCalendar()}
            disabled={isSyncLoading || authLoading || !user} // Disable if auth loading or no user
            className="flex items-center px-2 py-1.5 text-xs font-medium bg-blueprint-blue/90 text-white rounded hover:bg-blueprint-blue transition-colors"
            title={lastSyncTime ? `Last synced: ${lastSyncTime}` : 'Sync with Google Calendar'}
          >
            <CalendarIcon className="h-3.5 w-3.5 mr-1.5" />
            {isSyncLoading ? 'Syncing...' : 'Sync Google Calendar'}
          </button>
          
          {syncError && (
            <p className="text-muted-red text-xs italic mt-1">
              Error: {syncError}
            </p>
          )}
          
          {lastSyncTime && (
            <p className="text-xs text-dark-sepia/70 dark:text-white/70 mt-0.5">
              Last synced: {lastSyncTime}
            </p>
          )}
        </div>
      ) : (
        <>
          {!showConfirmationModal && (
            <button 
              onClick={handleConnectClick}
              disabled={authLoading || !user} // Disable if auth loading or no user
              className="flex items-center px-2 py-1.5 text-xs font-medium bg-light-gray text-dark-sepia/90 rounded hover:bg-light-gray/80 transition-colors"
            >
              <CalendarIcon className="h-3.5 w-3.5 mr-1.5" />
              Connect Google Calendar
            </button>
          )}

          {showConfirmationModal && !authLoading && user && ( // Only show modal if not auth loading and user exists
            <div className="p-4 border rounded-md bg-white dark:bg-gray-700 shadow-lg mt-2">
              <p className="text-sm text-dark-sepia dark:text-white mb-3">
                Are you sure you want to connect your Google Calendar?
              </p>
              <div className="flex justify-end gap-2">
                <button
                  onClick={handleCancelConnect}
                  className="px-3 py-1 text-xs font-medium text-dark-sepia/90 dark:text-white/90 rounded hover:bg-light-gray/50 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleConfirmConnect}
                  className="px-3 py-1 text-xs font-medium bg-blueprint-blue text-white rounded hover:bg-blueprint-blue/90 transition-colors"
                >
                  Yes, Connect
                </button>
              </div>
            </div>
          )}
        </>
      )}
      
      {/* Status Indicator */}
      {syncSettings.calendarConnected && (
        <div className="flex items-center text-xs pt-2 mt-2 border-t border-dark-sepia/10 dark:border-white/10">
          <span className="text-dark-sepia/70 dark:text-white/70">Google Calendar:</span>
          <span className={`ml-auto font-medium ${syncSettings.syncEnabled ? 'text-green-600 dark:text-green-400' : 'text-dark-sepia/70 dark:text-white/70'}`}>
            {syncSettings.syncEnabled ? 'Synced' : 'Not synced'}
          </span>
        </div>
      )}
    </div>
  );
}
