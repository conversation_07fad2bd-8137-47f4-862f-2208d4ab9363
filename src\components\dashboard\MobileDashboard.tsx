'use client';

import AddBillForm from '@/components/bills/BillForm';
import { BillsOverview } from '@/components/bills/BillsOverview';
import { DashboardCardModal } from '@/components/dashboard/DashboardCardModal';
import { NotificationBadge } from '@/components/ui/Icon';
import { useChangelogNotification } from '@/hooks/useChangelogNotification';
import { useBillsStore } from '@/stores/billsStore';
import { useFinancialStore, useFinancialSummary } from '@/stores/financialStore';
import { Bill, BillFormData } from '@/types/bill';
import { cn } from '@/utils/cn';
import {
  Bars3Icon,
  BellIcon,
  CalendarDaysIcon,
  HomeIcon,
  PlusIcon,
  RectangleStackIcon,
  SparklesIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';
import dynamic from 'next/dynamic';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { createPortal } from 'react-dom';
const BillCalendarApp = dynamic(() => import('@/components/calendar/BillCalendar.new'), { ssr: false });
const NotificationsModalContent = dynamic(() => import('@/components/notifications/NotificationsModal').then(mod => mod.default), { ssr: false });
const SettingsPanel = dynamic(() => import('@/app/settings/page').then(mod => mod.default), { ssr: false });
const AiPage = dynamic(() => import('@/app/ai/page').then(mod => mod.default), { ssr: false });


const walletCardGradients = [
  'from-[#fee2e2] via-[#fbcfe8] to-[#f9a8d4]',
  'from-[#e0e7ff] via-[#c7d2fe] to-[#a5b4fc]',
  'from-[#d1fae5] via-[#a7f3d0] to-[#6ee7b7]'
];

type ModalView = 'addBill' | 'calendar' | 'bills' | 'ai' | 'notifications' | 'settings';

function formatCurrency(value: number, currency: string) {
  return new Intl.NumberFormat(undefined, {
    style: 'currency',
    currency,
    maximumFractionDigits: 2
  }).format(value);
}

function formatDueDate(date: string | undefined | null) {
  if (!date) return 'No date';
  const parsed = new Date(date);
  if (Number.isNaN(parsed.getTime())) return 'No date';
  return parsed.toLocaleDateString(undefined, {
    month: 'short',
    day: 'numeric'
  });
}

function getBillLabel(bill: Bill) {
  if (bill.vendor) return bill.vendor;
  if (bill.category) return bill.category;
  return 'Bill';
}

export function MobileDashboard() {
  // Improved mobile UX - removed problematic viewport restrictions
  // for better accessibility and to eliminate console warnings
  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport instanceof HTMLMetaElement) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
    }
  }, []);

  const router = useRouter();
  const pathname = usePathname();
  const { bills, addBill } = useBillsStore();
  const { transactions, isLoading: isLoadingTransactions } = useFinancialStore();
  const { summary, isLoadingSummary } = useFinancialSummary();
  const changelogNotification = useChangelogNotification();

  const [activeModal, setActiveModal] = useState<ModalView | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isMounted, setIsMounted] = useState(false);


  const currency = 'USD';

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  const upcomingBills = useMemo(() => {
    if (!bills || bills.length === 0) return [] as Bill[];

    return [...bills]
      .filter(bill => !bill.paidDate)
      .sort((a, b) => {
        const aTime = a.dueDate ? new Date(a.dueDate).getTime() : Number.POSITIVE_INFINITY;
        const bTime = b.dueDate ? new Date(b.dueDate).getTime() : Number.POSITIVE_INFINITY;
        return aTime - bTime;
      })
      .slice(0, 3);
  }, [bills]);

  const totalBalance = useMemo(() => {
    if (summary?.balance != null) return summary.balance;

    if (transactions && transactions.length > 0) {
      return transactions.reduce((acc, tx) => acc + (tx.type === 'income' ? tx.amount : -Math.abs(tx.amount)), 0);
    }

    if (bills && bills.length > 0) {
      return bills
        .filter(bill => !bill.paidDate)
        .reduce((acc, bill) => acc + bill.amount, 0);
    }

    return 0;
  }, [summary?.balance, transactions, bills]);

  const recentTransactions = useMemo(() => {
    if (!transactions || transactions.length === 0) return [];

    return [...transactions]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 4);
  }, [transactions]);

  const totalUpcomingDue = useMemo(() => {
    if (!bills || bills.length === 0) return 0;

    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    return bills
      .filter(bill => !bill.paidDate)
      .filter(bill => {
        if (!bill.dueDate) return false;
        const due = new Date(bill.dueDate);
        if (Number.isNaN(due.getTime())) return false;
        return due <= thirtyDaysFromNow;
      })
      .reduce((acc, bill) => acc + bill.amount, 0);
  }, [bills]);

  const handleAddBill = useCallback(async (billData: BillFormData) => {
    setIsSubmitting(true);
    try {
      await addBill(billData);
      setActiveModal(null);
      router.push('/dashboard');
    } catch (error) {
      console.error('Error adding bill from mobile dashboard:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [addBill, router]);

  const closeModal = useCallback(() => setActiveModal(null), []);

  const navItems = useMemo(() => (
    [
      {
        id: 'home' as const,
        label: 'Home',
        icon: HomeIcon,
        href: '/dashboard'
      },
      {
        id: 'calendar' as const,
        label: 'Calendar',
        icon: CalendarDaysIcon,
        href: '/calendar'
      },
      {
        id: 'profile' as const,
        label: 'Profile',
        icon: UserCircleIcon,
        href: '/profile'
      },
      {
        id: 'assistant' as const,
        label: 'AI Assistant',
        icon: SparklesIcon,
        action: () => setActiveModal('ai')
      }
    ]
  ), [setActiveModal]);


  const navMarkup = (
    <nav className="mobile-bottom-nav sm:hidden fixed bottom-0 left-0 right-0">
      <div className="relative">
        <div className="footer-aligned mobile-nav-spacing">
          {navItems.map(item => {
            const isRoute = 'href' in item;
            const isActive = isRoute ? pathname === item.href : activeModal === 'ai';

            const handleClick = () => {
              if (isRoute && item.href) {
                router.push(item.href);
              } else if (!isRoute) {
                item.action();
              }
            };

            return (
              <button
                key={item.id}
                type="button"
                onClick={handleClick}
                className="flex flex-col items-center gap-1 p-2 min-w-[60px] touch-target-enhanced focus-enhanced group"
                aria-label={isRoute ? `Navigate to ${item.label}` : `Open ${item.label}`}
              >
                <div
                  className={cn(
                    'h-8 w-8 rounded-xl flex items-center justify-center transition-all duration-300 ease-out',
                    isActive
                      ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/25'
                      : 'text-gray-500 dark:text-gray-400 group-hover:bg-gray-100 dark:group-hover:bg-gray-800 group-active:scale-95'
                  )}
                >
                  <item.icon className="h-5 w-5" />
                </div>
                <span
                  className={cn(
                    'text-xs font-medium transition-all duration-300',
                    isActive
                      ? 'text-blue-600 dark:text-blue-400 font-semibold'
                      : 'text-gray-500 dark:text-gray-400'
                  )}
                >
                  {item.label}
                </span>
              </button>
            );
          })}
        </div>

        {/* Floating Action Button - Quick add entry point */}
        <button
          type="button"
          onClick={() => setActiveModal('addBill')}
          className="mobile-fab bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 text-white flex items-center justify-center w-14 h-14 rounded-full border-4 border-white dark:border-gray-900 shadow-2xl shadow-blue-500/30 hover:shadow-blue-500/40 hover:-translate-y-1 active:translate-y-0 transition-all duration-200 btn-focus-enhanced"
          aria-label="Add new bill"
        >
          <PlusIcon className="h-6 w-6" />
        </button>
      </div>
    </nav>
  );


  const modalContent = useMemo(() => {
    if (!activeModal) return null;

    switch (activeModal) {
      case 'addBill':
        return (
          <AddBillForm
            onSave={handleAddBill}
            onCancel={closeModal}
            allBills={bills || []}
          />
        );
      case 'calendar':
        return (
          <div className="h-[80vh] sm:h-[75vh] overflow-hidden">
            <BillCalendarApp compact={false} />
          </div>
        );
      case 'bills':
        return (
          <div className="max-h-[80vh] sm:max-h-none overflow-y-auto">
            <BillsOverview bills={bills} />
          </div>
        );
      case 'ai':
        return (
          <div className="h-[75vh] sm:h-[70vh] overflow-hidden flex flex-col">
            <AiPage />
          </div>
        );
      case 'notifications':
        return (
          <div className="max-h-[80vh] sm:max-h-none overflow-y-auto">
            <NotificationsModalContent />
          </div>
        );
      case 'settings':
        return (
          <div className="max-h-[80vh] sm:max-h-none overflow-y-auto">
            <SettingsPanel />
          </div>
        );
      default:
        return null;
    }
  }, [activeModal, bills, handleAddBill, closeModal]);

  const modalTitle = useMemo(() => {
    switch (activeModal) {
      case 'addBill':
        return 'Add Bill';
      case 'calendar':
        return 'Bill Calendar';
      case 'bills':
        return 'Bills Overview';
      case 'ai':
        return 'AI Assistant';
      case 'notifications':
        return 'Notifications';
      case 'settings':
        return 'Settings';
      default:
        return '';
    }
  }, [activeModal]);

  const modalSize = useMemo(() => {
    // On mobile, use appropriate sizes for better UX
    const isMobile = typeof window !== 'undefined' && window.innerWidth < 640;

    if (!isMobile) {
      return activeModal === 'calendar' || activeModal === 'ai' ? 'full' : 'xl';
    }

    // Mobile-optimized modal sizes
    switch (activeModal) {
      case 'calendar':
      case 'ai':
        return 'full';
      case 'addBill':
      case 'notifications':
      case 'settings':
        return 'xl';
      case 'bills':
        return 'lg';
      default:
        return 'lg';
    }
  }, [activeModal]);

  return (
    <>
      <div className="sm:hidden fixed inset-0 z-0 flex flex-col bg-gray-50 dark:bg-gray-900">
        {/* Scrollable Content Area */}
        <main
          className="mobile-dashboard-container flex-1 space-y-6 will-change-transform overflow-y-auto"
          style={{
            WebkitOverflowScrolling: 'touch',
            overscrollBehavior: 'contain',
            paddingBottom: 'calc(112px + env(safe-area-inset-bottom, 0px))',
            paddingTop: 'env(safe-area-inset-top, 0px)'
          }}
        >
          {/* Header Section - Redesigned for better mobile UX */}
          <section className="px-0 pb-2">
            <div className="relative bg-gradient-to-br from-[#1e40af] via-[#3b82f6] to-[#6366f1] text-white rounded-3xl mobile-header-spacing shadow-2xl shadow-blue-500/20 overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-30">
                <div className="absolute inset-0" style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
              </div>

              {/* Header Controls */}
              <div className="relative flex items-center justify-between mb-8">
                <button
                  type="button"
                  onClick={() => setActiveModal('settings')}
                  className="h-11 w-11 flex items-center justify-center rounded-xl bg-white/15 backdrop-blur-sm hover:bg-white/25 active:bg-white/30 transition-all duration-200 touch-manipulation border border-white/20"
                  aria-label="Open settings menu"
                >
                  <Bars3Icon className="h-5 w-5" />
                </button>

                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 bg-white/20 rounded-lg flex items-center justify-center">
                    <span className="text-xs font-bold">P</span>
                  </div>
                  <span className="text-sm font-semibold opacity-90">PaydayPilot</span>
                </div>

                <button
                  type="button"
                  onClick={() => {
                    setActiveModal('notifications');
                    if (changelogNotification.hasNewUpdate) {
                      changelogNotification.markChangelogAsRead();
                    }
                  }}
                  className="relative h-11 w-11 flex items-center justify-center rounded-xl bg-white/15 backdrop-blur-sm hover:bg-white/25 active:bg-white/30 transition-all duration-200 touch-manipulation border border-white/20"
                  aria-label={`Open notifications${changelogNotification.hasNewUpdate ? ' (new updates available)' : ''}`}
                >
                  <NotificationBadge show={changelogNotification.hasNewUpdate} />
                  <BellIcon className="h-5 w-5" />
                </button>
              </div>

              {/* Balance Display */}
              <div className="relative mb-6">
                <p className="text-xs uppercase tracking-wider font-medium text-white/60 mb-2">Total Balance</p>
                {isLoadingSummary ? (
                  <div className="h-12 w-48 bg-white/20 rounded-xl animate-pulse"></div>
                ) : (
                  <p className="text-5xl font-bold leading-none mb-1 tracking-tight">
                    {formatCurrency(totalBalance, currency)}
                  </p>
                )}
                <p className="text-sm text-white/70">Available funds</p>
              </div>

              {/* Stats Cards */}
              <div className="relative grid grid-cols-2 gap-4">
                <div className="bg-white/10 rounded-2xl p-4 backdrop-blur-sm border border-white/10">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-6 w-6 bg-orange-400/20 rounded-lg flex items-center justify-center">
                      <CalendarDaysIcon className="h-3.5 w-3.5 text-orange-300" />
                    </div>
                    <p className="text-xs font-medium text-white/70 uppercase tracking-wider">Upcoming</p>
                  </div>
                  <p className="text-2xl font-bold text-white">{upcomingBills.length}</p>
                  <p className="text-xs text-white/60 mt-1">bills pending</p>
                </div>

                <div className="bg-white/10 rounded-2xl p-4 backdrop-blur-sm border border-white/10">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-6 w-6 bg-red-400/20 rounded-lg flex items-center justify-center">
                      <span className="text-red-300 text-xs font-bold">$</span>
                    </div>
                    <p className="text-xs font-medium text-white/70 uppercase tracking-wider">Due Soon</p>
                  </div>
                  <p className="text-lg font-bold text-white leading-tight">{formatCurrency(totalUpcomingDue, currency)}</p>
                  <p className="text-xs text-white/60 mt-1">this month</p>
                </div>
              </div>
            </div>
          </section>

          {/* Bills Section - Redesigned with better hierarchy */}
          <section className="px-4 pb-4">
            <div className="mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">Upcoming Bills</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">Manage your upcoming payments</p>
              </div>
            </div>

            <div className="space-y-4">
              {upcomingBills.length === 0 && (
                <div className="rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 border border-gray-200 dark:border-gray-700 px-8 py-12 text-center">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 flex items-center justify-center shadow-inner">
                    <CalendarDaysIcon className="w-10 h-10 text-blue-500 dark:text-blue-400" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-3">No upcoming bills</h3>
                  <p className="text-gray-500 dark:text-gray-400 leading-relaxed max-w-sm mx-auto">
                    Bills you add will appear here for quick access and management.
                  </p>
                </div>
              )}

              {upcomingBills.map((bill) => {
                const isDue = bill.dueDate && new Date(bill.dueDate) <= new Date();
                const isDueSoon = bill.dueDate && new Date(bill.dueDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

                return (
                  <div
                    key={bill.id}
                    className="relative group"
                  >
                    <button
                      type="button"
                      className="w-full text-left rounded-2xl bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group-hover:border-blue-200 dark:group-hover:border-blue-800 touch-manipulation active:scale-[0.98]"
                      onClick={() => {
                        router.push(`/bills/${bill.id}`);
                      }}
                      aria-label={`View details for ${bill.name || getBillLabel(bill)} bill`}
                    >
                      {/* Status indicator */}
                      <div className={cn(
                        'absolute top-0 left-0 w-1 h-full rounded-l-2xl',
                        isDue ? 'bg-red-500' : isDueSoon ? 'bg-orange-500' : 'bg-green-500'
                      )} />

                      <div className="p-5 pl-6">
                        {/* Header */}
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="font-bold text-lg text-gray-900 dark:text-gray-100 truncate">
                                {bill.name || getBillLabel(bill)}
                              </h3>
                              {isDue && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                                  Due Now
                                </span>
                              )}
                              {!isDue && isDueSoon && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300">
                                  Due Soon
                                </span>
                              )}
                            </div>

                            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                              {bill.vendor && (
                                <>
                                  <span>{bill.vendor}</span>
                                  {bill.category && <span>-</span>}
                                </>
                              )}
                              {bill.category && <span>{bill.category}</span>}
                            </div>
                          </div>
                        </div>

                        {/* Amount and Date Row */}
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-1">
                              Amount
                            </p>
                            <p className={cn(
                              "text-2xl font-bold",
                              isDue
                                ? "text-red-600 dark:text-red-400"
                                : isDueSoon
                                  ? "text-orange-600 dark:text-orange-400"
                                  : "text-gray-900 dark:text-gray-100"
                            )}>
                              {formatCurrency(bill.amount, currency)}
                            </p>
                          </div>

                          <div className="text-right">
                            <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-1">
                              Due Date
                            </p>
                            <p className={cn(
                              "text-base font-semibold",
                              isDue
                                ? "text-red-600 dark:text-red-400"
                                : isDueSoon
                                  ? "text-orange-600 dark:text-orange-400"
                                  : "text-gray-700 dark:text-gray-300"
                            )}>
                              {formatDueDate(bill.dueDate)}
                            </p>
                          </div>
                        </div>

                        {/* Notes */}
                        {bill.notes && (
                          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-100 dark:border-gray-700">
                            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                              {bill.notes}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Hover overlay */}
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-indigo-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl pointer-events-none" />
                    </button>
                  </div>
                );
              })}
            </div>
          </section>


          {/* Recent Transactions - Enhanced design */}
          <section className="px-4 pb-4">
            <div className="rounded-2xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 shadow-sm p-6">
              <div className="mb-6">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-1">Recent Activity</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Latest financial transactions</p>
                </div>
                <button
                  type="button"
                  onClick={() => router.push('/notifications')}
                  className="text-sm font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors px-3 py-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 min-h-[40px] touch-manipulation"
                  aria-label="View all transactions"
                >
                  View All
                </button>
              </div>

              <div className="space-y-3">
                {isLoadingTransactions && (
                  <div className="space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-center gap-4 p-4 rounded-xl">
                        <div className="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3"></div>
                        </div>
                        <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                )}

                {!isLoadingTransactions && recentTransactions.length === 0 && (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center">
                      <RectangleStackIcon className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                    </div>
                    <p className="text-gray-500 dark:text-gray-400 font-medium">No transactions yet</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">Your recent activity will appear here</p>
                  </div>
                )}

                {recentTransactions.map((tx, index) => {
                  const isIncome = tx.type === 'income';
                  const amountDisplay = formatCurrency(Math.abs(tx.amount), currency);
                  const categoryColors = [
                    'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-pink-500'
                  ];
                  const categoryColor = categoryColors[index % categoryColors.length];

                  return (
                    <button
                      key={tx.id}
                      type="button"
                      className="w-full group flex items-center gap-4 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800/50 active:bg-gray-100 dark:active:bg-gray-700/50 transition-all duration-200 touch-manipulation border border-transparent hover:border-gray-200 dark:hover:border-gray-700"
                      onClick={() => {
                        // Future: Navigate to transaction details
                        console.log('Transaction clicked:', tx.id);
                      }}
                      aria-label={`View transaction: ${tx.description || tx.category || 'Transaction'}`}
                    >
                      {/* Transaction Icon */}
                      <div className={cn(
                        'h-12 w-12 rounded-xl flex items-center justify-center text-white font-bold shadow-sm group-hover:shadow-md transition-all duration-200',
                        isIncome ? 'bg-green-500' : categoryColor
                      )}>
                        {isIncome ? (
                          <span className="text-lg">&rarr;</span>
                        ) : (
                          <span className="text-sm">
                            {tx.description?.charAt(0).toUpperCase() || tx.category?.charAt(0).toUpperCase() || '?'}
                          </span>
                        )}
                      </div>

                      {/* Transaction Details */}
                      <div className="flex-1 min-w-0 text-left">
                        <p className="font-semibold text-gray-900 dark:text-gray-100 truncate mb-1">
                          {tx.description || tx.category || 'Transaction'}
                        </p>
                        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                          <span>{new Date(tx.date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}</span>
                          <span className="w-1 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"></span>
                          <span className="truncate">{tx.category || 'Uncategorized'}</span>
                        </div>
                      </div>

                      {/* Amount */}
                      <div className="text-right">
                        <p className={cn(
                          'font-bold text-lg',
                          isIncome
                            ? 'text-green-600 dark:text-green-400'
                            : 'text-gray-900 dark:text-gray-100'
                        )}>
                          {isIncome ? '+' : '-'}{amountDisplay}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {isIncome ? 'Income' : 'Expense'}
                        </p>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          </section>

          {/* Footer Section - Clean and minimal */}
          <section className="px-4 pb-20">
            <div className="rounded-2xl bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 border border-gray-200 dark:border-gray-700 p-8 text-center shadow-sm">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-white text-base font-bold">P</span>
                </div>
                <span className="text-xl font-bold text-gray-900 dark:text-gray-100">PaydayPilot</span>
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-3 font-medium">
                Smart bill management at your fingertips
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500">
                &copy; {new Date().getFullYear()} PayDay Pilot. Made with care.
              </p>
            </div>
          </section>
        </main>
      </div>

      {isMounted ? createPortal(navMarkup, document.body) : navMarkup}

      {activeModal && modalContent && (
        <DashboardCardModal
          isOpen={!!activeModal}
          onClose={closeModal}
          title={modalTitle}
          size={modalSize}
        >
          {modalContent}
        </DashboardCardModal>
      )}

      {isSubmitting && (
        <div className="fixed inset-0 z-[60] bg-black/50 backdrop-blur-sm flex items-center justify-center">
          <div className="bg-white dark:bg-gray-900 rounded-2xl px-8 py-6 shadow-2xl mx-4 max-w-sm w-full">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-indigo-200 border-t-indigo-600"></div>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Saving bill...</span>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default MobileDashboard;

