import * as dateUtils from '../date'

describe('Date Utilities', () => {
  describe('formatDate', () => {
    it('formats date correctly with different formats', () => {
      const date = new Date('2025-01-15T10:30:00Z')

      expect(dateUtils.formatDate(date, 'short')).toBeTruthy()
      expect(dateUtils.formatDate(date, 'medium')).toContain('Jan')
      expect(dateUtils.formatDate(date, 'long')).toContain('January')
    })

    it('handles invalid dates gracefully', () => {
      const invalidDate = new Date('invalid')
      expect(dateUtils.formatDate(invalidDate, 'medium')).toBe('')
    })

    it('handles null and undefined dates', () => {
      expect(dateUtils.formatDate(null, 'medium')).toBe('')
      expect(dateUtils.formatDate(undefined, 'medium')).toBe('')
    })
  })

  describe('isToday', () => {
    it('correctly identifies today', () => {
      const today = new Date()
      expect(dateUtils.isToday(today)).toBe(true)
    })

    it('correctly identifies non-today dates', () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)

      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)

      expect(dateUtils.isToday(yesterday)).toBe(false)
      expect(dateUtils.isToday(tomorrow)).toBe(false)
    })

    it('handles null and undefined dates', () => {
      expect(dateUtils.isToday(null)).toBe(false)
      expect(dateUtils.isToday(undefined)).toBe(false)
    })
  })

  describe('parseLocalDateString', () => {
    it('parses date strings correctly', () => {
      const result = dateUtils.parseLocalDateString('2025-01-15')
      expect(result).toBeInstanceOf(Date)
      expect(result?.getFullYear()).toBe(2025)
      expect(result?.getMonth()).toBe(0) // January
      expect(result?.getDate()).toBe(15)
    })

    it('handles invalid date strings', () => {
      const result = dateUtils.parseLocalDateString('invalid-date')
      expect(result).toBeUndefined()
    })

    it('handles empty strings', () => {
      const result = dateUtils.parseLocalDateString('')
      expect(result).toBeUndefined()
    })
  })

  describe('addDays', () => {
    it('adds days correctly', () => {
      const date = new Date(2025, 0, 15) // January 15, 2025
      const result = dateUtils.addDays(date, 5)
      expect(result?.getDate()).toBe(20)
      expect(result?.getMonth()).toBe(0) // January
      expect(result?.getFullYear()).toBe(2025)
    })

    it('handles negative days (subtraction)', () => {
      const date = new Date(2025, 0, 15) // January 15, 2025
      const result = dateUtils.addDays(date, -5)
      expect(result?.getDate()).toBe(10)
      expect(result?.getMonth()).toBe(0) // January
      expect(result?.getFullYear()).toBe(2025)
    })

    it('handles month boundaries', () => {
      const date = new Date(2025, 0, 30) // January 30, 2025
      const result = dateUtils.addDays(date, 5)
      expect(result?.getDate()).toBe(4)
      expect(result?.getMonth()).toBe(1) // February
      expect(result?.getFullYear()).toBe(2025)
    })

    it('handles null and undefined dates', () => {
      expect(dateUtils.addDays(null, 5)).toBeUndefined()
      expect(dateUtils.addDays(undefined, 5)).toBeUndefined()
    })

    it('handles invalid days parameter', () => {
      const date = new Date('2025-01-15')
      expect(dateUtils.addDays(date, NaN)).toBeUndefined()
      expect(dateUtils.addDays(date, Infinity)).toBeUndefined()
    })
  })

  describe('getRelativeTimeString', () => {
    it('returns correct relative time strings', () => {
      const today = new Date()
      expect(dateUtils.getRelativeTimeString(today)).toBe('Today')

      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      expect(dateUtils.getRelativeTimeString(tomorrow)).toBe('Tomorrow')

      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      expect(dateUtils.getRelativeTimeString(yesterday)).toBe('Yesterday')
    })

    it('handles null and undefined dates', () => {
      expect(dateUtils.getRelativeTimeString(null)).toBe('')
      expect(dateUtils.getRelativeTimeString(undefined)).toBe('')
    })

    it('handles future dates', () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 3)
      expect(dateUtils.getRelativeTimeString(futureDate)).toBe('In 3 days')
    })

    it('handles past dates', () => {
      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - 3)
      expect(dateUtils.getRelativeTimeString(pastDate)).toBe('3 days ago')
    })
  })

  describe('isPastDate', () => {
    it('correctly identifies past dates', () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      expect(dateUtils.isPastDate(yesterday)).toBe(true)
    })

    it('correctly identifies future dates', () => {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      expect(dateUtils.isPastDate(tomorrow)).toBe(false)
    })

    it('correctly identifies today', () => {
      const today = new Date()
      expect(dateUtils.isPastDate(today)).toBe(false)
    })

    it('handles null and undefined dates', () => {
      expect(dateUtils.isPastDate(null)).toBe(false)
      expect(dateUtils.isPastDate(undefined)).toBe(false)
    })
  })

  describe('getNextOccurrence', () => {
    it('calculates weekly recurrence correctly', () => {
      const date = new Date(2025, 0, 15) // January 15, 2025
      const result = dateUtils.getNextOccurrence(date, 'weekly')
      expect(result?.getDate()).toBe(22)
    })

    it('calculates monthly recurrence correctly', () => {
      const date = new Date(2025, 0, 15) // January 15, 2025
      const result = dateUtils.getNextOccurrence(date, 'monthly')
      expect(result?.getMonth()).toBe(1) // February
      expect(result?.getDate()).toBe(15)
    })

    it('calculates yearly recurrence correctly', () => {
      const date = new Date(2025, 0, 15) // January 15, 2025
      const result = dateUtils.getNextOccurrence(date, 'yearly')
      expect(result?.getFullYear()).toBe(2026)
      expect(result?.getMonth()).toBe(0) // January
      expect(result?.getDate()).toBe(15)
    })

    it('handles null and undefined dates', () => {
      expect(dateUtils.getNextOccurrence(null, 'weekly')).toBeUndefined()
      expect(dateUtils.getNextOccurrence(undefined, 'monthly')).toBeUndefined()
    })
  })

  describe('isWithinDays', () => {
    it('correctly identifies dates within range', () => {
      const date1 = new Date(2025, 0, 15) // January 15, 2025
      const date2 = new Date(2025, 0, 17) // January 17, 2025
      expect(dateUtils.isWithinDays(date1, date2, 5)).toBe(true)
    })

    it('correctly identifies dates outside range', () => {
      const date1 = new Date(2025, 0, 15) // January 15, 2025
      const date2 = new Date(2025, 0, 25) // January 25, 2025
      expect(dateUtils.isWithinDays(date1, date2, 5)).toBe(false)
    })

    it('handles null and undefined dates', () => {
      const date = new Date(2025, 0, 15) // January 15, 2025
      expect(dateUtils.isWithinDays(null, date, 5)).toBe(false)
      expect(dateUtils.isWithinDays(date, null, 5)).toBe(false)
    })

    it('handles invalid days parameter', () => {
      const date1 = new Date(2025, 0, 15) // January 15, 2025
      const date2 = new Date(2025, 0, 17) // January 17, 2025
      expect(dateUtils.isWithinDays(date1, date2, -1)).toBe(false)
      expect(dateUtils.isWithinDays(date1, date2, NaN)).toBe(false)
    })
  })

  describe('getDateRange', () => {
    it('calculates week range correctly', () => {
      const { start, end } = dateUtils.getDateRange('week')
      expect(start).toBeInstanceOf(Date)
      expect(end).toBeInstanceOf(Date)
      expect(end.getTime() - start.getTime()).toBe(6 * 24 * 60 * 60 * 1000)
    })

    it('calculates month range correctly', () => {
      const { start, end } = dateUtils.getDateRange('month')
      expect(start.getDate()).toBe(1)
      expect(start).toBeInstanceOf(Date)
      expect(end).toBeInstanceOf(Date)
    })

    it('calculates year range correctly', () => {
      const { start, end } = dateUtils.getDateRange('year')
      expect(start.getMonth()).toBe(0) // January
      expect(start.getDate()).toBe(1)
      expect(end.getMonth()).toBe(11) // December
      expect(end.getDate()).toBe(31)
    })
  })
})
