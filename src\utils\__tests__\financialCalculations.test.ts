import { DebtInfo, LoanInfo } from '@/types/bill';
import { calculateLoanPayment, calculateMinimumPayment, calculateTotalInterest, getPeriodicRate } from '@/utils/financialCalculations';

describe('Financial Calculation Precision Tests', () => {
    // Test loan payment calculations with extreme precision
    describe('calculateLoanPayment', () => {
        test('should calculate a standard loan payment correctly', () => {
            const loanInfo: LoanInfo = {
                originalAmount: 10000,
                interestRate: 5,
                loanTerm: 60, // 5 years
                startDate: '2023-01-01',
                paymentFrequency: 'monthly'
            };

            const payment = calculateLoanPayment(loanInfo);
            expect(payment).toBeCloseTo(188.71, 2); // Expected $188.71/month
        });

        test('should handle zero interest loans correctly', () => {
            const loanInfo: LoanInfo = {
                originalAmount: 10000,
                interestRate: 0,
                loanTerm: 60,
                startDate: '2023-01-01',
                paymentFrequency: 'monthly'
            };

            const payment = calculateLoanPayment(loanInfo);
            expect(payment).toBeCloseTo(166.67, 2); // $10000/60 = $166.67
        });

        test('should handle very small interest rates precisely', () => {
            const loanInfo: LoanInfo = {
                originalAmount: 10000,
                interestRate: 0.1, // 0.1%
                loanTerm: 60,
                startDate: '2023-01-01',
                paymentFrequency: 'monthly'
            };

            const payment = calculateLoanPayment(loanInfo);
            expect(payment).toBeCloseTo(167.09, 2);
        });

        test('should handle very large loan amounts without losing precision', () => {
            const loanInfo: LoanInfo = {
                originalAmount: 1000000, // $1 million
                interestRate: 4.5,
                loanTerm: 360, // 30 years
                startDate: '2023-01-01',
                paymentFrequency: 'monthly'
            };

            const payment = calculateLoanPayment(loanInfo);
            expect(payment).toBeCloseTo(5066.55, 2);
        });
    });

    describe('calculateMinimumPayment', () => {
        test('should calculate percentage-based minimum payment correctly', () => {
            const debtInfo: DebtInfo = {
                currentBalance: 5000,
                minimumPaymentCalculation: 'percentage',
                minimumPaymentPercentage: 2,
                interestRate: 18
            };

            const payment = calculateMinimumPayment(debtInfo);
            expect(payment).toBeCloseTo(100, 2); // 2% of $5000 = $100
        });

        test('should calculate balance-based minimum payment correctly', () => {
            const debtInfo: DebtInfo = {
                currentBalance: 5000,
                minimumPaymentCalculation: 'balance_based',
                interestRate: 18 // 18% APR
            };

            // 1% of balance + monthly interest
            // $5000 * 0.01 + $5000 * (0.18 / 12) = $50 + $75 = $125
            const payment = calculateMinimumPayment(debtInfo);
            expect(payment).toBeCloseTo(125, 2);
        });

        test('should use fixed minimum payment when specified', () => {
            const debtInfo: DebtInfo = {
                currentBalance: 5000,
                minimumPaymentCalculation: 'fixed',
                minimumPayment: 150,
                interestRate: 18
            };

            const payment = calculateMinimumPayment(debtInfo);
            expect(payment).toBe(150);
        });

        test('should handle large balances without precision loss', () => {
            const debtInfo: DebtInfo = {
                currentBalance: 123456.78,
                minimumPaymentCalculation: 'percentage',
                minimumPaymentPercentage: 3,
                interestRate: 19.99
            };

            const payment = calculateMinimumPayment(debtInfo);
            expect(payment).toBeCloseTo(3703.70, 2); // 3% of $123,456.78
        });
    });

    describe('calculateTotalInterest', () => {
        test('should calculate total interest accurately for a standard loan', () => {
            const loanInfo: LoanInfo = {
                originalAmount: 10000,
                interestRate: 5,
                loanTerm: 60, // 5 years
                startDate: '2023-01-01',
                paymentFrequency: 'monthly'
            };

            const totalInterest = calculateTotalInterest(loanInfo);
            // Total paid: 60 * $188.71 = $11,322.60
            // Total interest: $11,322.60 - $10,000 = $1,322.60
            expect(totalInterest).toBeCloseTo(1322.60, 2);
        });

        test('should return zero interest for a zero interest loan', () => {
            const loanInfo: LoanInfo = {
                originalAmount: 10000,
                interestRate: 0,
                loanTerm: 60,
                startDate: '2023-01-01',
                paymentFrequency: 'monthly'
            };

            const totalInterest = calculateTotalInterest(loanInfo);
            expect(totalInterest).toBeCloseTo(0, 2);
        });

        test('should calculate interest correctly for biweekly payments', () => {
            const loanInfo: LoanInfo = {
                originalAmount: 10000,
                interestRate: 5,
                loanTerm: 60,
                startDate: '2023-01-01',
                paymentFrequency: 'biweekly'
            };

            const totalInterest = calculateTotalInterest(loanInfo);
            // Biweekly payments should result in less total interest than monthly
            expect(totalInterest).toBeLessThan(1322.60);
        });
    });

    describe('getPeriodicRate', () => {
        test('should convert annual rate to monthly rate correctly', () => {
            const annualRate = 12; // 12%
            const frequency = 'monthly';

            const periodicRate = getPeriodicRate(annualRate, frequency);
            expect(periodicRate).toBeCloseTo(0.01, 4); // 12% / 12 months = 1%
        });

        test('should convert annual rate to weekly rate correctly', () => {
            const annualRate = 52; // 52%
            const frequency = 'weekly';

            const periodicRate = getPeriodicRate(annualRate, frequency);
            expect(periodicRate).toBeCloseTo(0.01, 4); // 52% / 52 weeks = 1%
        });

        test('should handle very small interest rates without losing precision', () => {
            const annualRate = 0.01; // 0.01%
            const frequency = 'monthly';

            const periodicRate = getPeriodicRate(annualRate, frequency);
            expect(periodicRate).toBeCloseTo(0.0000083333, 10); // 0.01% / 12 = 0.00083333...%
        });
    });
});
