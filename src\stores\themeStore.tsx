'use client';

import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';

// Define theme colors
export type ThemeColor = 'default' | 'blue' | 'purple' | 'green' | 'orange' | 'teal';

interface ThemeContextType {
  themeColor: ThemeColor;
  setThemeColor: (color: ThemeColor) => void;
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Initialize with system preference to avoid flash
  const [themeColor, setThemeColor] = useState<ThemeColor>('default');
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Initialize with system preference if available
    if (typeof window !== 'undefined') {
      const savedDarkMode = localStorage.getItem('darkMode');
      if (savedDarkMode !== null) {
        return savedDarkMode === 'true';
      }
      // Check for system preference if no saved preference
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });

  // Memoize the theme color setter to prevent unnecessary re-renders
  const setThemeColorMemoized = useCallback((color: ThemeColor) => {
    setThemeColor(color);
  }, []);

  // Load saved preferences on initial render
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Theme color
      const savedThemeColor = localStorage.getItem('themeColor');
      if (savedThemeColor && ['default', 'blue', 'purple', 'green', 'orange', 'teal'].includes(savedThemeColor)) {
        setThemeColor(savedThemeColor as ThemeColor);
      }

      // Dark mode - recheck and apply immediately
      const savedDarkMode = localStorage.getItem('darkMode');
      let shouldBeDark = false;

      if (savedDarkMode !== null) {
        shouldBeDark = savedDarkMode === 'true';
      } else {
        // Check for system preference if no saved preference
        shouldBeDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      }

      setIsDarkMode(shouldBeDark);

      // Apply dark mode class immediately to prevent flash
      if (shouldBeDark) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }

      // Listen for system theme changes
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleSystemThemeChange = (e: MediaQueryListEvent) => {
        // Only update if user hasn't set a manual preference
        if (localStorage.getItem('darkMode') === null) {
          setIsDarkMode(e.matches);
        }
      };

      mediaQuery.addEventListener('change', handleSystemThemeChange);

      return () => {
        mediaQuery.removeEventListener('change', handleSystemThemeChange);
      };
    }
  }, []);

  // Update document when theme changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }

      // Save to localStorage
      localStorage.setItem('darkMode', isDarkMode.toString());
    }
  }, [isDarkMode]);

  // Save theme color to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('themeColor', themeColor);

      // Update CSS variables for the theme color
      if (themeColor === 'default') {
        document.documentElement.style.setProperty('--theme-color', '#3880ff');
        document.documentElement.style.setProperty('--theme-color-light', 'color-mix(in srgb, #3880ff, white 20%)');
        document.documentElement.style.setProperty('--theme-color-dark', 'color-mix(in srgb, #3880ff, black 20%)');
      } else {
        document.documentElement.style.setProperty('--theme-color', getThemeColorValue(themeColor));
        document.documentElement.style.setProperty('--theme-color-light', `color-mix(in srgb, ${getThemeColorValue(themeColor)}, white 20%)`);
        document.documentElement.style.setProperty('--theme-color-dark', `color-mix(in srgb, ${getThemeColorValue(themeColor)}, black 20%)`);
      }
    }
  }, [themeColor]);

  // Memoize toggleDarkMode to prevent unnecessary re-renders
  const toggleDarkMode = useCallback(() => {
    setIsDarkMode(prev => !prev);
  }, []);

  // Helper to get actual color value
  function getThemeColorValue(color: ThemeColor): string {
    switch (color) {
      case 'blue':
        return '#3880ff';
      case 'purple':
        return '#5260ff';
      case 'green':
        return '#2dd36f';
      case 'orange':
        return '#ff9500';
      case 'teal':
        return '#00b4d8';
      case 'default':
      default:
        return '#3880ff';
    }
  }

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    themeColor,
    setThemeColor: setThemeColorMemoized,
    isDarkMode,
    toggleDarkMode,
  }), [themeColor, setThemeColorMemoized, isDarkMode, toggleDarkMode]);

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

// Custom hook to use the theme context
export function useTheme() {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }

  return context;
}

// Selector hooks to allow components to subscribe to specific context values
// This prevents re-renders when unrelated theme state changes
export function useThemeColor() {
  const { themeColor, setThemeColor } = useTheme();
  return { themeColor, setThemeColor };
}

export function useDarkMode() {
  const { isDarkMode, toggleDarkMode } = useTheme();
  return { isDarkMode, toggleDarkMode };
}