'use client';

// Local-only auth store stub: no Firebase. Keeps API compatible with existing consumers.
import { create } from 'zustand';

interface AuthState {
  user: null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  auth: null;
  setUser: (user: null) => void;
  setAuth: (auth: null) => void;
  signOut: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  auth: null,
  setUser: () => set({ user: null, isAuthenticated: false, isLoading: false }),
  setAuth: () => set({ auth: null }),
  signOut: async () => { /* no-op */ },
}));

export function useAuthStateListener() {
  // No-op in local-only mode
  return null;
}
