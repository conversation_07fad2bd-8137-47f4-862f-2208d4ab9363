import { Bill, BILL_CATEGORIES, COMMON_VENDORS } from '@/types/bill';
import { differenceInDays, isSameDay, isValid, parse, parseISO } from 'date-fns';

/**
 * Attempts to parse a date string from various common formats.
 * Handles spreadsheet date objects (which might be Dates already) and numeric representations.
 * Prioritizes ISO format, then common US/EU formats.
 * @param value The value to parse (string, number, or Date object).
 * @returns A Date object if successful, otherwise null.
 */
export function parseImportDate(value: any): Date | null {
  if (!value) {
    return null;
  }

  // 1. If it's already a valid Date object (e.g., from xlsx parsing with cellDates:true)
  if (value instanceof Date && isValid(value)) {
    return value;
  }

  // 2. Handle Excel numeric date format (days since 1900 or 1904)
  // See https://github.com/SheetJS/sheetjs/blob/master/docbits/52_datatype.md
  if (typeof value === 'number' && value > 0) {
    try {
      // First, determine if we're using the 1900 or 1904 date system
      // Default to 1900 system (most common in Windows Excel)
      const use1904System = false; // Set to true for Mac Excel pre-2016

      // Constants for date conversion
      const SECONDS_IN_DAY = 86400;
      const MILLIS_IN_DAY = SECONDS_IN_DAY * 1000;

      // Different epoch adjustments for 1900 vs 1904 system
      let excelEpochDiff;
      if (use1904System) {
        // Mac Excel 1904 date system
        excelEpochDiff = 24107; // Days between 1970-01-01 and 1904-01-01
        const date = new Date((value - excelEpochDiff) * MILLIS_IN_DAY);
        if (isValid(date)) return date;
      } else {
        // Windows Excel 1900 date system
        excelEpochDiff = 25569; // Days between 1970-01-01 and 1900-01-01

        // Handle the Excel leap year bug (1900 wasn't actually a leap year)
        if (value === 60) {
          // Special case: Excel thinks Feb 29, 1900 exists
          // Return Feb 28 instead or Mar 1 depending on your preference
          return new Date(1900, 1, 28);
        }

        if (value < 60) {
          // For dates before the phantom leap day
          const date = new Date((value - excelEpochDiff) * MILLIS_IN_DAY);
          if (isValid(date)) return date;
        } else {
          // For dates after the phantom leap day, adjust by subtracting 1
          const date = new Date((value - excelEpochDiff - 1) * MILLIS_IN_DAY);
          if (isValid(date)) return date;
        }
      }

      // Fallback: try direct conversion without adjustments
      const date = new Date((value - excelEpochDiff) * MILLIS_IN_DAY);
      if (isValid(date)) return date;
    } catch (e) {
      console.warn('Error parsing Excel date:', e);
      // Ignore conversion error, try string parsing
    }
  }

  // 3. Convert to string and trim for parsing
  const dateString = String(value).trim();
  if (!dateString) {
    return null;
  }

  // 4. Attempt parsing specific formats
  const formats = [
    'yyyy-MM-dd',   // ISO basic
    'MM/dd/yyyy',   // Common US
    'M/d/yyyy',     // Common US (no leading zero)
    'MM-dd-yyyy',   // Common US (hyphen)
    'M-d-yyyy',     // Common US (hyphen, no leading zero)
    'dd/MM/yyyy',   // Common EU
    'd/M/yyyy',     // Common EU (no leading zero)
    'dd-MM-yyyy',   // Common EU (hyphen)
    'd-M-yyyy',     // Common EU (hyphen, no leading zero)
    'yyyy/MM/dd',
    'MMMM d, yyyy', // e.g., January 1, 2023
    'MMM d, yyyy',  // e.g., Jan 1, 2023
    'MMMM d yyyy',  // e.g., January 1 2023 (no comma)
    'MMM d yyyy',   // e.g., Jan 1 2023 (no comma)
    // Add more formats as needed
  ];

  // Try ISO parsing first (covers timestamps too)
  try {
    const isoParsed = parseISO(dateString);
    if (isValid(isoParsed)) {
      return isoParsed;
    }
  } catch (e) { /* Ignore */ }

  // Try other formats
  const referenceDate = new Date(); // Use current date as reference for parsing
  for (const format of formats) {
    try {
      const parsedDate = parse(dateString, format, referenceDate);
      if (isValid(parsedDate)) {
        // Basic sanity check: Avoid dates too far in the past/future if format is ambiguous (e.g., M/d/yy)
        // If the format doesn't include century (yy), date-fns might guess wrong.
        // Add checks if needed, e.g., if year < 1900 or year > 2100, maybe reject.
        return parsedDate;
      }
    } catch (e) { /* Ignore parsing errors for this format */ }
  }

  // If no format matched
  return null;
}

/**
 * Cleans an amount value, removing currency symbols, commas, and whitespace.
 * Converts to a number.
 * @param value The value to clean.
 * @returns A number if successful, otherwise null.
 */
export function cleanImportAmount(value: any): number | null {
  if (value === null || value === undefined || String(value).trim() === '') {
    return null;
  }

  let cleanedString = String(value)
    .replace(/[$€£¥]/g, '') // Remove common currency symbols
    .replace(/,/g, '')       // Remove thousand separators
    .trim();

  // Handle potential parentheses for negative numbers (e.g., (100.00))
  const isNegative = cleanedString.startsWith('(') && cleanedString.endsWith(')');
  if (isNegative) {
    cleanedString = '-' + cleanedString.substring(1, cleanedString.length - 1);
  }

  const num = parseFloat(cleanedString);

  return isNaN(num) ? null : num;
}

/**
 * Trims leading/trailing whitespace from a string value.
 * @param value The value to trim.
 * @returns The trimmed string, or an empty string if the input is null/undefined.
 */
export function trimImportText(value: any): string {
  return value === null || value === undefined ? '' : String(value).trim();
}

/**
 * Detects potential vendor name from bill name or description
 * @param billName The bill name or description
 * @returns The detected vendor name, or undefined if none detected
 */
export function detectVendor(billName: string): string | undefined {
  if (!billName) return undefined;

  const normalizedName = billName.toLowerCase().trim();

  // Check for exact matches first (case insensitive)
  const exactMatch = COMMON_VENDORS.find(vendor =>
    normalizedName.includes(vendor.toLowerCase())
  );

  if (exactMatch) return exactMatch;

  // Check for partial matches with more sophisticated logic
  // This could be enhanced with fuzzy matching or ML-based approaches
  const partialMatches = COMMON_VENDORS.filter(vendor => {
    const vendorWords = vendor.toLowerCase().split(' ');
    return vendorWords.some(word =>
      word.length > 3 && normalizedName.includes(word.toLowerCase())
    );
  });

  return partialMatches.length > 0 ? partialMatches[0] : undefined;
}

/**
 * Suggests tags based on bill name, category, and vendor
 * @param billName The bill name
 * @param category The bill category
 * @param vendor The detected vendor (optional)
 * @returns Array of suggested tags
 */
export function suggestTags(billName: string, category: string, vendor?: string): string[] {
  const tags: string[] = [];
  const normalizedName = billName.toLowerCase();

  // Add category-based tag
  if (category && category !== 'Other') {
    tags.push(category.toLowerCase());
  }

  // Add vendor-based tag
  if (vendor) {
    tags.push(vendor.toLowerCase().replace(' ', '-'));
  }

  // Add common keyword-based tags
  const keywordMap: Record<string, string[]> = {
    subscription: ['subscription', 'monthly', 'recurring'],
    essential: ['rent', 'mortgage', 'electric', 'water', 'gas', 'grocery'],
    finance: ['loan', 'credit', 'interest', 'payment', 'bank'],
    entertainment: ['netflix', 'spotify', 'hulu', 'disney', 'streaming', 'subscription'],
    insurance: ['insurance', 'coverage', 'policy', 'premium'],
    utility: ['utility', 'electric', 'water', 'gas', 'internet', 'phone', 'bill']
  };

  // Check for keyword matches
  Object.entries(keywordMap).forEach(([tag, keywords]) => {
    if (keywords.some(keyword => normalizedName.includes(keyword))) {
      tags.push(tag);
    }
  });

  // Remove duplicates and return
  return [...new Set(tags)];
}

/**
 * Detects potential duplicates among bills
 * @param newBill The new bill to check
 * @param existingBills Array of existing bills
 * @returns Array of potential duplicate bills with similarity scores
 */
export function detectDuplicates(newBill: Partial<Bill>, existingBills: Bill[]): { bill: Bill; score: number }[] {
  if (!existingBills?.length) return [];

  const potentialDuplicates = existingBills.map(bill => {
    let score = 0;

    // Check name similarity (50% of score)
    if (bill.name && newBill.name) {
      const nameA = bill.name.toLowerCase();
      const nameB = newBill.name.toLowerCase();

      // Exact match
      if (nameA === nameB) {
        score += 50;
      }
      // Partial match
      else if (nameA.includes(nameB) || nameB.includes(nameA)) {
        score += 30;
      }
      // Word overlap
      else {
        const wordsA = nameA.split(/\s+/);
        const wordsB = nameB.split(/\s+/);
        const commonWords = wordsA.filter(word => wordsB.includes(word) && word.length > 3);
        if (commonWords.length > 0) {
          score += Math.min(25, commonWords.length * 10);
        }
      }
    }

    // Check amount similarity (30% of score)
    if (typeof bill.amount === 'number' && typeof newBill.amount === 'number') {
      // Exact match
      if (bill.amount === newBill.amount) {
        score += 30;
      }
      // Close match (within 5%)
      else {
        const diff = Math.abs(bill.amount - newBill.amount);
        const percentage = diff / Math.max(bill.amount, newBill.amount);
        if (percentage < 0.05) {
          score += 20;
        }
      }
    }

    // Check date proximity (20% of score)
    if (bill.dueDate && newBill.dueDate) {
      const dateA = new Date(bill.dueDate);
      const dateB = new Date(newBill.dueDate as string);

      if (isValid(dateA) && isValid(dateB)) {
        // Same day
        if (isSameDay(dateA, dateB)) {
          score += 20;
        }
        // Within 5 days
        else {
          const daysDiff = Math.abs(differenceInDays(dateA, dateB));
          if (daysDiff <= 5) {
            score += Math.max(0, 15 - daysDiff * 3);
          }
        }
      }
    }

    return { bill, score };
  });

  // Filter to only include significant matches (score > 40)
  return potentialDuplicates
    .filter(item => item.score > 40)
    .sort((a, b) => b.score - a.score);
}

/**
 * Validates a bill object and returns validation errors
 * @param bill The bill to validate
 * @returns Array of validation error messages
 */
export function validateBill(bill: Partial<Bill>): string[] {
  const errors: string[] = [];

  // Required fields
  if (!bill.name || bill.name.trim() === '') {
    errors.push('Bill name is required');
  }

  if (bill.amount === undefined || bill.amount === null) {
    errors.push('Amount is required');
  } else if (isNaN(bill.amount) || bill.amount < 0) {
    errors.push('Amount must be a positive number');
  }

  if (!bill.dueDate) {
    errors.push('Due date is required');
  } else {
    const dueDate = new Date(bill.dueDate);
    if (!isValid(dueDate)) {
      errors.push('Due date is invalid');
    }
  }

  // Validate category
  if (bill.category && !BILL_CATEGORIES.includes(bill.category)) {
    errors.push(`Category '${bill.category}' is not valid`);
  }

  return errors;
}
