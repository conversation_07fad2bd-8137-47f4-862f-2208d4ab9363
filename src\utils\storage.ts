// src/utils/storage.ts
import { UserRule } from '@/types/rules'; // Import UserRule

const RULES_STORAGE_KEY = 'paydayPilotUserRules';

// Function to load rules from Local Storage
export function loadRulesFromStorage(): UserRule[] {
  if (typeof window === 'undefined') {
    // Local Storage is not available server-side
    return [];
  }
  try {
    const storedRules = localStorage.getItem(RULES_STORAGE_KEY);
    if (storedRules) {
      const parsedRules = JSON.parse(storedRules);
      // Basic validation: check if it's an array
      if (Array.isArray(parsedRules)) {
        // TODO: Add more robust validation of rule structure if needed
        return parsedRules as UserRule[];
      }
    }
  } catch (error) {
    console.error("Error loading rules from Local Storage:", error);
    // Optionally clear corrupted data: localStorage.removeItem(RULES_STORAGE_KEY);
  }
  return []; // Return empty array if no rules found or error occurred
}

// Function to save rules to Local Storage
export function saveRulesToStorage(rules: UserRule[]): boolean {
   if (typeof window === 'undefined') {
    return false; // Cannot save server-side
  }
  try {
    localStorage.setItem(RULES_STORAGE_KEY, JSON.stringify(rules));
    return true;
  } catch (error) {
    console.error("Error saving rules to Local Storage:", error);
    // Handle potential errors like storage quota exceeded
    alert("Could not save rules. Local storage might be full.");
    return false;
  }
}
