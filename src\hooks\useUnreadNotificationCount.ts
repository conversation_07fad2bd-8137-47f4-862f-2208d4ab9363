'use client';

import { useState, useEffect, useCallback } from 'react';

// Define the shape of a notification object (ensure this matches the one in NotificationsPage)
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'bill' | 'system' | 'info';
  date: string;
  read: boolean;
  billId?: string;
}

const NOTIFICATIONS_STORAGE_KEY = 'notifications';
const READ_STATUS_STORAGE_KEY = 'notification-read-status';
const NOTIFICATIONS_CLEARED_KEY = 'notificationsCleared';

/**
 * Custom hook to get the count of unread notifications from localStorage.
 * Updates when localStorage changes.
 */
export function useUnreadNotificationCount(): number {
  const [unreadCount, setUnreadCount] = useState<number>(0);

  const updateCount = useCallback(() => {
    try {
      const stored = localStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      const readStatusStored = localStorage.getItem(READ_STATUS_STORAGE_KEY);
      const clearedStatus = localStorage.getItem(NOTIFICATIONS_CLEARED_KEY);
      
      // If notifications have been explicitly cleared, show no badge count
      if (clearedStatus === 'true') {
        setUnreadCount(0);
        return;
      }
      
      if (stored) {
        const notifications: Notification[] = JSON.parse(stored);
        const readStatusMap: Record<string, boolean> = readStatusStored ? JSON.parse(readStatusStored) : {};
        
        // Count all bill notifications (both read and unread) plus other unread notifications
        const count = notifications.filter(n => {
          // Include all bill notifications regardless of read status
          if (n.type === 'bill') return true;
          
          // For non-bill notifications, only include if they're unread
          return !readStatusMap[n.id];
        }).length;
        
        setUnreadCount(count);
      } else {
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('Error reading or parsing notifications from localStorage:', error);
      setUnreadCount(0); // Reset count on error
    }
  }, []);

  useEffect(() => {
    // Initial count calculation
    updateCount();

    // Listen for changes in localStorage (e.g., from other tabs or the notifications page itself)
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === NOTIFICATIONS_STORAGE_KEY || 
          event.key === READ_STATUS_STORAGE_KEY || 
          event.key === NOTIFICATIONS_CLEARED_KEY) {
        updateCount();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Cleanup listener on unmount
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [updateCount]);

  return unreadCount;
}
