# Version Management & Changelog System

PayDay Pilot includes a comprehensive version management and changelog notification system that keeps users informed about new features and improvements.

## Overview

The system consists of several components working together:

1. **Version Tracking**: Semantic versioning in `package.json`
2. **Changelog Management**: User-friendly changelog in Markdown format
3. **Update Notifications**: Visual indicators when new updates are available
4. **Settings Integration**: "What's New" section in the Settings panel
5. **Persistent Storage**: Tracks which updates users have viewed

## Components

### Version Utilities (`src/utils/version.ts`)
- Version comparison and parsing functions
- Changelog parsing and formatting
- Storage management for user preferences

### Changelog Notification Hook (`src/hooks/useChangelogNotification.ts`)
- Checks for new changelog entries
- Manages notification state
- Handles marking updates as read

### Update Badge Component (`src/components/ui/UpdateBadge.tsx`)
- Visual notification indicators
- Animated badges for dashboard cards
- Customizable positioning and styling

### Changelog Viewer (`src/components/settings/ChangelogViewer.tsx`)
- Displays changelog entries in user-friendly format
- Supports showing latest or all versions
- Automatic formatting and styling

## Usage

### For Users

1. **Dashboard Notification**: When new updates are available, the Settings card shows a red notification badge
2. **Settings Panel**: Open Settings to see the "What's New" section with latest updates
3. **Automatic Dismissal**: Notifications are automatically marked as read when viewing the Settings panel

### For Developers

#### Updating the Version

Use the built-in npm scripts for semantic versioning:

```bash
# Patch version (0.6.1 → 0.6.2)
npm run bump:patch

# Minor version (0.6.1 → 0.7.0)
npm run bump:minor

# Major version (0.6.1 → 1.0.0)
npm run bump:major

# Custom version
npm run version:bump 0.6.2
```

#### Updating the Changelog

1. Edit `CHANGELOG.md` and `public/CHANGELOG.md` with your changes
2. Follow the established format:

```markdown
## [0.6.2]

**Released: December 19, 2024** ✨

### Added
- New feature descriptions

### Improved
- Enhancement descriptions

### Fixed
- Bug fix descriptions

### Technical
- Technical change descriptions
```

#### Changelog Format Guidelines

- **Use semantic versioning**: `[MAJOR.MINOR.PATCH]`
- **Include release date**: Format as "Month Day, Year"
- **Categorize changes**:
  - `Added`: New features
  - `Improved`: Enhancements to existing features
  - `Fixed`: Bug fixes
  - `Changed`: Changes in existing functionality
  - `Removed`: Removed features
  - `Security`: Security improvements
  - `Technical`: Technical/internal changes
- **Write user-friendly descriptions**: Avoid technical jargon
- **Use action-oriented language**: Start with verbs like "Added", "Fixed", "Improved"

## Storage Keys

The system uses these localStorage keys:

- `payday_pilot_last_viewed_version`: Latest version user has viewed
- `last-viewed-changelog`: Legacy key for backward compatibility
- `current-changelog-version`: Current app version

## File Structure

```
src/
├── utils/version.ts                    # Version utilities
├── hooks/useChangelogNotification.ts   # Notification hook
├── components/
│   ├── ui/UpdateBadge.tsx             # Update indicators
│   └── settings/ChangelogViewer.tsx    # Changelog display
├── app/
│   ├── settings/page.tsx              # Settings with What's New
│   └── dashboard/DashboardHome.tsx     # Dashboard with notifications
scripts/
└── bump-version.js                     # Version bump automation
docs/
└── VERSION_MANAGEMENT.md              # This documentation
CHANGELOG.md                            # Main changelog
public/
└── CHANGELOG.md                        # Public changelog copy
```

## Configuration

### Environment Variables

- `NEXT_PUBLIC_APP_VERSION`: Automatically set from package.json version

### Next.js Configuration

The version is automatically exposed via `next.config.js`:

```javascript
env: {
  NEXT_PUBLIC_APP_VERSION: require('./package.json').version,
}
```

## Best Practices

1. **Always update both changelog files**: `CHANGELOG.md` and `public/CHANGELOG.md`
2. **Write user-focused descriptions**: Explain benefits, not implementation details
3. **Test notifications**: Verify update indicators appear correctly
4. **Use semantic versioning**: Follow semver principles for version numbers
5. **Include release dates**: Help users understand when changes were made
6. **Group related changes**: Organize updates by category for clarity

## Troubleshooting

### Notifications Not Showing
- Check that changelog files are updated
- Verify version format matches `## [X.Y.Z]` pattern
- Clear localStorage to reset notification state

### Version Mismatch
- Ensure `package.json` version matches changelog version
- Run version bump script to sync versions

### Changelog Not Loading
- Verify `public/CHANGELOG.md` exists and is accessible
- Check browser console for fetch errors
- Ensure changelog follows expected format

## Future Enhancements

- Automatic changelog generation from commit messages
- Integration with CI/CD for automated version bumping
- Email notifications for major updates
- Changelog RSS feed
- Version history API endpoint
