#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const MEMORY_LIMIT = '10240';
const PORT = process.env.PORT || '3000';
const CACHE_DIR = path.join(process.cwd(), 'node_modules/.cache');
const STALE_THRESHOLD = 3 * 24 * 60 * 60 * 1000; // 3 days

function ensureCacheDirectory() {
  if (!fs.existsSync(CACHE_DIR)) {
    fs.mkdirSync(CACHE_DIR, { recursive: true });
    console.log('[dev-fast] Created cache directory:', CACHE_DIR);
  }
}

function pruneStaleCaches() {
  if (!fs.existsSync(CACHE_DIR)) {
    return;
  }

  const cutoff = Date.now() - STALE_THRESHOLD;
  let removed = 0;

  for (const entry of fs.readdirSync(CACHE_DIR)) {
    const fullPath = path.join(CACHE_DIR, entry);
    try {
      const info = fs.statSync(fullPath);
      if (info.isDirectory() && info.mtimeMs < cutoff) {
        fs.rmSync(fullPath, { recursive: true, force: true });
        removed += 1;
      }
    } catch (error) {
      console.warn('[dev-fast] Unable to inspect cache entry:', fullPath, error);
    }
  }

  if (removed > 0) {
    console.log(`[dev-fast] Removed ${removed} stale cache directories`);
  }
}

ensureCacheDirectory();
pruneStaleCaches();

const env = {
  ...process.env,
  NODE_ENV: 'development',
  NEXT_TELEMETRY_DISABLED: '1',
  NEXT_SKIP_TSC: '1',
  NEXT_SKIP_PACKAGEJSON_MINIFICATION: '1',
  NEXT_MINIMAL_TRANSPILATION: '1',
  MOCK_FIREBASE: 'true',
  NODE_OPTIONS: `--max-old-space-size=${MEMORY_LIMIT} --no-warnings`,
};

console.log('[dev-fast] Starting Next.js dev server');
const devProcess = spawn('next', ['dev', '-p', PORT, '--turbo'], {
  env,
  stdio: 'inherit',
  shell: true,
});

devProcess.on('close', (code) => {
  process.exit(code ?? 0);
});

process.on('SIGINT', () => {
  devProcess.kill('SIGINT');
  process.exit(0);
});
