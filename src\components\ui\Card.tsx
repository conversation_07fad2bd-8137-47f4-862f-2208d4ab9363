import { cn } from '@/utils/cn';
import { cva, type VariantProps } from 'class-variance-authority';
import { ButtonHTMLAttributes, HTMLAttributes, ReactNode } from 'react';

const cardVariants = cva(
    // Base styles using design system tokens
    [
        'rounded-xl border transition-all duration-200',
        'overflow-hidden',
    ].join(' '),
    {
        variants: {
            variant: {
                // Default card - subtle elevation
                default: [
                    'bg-neutral-0 border-neutral-200 shadow-sm',
                    'dark:bg-neutral-900 dark:border-neutral-800',
                ].join(' '),

                // Elevated card - more prominent
                elevated: [
                    'bg-neutral-0 border-neutral-200 shadow-md',
                    'dark:bg-neutral-900 dark:border-neutral-800',
                    'hover:shadow-lg',
                ].join(' '),

                // Interactive card - clickable states
                interactive: [
                    'bg-neutral-0 border-neutral-200 shadow-sm',
                    'dark:bg-neutral-900 dark:border-neutral-800',
                    'cursor-pointer touch-manipulation',
                    'hover:shadow-lg hover:scale-[1.02] hover:border-neutral-300',
                    'active:scale-[0.98]',
                    'dark:hover:border-neutral-700',
                    'transition-all duration-200',
                ].join(' '),

                // Outline card - minimal styling
                outline: [
                    'bg-transparent border-neutral-200',
                    'dark:border-neutral-800',
                ].join(' '),

                // Glass card - backdrop blur effect
                glass: [
                    'bg-neutral-0/80 border-neutral-200/50 backdrop-blur-sm',
                    'dark:bg-neutral-900/80 dark:border-neutral-800/50',
                    'shadow-lg',
                ].join(' '),
            },
            padding: {
                none: 'p-0',
                sm: 'p-3',
                md: 'p-4',
                lg: 'p-6',
                xl: 'p-8',
            },
        },
        defaultVariants: {
            variant: 'default',
            padding: 'md',
        },
    }
);

const cardHeaderVariants = cva(
    'space-y-1.5',
    {
        variants: {
            padding: {
                none: 'p-0',
                sm: 'p-3 pb-2',
                md: 'p-4 pb-3',
                lg: 'p-6 pb-4',
                xl: 'p-8 pb-6',
            },
        },
        defaultVariants: {
            padding: 'md',
        },
    }
);

const cardContentVariants = cva(
    '',
    {
        variants: {
            padding: {
                none: 'p-0',
                sm: 'p-3 pt-0',
                md: 'p-4 pt-0',
                lg: 'p-6 pt-0',
                xl: 'p-8 pt-0',
            },
        },
        defaultVariants: {
            padding: 'md',
        },
    }
);

const cardFooterVariants = cva(
    'flex items-center',
    {
        variants: {
            padding: {
                none: 'p-0',
                sm: 'p-3 pt-2',
                md: 'p-4 pt-3',
                lg: 'p-6 pt-4',
                xl: 'p-8 pt-6',
            },
        },
        defaultVariants: {
            padding: 'md',
        },
    }
);

export interface CardProps extends VariantProps<typeof cardVariants> {
    children: ReactNode;
    asChild?: boolean;
    className?: string;
    onClick?: (() => void) | undefined;
}

export interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
    children: ReactNode;
    padding?: VariantProps<typeof cardHeaderVariants>['padding'];
}

export interface CardContentProps extends HTMLAttributes<HTMLDivElement> {
    children: ReactNode;
    padding?: VariantProps<typeof cardContentVariants>['padding'];
}

export interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {
    children: ReactNode;
    padding?: VariantProps<typeof cardFooterVariants>['padding'];
}

export interface CardTitleProps {
    children: ReactNode;
    level?: 1 | 2 | 3 | 4 | 5 | 6;
    className?: string;
}

export interface CardDescriptionProps extends HTMLAttributes<HTMLParagraphElement> {
    children: ReactNode;
}

// Main Card Component
export const Card = ({
    className,
    variant,
    padding,
    children,
    onClick,
    ...props
}: CardProps) => {
    const Component = onClick ? 'button' : 'div';
    const baseClassName = cn(cardVariants({ variant, padding }), className);

    if (onClick) {
        return (
            <button
                className={baseClassName}
                onClick={onClick}
                type="button"
                {...(props as ButtonHTMLAttributes<HTMLButtonElement>)}
            >
                {children}
            </button>
        );
    }

    return (
        <div
            className={baseClassName}
            {...(props as HTMLAttributes<HTMLDivElement>)}
        >
            {children}
        </div>
    );
};

// Card Sub-components
export const CardHeader = ({
    className,
    padding = 'md',
    children,
    ...props
}: CardHeaderProps) => (
    <div className={cn(cardHeaderVariants({ padding }), className)} {...props}>
        {children}
    </div>
);

export const CardContent = ({
    className,
    padding = 'md',
    children,
    ...props
}: CardContentProps) => (
    <div className={cn(cardContentVariants({ padding }), className)} {...props}>
        {children}
    </div>
);

export const CardFooter = ({
    className,
    padding = 'md',
    children,
    ...props
}: CardFooterProps) => (
    <div className={cn(cardFooterVariants({ padding }), className)} {...props}>
        {children}
    </div>
);

export const CardTitle = ({
    className,
    level = 3,
    children,
}: CardTitleProps) => {
    const Heading = `h${level}` as keyof JSX.IntrinsicElements;

    return (
        <Heading
            className={cn(
                'heading-lg font-semibold text-neutral-900 dark:text-neutral-100 leading-tight',
                className
            )}
        >
            {children}
        </Heading>
    );
};

export const CardDescription = ({
    className,
    children,
    ...props
}: CardDescriptionProps) => (
    <p
        className={cn(
            'body-sm text-neutral-600 dark:text-neutral-400',
            className
        )}
        {...props}
    >
        {children}
    </p>
);

// Re-export all components for convenience
export {
    cardContentVariants,
    cardFooterVariants, cardHeaderVariants, cardVariants, Card as default
};

