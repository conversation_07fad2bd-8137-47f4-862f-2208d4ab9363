'use client';

import { useBillsStore } from '@/stores/billsStore';
import { formatDate } from '@/utils/date';
import { CheckCircleIcon, ClockIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useBillCalendarContext } from './BillCalendarContext';

// Custom Agenda Event for mobile view with side panel integration
export const BillAgendaEvent = ({ event }: { event: any }) => {
  const router = useRouter();
  const { markBillUnpaid, markBillPaid } = useBillsStore();
  const { openSidePanel } = useBillCalendarContext();

  const isPaid = !!event.resource.paidDate;
  const isOverdue = event.status === 'overdue';
  const isUpcoming = event.status === 'upcoming';
  const isFuture = event.status === 'future';
  const dueDate = new Date(event.resource.dueDate);
  const dueDateFormatted = formatDate(event.resource.dueDate, 'short');
  const daysUntilDue = Math.ceil((dueDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
  const isDueToday = !isPaid && daysUntilDue === 0;
  const dueText = isPaid ? 'Paid' :
    isOverdue ? 'Overdue' :
      isFuture ? `Future (${dueDateFormatted})` :
        isDueToday ? 'Due today' :
          daysUntilDue === 1 ? 'Due tomorrow' :
            `Due in ${daysUntilDue} days`;

  // Theme color mapping
  let cardBg = 'bg-white dark:bg-dark-sepia/10';
  let textColor = 'text-dark-sepia dark:text-white/90';
  let accentColor = 'border-dark-sepia/20 dark:border-white/20'; // Default border
  let statusIcon = null;
  let riskIndicatorColor = 'bg-gray-400'; // Default risk color

  if (isPaid) {
    cardBg = 'bg-white/90 dark:bg-dark-sepia/5';
    textColor = 'text-dark-sepia/70 dark:text-white/60'; // Muted text for paid
    accentColor = 'border-dark-sepia/10 dark:border-white/10';
    statusIcon = <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400" />;
  } else if (isOverdue) {
    accentColor = 'border-muted-red dark:border-muted-red/70'; // Use muted-red for overdue border emphasis
    cardBg = 'bg-white dark:bg-dark-sepia/15';
    textColor = 'text-muted-red dark:text-muted-red';
    statusIcon = <ExclamationCircleIcon className="h-4 w-4 text-muted-red dark:text-muted-red" />;
  } else if (isDueToday) {
    // Special styling for bills due today
    accentColor = 'border-orange-500 dark:border-orange-400'; // Use orange for due today
    cardBg = 'bg-orange-50 dark:bg-orange-900/20';
    textColor = 'text-orange-700 dark:text-orange-300';
    statusIcon = <ExclamationCircleIcon className="h-4 w-4 text-orange-500 dark:text-orange-400" />;
    riskIndicatorColor = 'bg-orange-500'; // Override risk indicator with orange
  } else if (isFuture) {
    // Special styling for future bill instances
    accentColor = 'border-purple-500 dark:border-purple-400'; // Use purple for future bills
    cardBg = 'bg-purple-50 dark:bg-purple-900/20';
    textColor = 'text-purple-700 dark:text-purple-300';
    statusIcon = <ClockIcon className="h-4 w-4 text-purple-500 dark:text-purple-400" />;
    riskIndicatorColor = 'bg-purple-500'; // Override risk indicator with purple
  } else if (isUpcoming) {
    accentColor = 'border-blueprint-blue dark:border-blueprint-blue/70'; // Use blueprint for upcoming
    statusIcon = <ClockIcon className="h-4 w-4 text-blueprint-blue dark:text-blueprint-blue/80" />;
  }

  // Risk Indicator Logic (Example: Higher score = darker/more prominent color)
  if (event.riskScore >= 7) {
    riskIndicatorColor = 'bg-muted-red';
  } else if (event.riskScore >= 4) {
    riskIndicatorColor = 'bg-yellow-500';
  } else {
    riskIndicatorColor = 'bg-blueprint-blue';
  }

  // Open bill in side panel instead of navigating away
  const handleClick = () => {
    openSidePanel(event.resource);
  };

  const handleUnmarkPaid = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await markBillUnpaid(event.resource.id);
      console.log('Bill successfully unmarked as paid:', event.resource.id);
    } catch (error) {
      console.error('Error unmarking bill as paid:', error);
    }
  };

  const handleMarkPaid = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await markBillPaid(event.resource.id);
      console.log('Bill successfully marked as paid:', event.resource.id);
    } catch (error) {
      console.error('Error marking bill as paid:', error);
    }
  };

  return (
    <div
      className={`relative flex flex-col p-3 rounded-md border-l-3 ${accentColor} ${cardBg} mb-2 transition-all duration-200 hover:bg-light-gray/50 dark:hover:bg-dark-sepia/20 cursor-pointer shadow-sm`}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-label={`Bill: ${event.title}, Amount: ${event.resource.amount.toFixed(2)}, Due: ${dueDateFormatted}, Status: ${event.status}`}
    >
      {/* Risk Indicator Dot */}
      <span className={`absolute top-3 right-3 h-2 w-2 rounded-full ${riskIndicatorColor}`}
        title={`Risk Score: ${event.riskScore}/10`}></span>

      <div className="flex justify-between items-start">
        <div className={`font-serif font-medium text-sm ${textColor} truncate pr-4 w-7/12`}>{event.title}</div>
        <div className={`font-mono text-sm ${textColor} w-4/12 text-right`}>${event.resource.amount.toFixed(2)}</div>
      </div>

      <div className="flex justify-between items-center text-xs mt-2">
        <div className={`flex items-center gap-1 ${textColor} opacity-80 dark:opacity-70`}>
          {statusIcon && <span className="w-3 h-3">{statusIcon}</span>}
          <span className={`${isOverdue ? 'text-muted-red dark:text-muted-red' : isPaid ? 'text-green-600 dark:text-green-400' : 'text-dark-sepia/90 dark:text-white/80'}`}>
            {dueText}
          </span>
        </div>

        {isPaid ? (
          <button
            className={`px-2 py-0.5 text-xs rounded-full border border-dark-sepia/30 dark:border-white/20 ${textColor} opacity-70 hover:opacity-100 hover:bg-light-bg dark:hover:bg-dark-sepia/20 transition-colors`}
            onClick={handleUnmarkPaid}
            aria-label={`Unmark ${event.title} as paid`}
          >
            Undo
          </button>
        ) : isFuture ? (
          <span className="px-2 py-0.5 text-xs rounded-full bg-purple-100 dark:bg-purple-900/30 border border-purple-300 dark:border-purple-600 text-purple-600 dark:text-purple-400">
            Projected
          </span>
        ) : (
          <button
            className="px-2 py-0.5 text-xs rounded-full bg-blueprint-blue/10 border border-blueprint-blue/30 text-blueprint-blue dark:text-blueprint-blue hover:bg-blueprint-blue/20 transition-colors"
            onClick={handleMarkPaid}
            aria-label={`Mark ${event.title} as paid`}
          >
            Pay Now
          </button>
        )}
      </div>
    </div>
  );
};

export default BillAgendaEvent;
