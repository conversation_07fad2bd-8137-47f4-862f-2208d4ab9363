import { DashboardIcons } from '@/components/ui/Icon';
import { cn } from '@/utils/cn';
import { ReactNode } from 'react';

interface DashboardCardContentProps {
  icon: ReactNode;
  title: string;
  subtitle?: string;
  variant?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
  className?: string;
  children?: ReactNode;
}

// Consistent icon color mapping
const iconColorMap = {
  primary: 'text-primary-500',
  secondary: 'text-secondary-500',
  accent: 'text-accent-500',
  info: 'text-info-500',
  success: 'text-success-500',
  warning: 'text-warning-500',
  error: 'text-error-500'
};

// Consistent text styling with enhanced accessibility and readability
const titleClasses = 'text-sm sm:text-base font-bold text-center text-contrast-primary leading-tight card-title-safe dashboard-card-text mobile-text-enhanced px-2';
const subtitleClasses = 'text-xs sm:text-sm text-contrast-secondary text-center leading-tight card-subtitle-safe dashboard-card-subtitle mobile-text-enhanced px-2 font-medium';

export function DashboardCardContent({
  icon,
  title,
  subtitle,
  variant = 'primary',
  className = '',
  children
}: DashboardCardContentProps) {
  return (
    <div className={cn(
      'p-3 sm:p-4 text-center h-full flex flex-col justify-center items-center space-y-2 sm:space-y-3 dashboard-card-content',
      className
    )}>
      {/* Icon with consistent sizing and color */}
      <div className={cn('w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center flex-shrink-0', iconColorMap[variant])}>
        {icon}
      </div>

      {/* Text content with consistent styling and improved readability */}
      <div className="space-y-1 flex-1 flex flex-col justify-center min-h-0 w-full">
        <p className={titleClasses}>
          {title}
        </p>
        {subtitle && (
          <p className={subtitleClasses}>
            {subtitle}
          </p>
        )}
        {children && (
          <div className="mt-1 text-center">
            {children}
          </div>
        )}
      </div>
    </div>
  );
}

// Preset card content components for common dashboard cards
export function AddBillCardContent() {
  return (
    <DashboardCardContent
      icon={<DashboardIcons.AddBill />}
      title="Add New Bill"
      subtitle="Bills, loans & debts"
      variant="primary"
    />
  );
}

export function CalendarCardContent() {
  return (
    <DashboardCardContent
      icon={<DashboardIcons.Calendar />}
      title="Calendar"
      subtitle="View bill due dates"
      variant="accent"
    />
  );
}

export function SettingsCardContent({ hasUpdate = false }: { hasUpdate?: boolean }) {
  return (
    <DashboardCardContent
      icon={<DashboardIcons.Settings />}
      title="Settings"
      subtitle="Manage preferences"
      variant="secondary"
    >
      {hasUpdate && (
        <div className="text-xs text-error-600 dark:text-error-400 font-medium">
          New updates available!
        </div>
      )}
    </DashboardCardContent>
  );
}

export function NotificationsCardContent() {
  return (
    <DashboardCardContent
      icon={<DashboardIcons.Notifications />}
      title="Notifications"
      subtitle="Recent alerts"
      variant="info"
    />
  );
}

export function AIAssistantCardContent() {
  return (
    <DashboardCardContent
      icon={<DashboardIcons.AIAssistant />}
      title="AI Assistant"
      subtitle="Smart insights"
      variant="secondary"
    />
  );
}

export function BillsOverviewCardContent() {
  return (
    <DashboardCardContent
      icon={<DashboardIcons.BillsOverview />}
      title="Bills Overview"
      subtitle="All your bills"
      variant="info"
    />
  );
}

export function CalculatorCardContent() {
  return (
    <DashboardCardContent
      icon={<DashboardIcons.Calculator />}
      title="Financial Calculator"
      subtitle="Calculate payments"
      variant="success"
    />
  );
}
