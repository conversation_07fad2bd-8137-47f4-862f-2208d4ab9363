// src/lib/transactionsStore.ts
import { Transaction } from '@/types/financial';

// --- IMPORTANT ---
// This is in-memory storage for DEMONSTRATION ONLY.
// Replace this with a real database connection (e.g., Prisma, Supabase client, etc.)
// In a production app, you would use a real database instead of this in-memory store
// --- IMPORTANT ---

// Using a closure to maintain state while keeping it private
const createTransactionsStore = () => {
  // Private transactions array
  const transactions: Transaction[] = [];
  
  return {
    // Get all transactions
    getTransactions: (): Transaction[] => {
      return [...transactions]; // Return a copy to prevent direct mutation
    },
    
    // Add a new transaction
    addTransaction: (transaction: Transaction): void => {
      transactions.push(transaction);
    },
    
    // Get a transaction by ID
    getTransactionById: (id: string): Transaction | undefined => {
      return transactions.find((transaction: Transaction) => transaction.id === id);
    },
    
    // Update a transaction
    updateTransaction: (id: string, updatedTransaction: Partial<Transaction>): Transaction | undefined => {
      const index = transactions.findIndex((transaction: Transaction) => transaction.id === id);
      if (index === -1) return undefined;
      
      transactions[index] = { 
        ...transactions[index], 
        ...updatedTransaction, 
        updatedAt: new Date().toISOString() 
      };
      
      return { ...transactions[index] }; // Return a copy
    },
    
    // Delete a transaction
    deleteTransaction: (id: string): boolean => {
      const initialLength = transactions.length;
      const newTransactions = transactions.filter((transaction: Transaction) => transaction.id !== id);
      
      // Only update if something was actually removed
      if (newTransactions.length !== initialLength) {
        transactions.splice(0, transactions.length, ...newTransactions);
        return true;
      }
      
      return false;
    }
  };
};

// Create and export a singleton instance
const transactionsStore = createTransactionsStore();
export default transactionsStore;
