/* Consistent Spacing System - 8pt Grid Implementation */

/* Fix heading clipping issues */
.heading-safe {
  padding-top: var(--space-2);
  padding-bottom: var(--space-2);
  line-height: 1.2;
  word-break: break-word;
  overflow-wrap: break-word;
}

/* Prevent text overflow in cards */
.text-safe {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  hyphens: auto;
}

/* Consistent spacing utilities using 8pt grid */
.space-8pt-xs { margin: var(--space-1); }      /* 4px */
.space-8pt-sm { margin: var(--space-2); }      /* 8px */
.space-8pt-md { margin: var(--space-4); }      /* 16px */
.space-8pt-lg { margin: var(--space-6); }      /* 24px */
.space-8pt-xl { margin: var(--space-8); }      /* 32px */

.gap-8pt-xs { gap: var(--space-1); }           /* 4px */
.gap-8pt-sm { gap: var(--space-2); }           /* 8px */
.gap-8pt-md { gap: var(--space-4); }           /* 16px */
.gap-8pt-lg { gap: var(--space-6); }           /* 24px */
.gap-8pt-xl { gap: var(--space-8); }           /* 32px */

.padding-8pt-xs { padding: var(--space-1); }   /* 4px */
.padding-8pt-sm { padding: var(--space-2); }   /* 8px */
.padding-8pt-md { padding: var(--space-4); }   /* 16px */
.padding-8pt-lg { padding: var(--space-6); }   /* 24px */
.padding-8pt-xl { padding: var(--space-8); }   /* 32px */

/* Fix header clipping on scroll */
.header-safe {
  position: sticky;
  top: 0;
  z-index: 30;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Ensure content doesn't get clipped behind fixed headers */
.content-with-header {
  padding-top: calc(4rem + var(--space-4)); /* Header height + spacing */
}

/* Footer alignment improvements */
.footer-aligned {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  padding: var(--space-6) var(--space-4);
}

.footer-icons {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.footer-icon {
  width: var(--space-6);
  height: var(--space-6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.footer-icon:hover {
  background-color: var(--neutral-100);
  transform: translateY(-1px);
}

.dark .footer-icon:hover {
  background-color: var(--neutral-800);
}

/* Dashboard card spacing consistency */
.dashboard-spacing {
  padding: var(--space-6);
  gap: var(--space-6);
}

@media (max-width: 640px) {
  .dashboard-spacing {
    padding: var(--space-4);
    gap: var(--space-4);
  }
}

/* Mobile-specific spacing fixes */
@media (max-width: 767px) {
  /* Fix mobile header spacing */
  .mobile-header-spacing {
    padding-top: env(safe-area-inset-top, var(--space-4));
    padding-bottom: var(--space-6);
    margin-bottom: var(--space-6);
  }
  
  /* Fix mobile content spacing */
  .mobile-content-spacing {
    padding-left: var(--space-4);
    padding-right: var(--space-4);
    padding-bottom: calc(7rem + env(safe-area-inset-bottom, var(--space-4)));
  }
  
  /* Fix mobile section spacing */
  .mobile-section-spacing > * + * {
    margin-top: var(--space-6);
  }
  
  /* Fix mobile card spacing */
  .mobile-card-spacing {
    margin-bottom: var(--space-4);
    padding: var(--space-4);
    border-radius: var(--radius-xl);
  }
  
  /* Fix mobile navigation spacing */
  .mobile-nav-spacing {
    padding: var(--space-4) var(--space-2);
    gap: var(--space-2);
  }
}

/* Text overflow prevention */
.text-overflow-safe {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.text-multiline-safe {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  word-break: break-word;
  hyphens: auto;
}

/* Consistent button spacing */
.button-spacing {
  padding: var(--space-3) var(--space-6);
  gap: var(--space-2);
  border-radius: var(--radius-lg);
}

.button-spacing.small {
  padding: var(--space-2) var(--space-4);
  gap: var(--space-1);
  border-radius: var(--radius-md);
}

.button-spacing.large {
  padding: var(--space-4) var(--space-8);
  gap: var(--space-3);
  border-radius: var(--radius-xl);
}

/* Form spacing consistency */
.form-spacing {
  gap: var(--space-4);
}

.form-field-spacing {
  margin-bottom: var(--space-4);
}

.form-field-spacing label {
  margin-bottom: var(--space-2);
  display: block;
}

.form-field-spacing input,
.form-field-spacing select,
.form-field-spacing textarea {
  padding: var(--space-3);
  border-radius: var(--radius-md);
}

/* Modal spacing consistency */
.modal-spacing {
  padding: var(--space-6);
  gap: var(--space-4);
}

.modal-header-spacing {
  padding-bottom: var(--space-4);
  margin-bottom: var(--space-4);
  border-bottom: 1px solid var(--neutral-200);
}

.dark .modal-header-spacing {
  border-bottom-color: var(--neutral-700);
}

.modal-footer-spacing {
  padding-top: var(--space-4);
  margin-top: var(--space-4);
  border-top: 1px solid var(--neutral-200);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
}

.dark .modal-footer-spacing {
  border-top-color: var(--neutral-700);
}

/* Card content spacing */
.card-content-spacing {
  padding: var(--space-5);
  gap: var(--space-3);
}

.card-header-spacing {
  margin-bottom: var(--space-4);
}

.card-footer-spacing {
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--neutral-200);
}

.dark .card-footer-spacing {
  border-top-color: var(--neutral-700);
}

/* List spacing consistency */
.list-spacing {
  gap: var(--space-3);
}

.list-item-spacing {
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
}

/* Navigation spacing */
.nav-spacing {
  gap: var(--space-6);
  padding: var(--space-4) var(--space-6);
}

.nav-item-spacing {
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  gap: var(--space-2);
}

/* Utility classes for common spacing patterns */
.stack-spacing > * + * {
  margin-top: var(--space-4);
}

.stack-spacing-sm > * + * {
  margin-top: var(--space-2);
}

.stack-spacing-lg > * + * {
  margin-top: var(--space-6);
}

.inline-spacing {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.inline-spacing-sm {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.inline-spacing-lg {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}
