import type { <PERSON><PERSON>, <PERSON>lerConte<PERSON><PERSON>, <PERSON>lerEvent, HandlerResponse } from "@netlify/functions";
import * as admin from "firebase-admin";
import { db, fcm } from "./utils/firebase-admin";

// Define Bill interface (adjust based on your actual Firestore structure)
interface Bill {
  id: string;
  name?: string;
  dueDate: admin.firestore.Timestamp;
  status: string;
  // Add other relevant fields
}

const handler: Handler = async (event: HandlerEvent, context: HandlerContext): Promise<HandlerResponse> => {
  console.log(`Scheduled function 'send-due-date-notifications' triggered at ${new Date().toISOString()}`);

  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of today

    const twoDaysFromNow = new Date(today);
    twoDaysFromNow.setDate(today.getDate() + 3);
    twoDaysFromNow.setMilliseconds(twoDaysFromNow.getMilliseconds() - 1);

    const todayTimestamp = admin.firestore.Timestamp.fromDate(today);
    const twoDaysTimestamp = admin.firestore.Timestamp.fromDate(twoDaysFromNow);

    console.log(`Querying for bills due between ${today.toISOString()} and ${twoDaysFromNow.toISOString()}`);

    const usersSnapshot = await db.collection('users').get();
    if (usersSnapshot.empty) {
      console.log("No users found in the database.");
      return { statusCode: 200, body: "No users found." };
    }

    console.log(`Processing ${usersSnapshot.size} users.`);
    let usersWithDueBills = 0;
    let notificationsAttempted = 0;
    let tokensProcessed = 0;
    let successfulSends = 0;
    let failedSends = 0;
    let tokensRemoved = 0;

    for (const userDoc of usersSnapshot.docs) {
      const userId = userDoc.id;
      const userData = userDoc.data();

      const billsSnapshot = await db.collection('users').doc(userId).collection('bills')
        .where('dueDate', '>=', todayTimestamp)
        .where('dueDate', '<=', twoDaysTimestamp)
        .where('status', '!=', 'paid')
        .get();

      if (billsSnapshot.empty) {
        continue;
      }

      usersWithDueBills++;
      const dueBills: Bill[] = billsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Bill));
      console.log(`Found ${dueBills.length} bills due soon for user: ${userId}.`);

      const fcmTokens: string[] = userData?.fcmTokens || [];
      if (fcmTokens.length === 0) {
        console.log(`No FCM tokens found for user ${userId}, cannot send notification.`);
        continue;
      }

      tokensProcessed += fcmTokens.length;
      notificationsAttempted++;

      const billsSummary = dueBills.length === 1
        ? `Your bill "${dueBills[0].name || 'Unnamed Bill'}" is due soon.`
        : `${dueBills.length} bills are due soon. Check PayDay Pilot.`;

      const payload: admin.messaging.MessagingPayload = {
        notification: {
          title: 'Upcoming Bill Reminder',
          body: billsSummary,
        },
        data: {
          url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://payday-pilot.netlify.app'}/bills`,
          icon: '/icons/icon-192x192.png',
          badge: '/icons/icon-72x72.svg'
        }
      };

      if (fcmTokens.length > 0) {
        try {
          const response: admin.messaging.MessagingDevicesResponse = await fcm.sendToDevice(
            fcmTokens,
            payload,
            { timeToLive: 86400 } // 1 day
          );

          successfulSends += response.successCount;
          failedSends += response.failureCount;

          if (response.failureCount > 0) {
            const tokensToDelete: string[] = [];
            response.results.forEach((result, index) => {
              if (!result.success) {
                const error = result.error;
                console.error(`Failure sending notification to token ${fcmTokens[index].substring(0,20)}... for user ${userId}: ${error?.code}`);
                if (error && (error.code === 'messaging/invalid-registration-token' || error.code === 'messaging/registration-token-not-registered')) {
                  tokensToDelete.push(fcmTokens[index]);
                }
              }
            });

            if (tokensToDelete.length > 0) {
              console.log(`Attempting to remove ${tokensToDelete.length} invalid FCM tokens for user ${userId}.`);
              const userRef = db.collection('users').doc(userId);
              await userRef.update({
                fcmTokens: admin.firestore.FieldValue.arrayRemove(...tokensToDelete)
              });
              tokensRemoved += tokensToDelete.length;
              console.log(`Removed ${tokensToDelete.length} invalid FCM tokens for user ${userId}.`);
            }
          }
        } catch (error) {
          console.error(`Critical error sending FCM message for user ${userId}:`, error);
          failedSends += fcmTokens.length;
        }
      }
    }

    // Wait for all notifications to be sent/processed (sendToDevice is async but we await it in loop)
    // Promise.allSettled is not strictly needed here anymore as sendToDevice is awaited.
    console.log("Finished processing notifications.");
    console.log(`Summary: Users processed: ${usersSnapshot.size}, Users with due bills: ${usersWithDueBills}, Notifications Attempted (users): ${notificationsAttempted}, Tokens Processed: ${tokensProcessed}, Successful Sends: ${successfulSends}, Failed Sends: ${failedSends}, Tokens Removed: ${tokensRemoved}`);

    return { statusCode: 200, body: `Notifications processed. Successful: ${successfulSends}, Failed: ${failedSends}, Tokens Removed: ${tokensRemoved}.` };

  } catch (error) {
    console.error("Critical error in send-due-date-notifications handler:", error);
    // Check if error is an object with a message property
    const errorMessage = (error instanceof Error) ? error.message : String(error);
    return { statusCode: 500, body: `Internal Server Error: ${errorMessage}` };
  }
};

export { handler };
