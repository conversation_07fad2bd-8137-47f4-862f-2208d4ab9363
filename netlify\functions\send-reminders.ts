// netlify/functions/send-reminders.ts
import type { <PERSON><PERSON>, HandlerContext, HandlerEvent } from "@netlify/functions";
import { Timestamp } from "firebase-admin/firestore"; // Import Timestamp
import webPush from "web-push";
import { db } from "./utils/firebase-admin"; // Import Firestore db

// Define the structure of your Bill data in Firestore
interface Bill {
  id: string; // Firestore document ID
  userId: string;
  name: string;
  dueDate: Timestamp; // Assuming dueDate is stored as Firestore Timestamp
  // ... other bill properties
}

// Define the structure for subscription data stored in Firestore
interface StoredSubscription {
  userId: string;
  keys: { // This matches the PushSubscription keys structure
    p256dh: string;
    auth: string;
  };
  // Optional: createdAt: Timestamp;
}

/**
 * Fetches bills from Firestore that are due tomorrow.
 */
async function getBillsDueTomorrow(): Promise<Bill[]> {
  const now = new Date();
  // Calculate start of tomorrow (midnight)
  const startOfTomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
  startOfTomorrow.setHours(0, 0, 0, 0);
  // Calculate end of tomorrow (just before midnight)
  const endOfTomorrow = new Date(startOfTomorrow);
  endOfTomorrow.setHours(23, 59, 59, 999);

  // Convert to Firestore Timestamps for querying
  const startTimestamp = Timestamp.fromDate(startOfTomorrow);
  const endTimestamp = Timestamp.fromDate(endOfTomorrow);

  console.log(`Querying bills due between ${startOfTomorrow.toISOString()} and ${endOfTomorrow.toISOString()}`);

  try {
    const billsRef = db.collection('bills');
    // Query for bills where dueDate is within tomorrow's range
    const snapshot = await billsRef
      .where('dueDate', '>=', startTimestamp)
      .where('dueDate', '<=', endTimestamp)
      .get();

    if (snapshot.empty) {
      console.log('No bills found due tomorrow.');
      return [];
    }

    const bills: Bill[] = [];
    snapshot.forEach((doc: FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>) => {
      // Ensure dueDate exists and is a Timestamp before accessing toDate()
      const data = doc.data();
      if (data.dueDate && data.dueDate instanceof Timestamp) {
        bills.push({ id: doc.id, ...data } as Bill);
      } else {
        console.warn(`Bill ${doc.id} skipped due to missing or invalid dueDate.`);
      }
    });
    console.log(`Found ${bills.length} bills due tomorrow.`);
    return bills;
  } catch (error) {
    console.error('Error fetching bills due tomorrow:', error);
    return []; // Return empty array on error
  }
}

/**
 * Fetches bills from Firestore that are overdue (due date before today and not marked as paid).
 */
async function getOverdueBills(): Promise<Bill[]> {
  const now = new Date();
  now.setHours(0, 0, 0, 0); // Start of today
  const todayTimestamp = Timestamp.fromDate(now);
  try {
    const billsRef = db.collection('bills');
    const snapshot = await billsRef
      .where('dueDate', '<', todayTimestamp)
      .where('isPaid', '!=', true)
      .get();
    if (snapshot.empty) {
      return [];
    }
    const bills: Bill[] = [];
    snapshot.forEach((doc: FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>) => {
      const data = doc.data();
      if (data.dueDate && data.dueDate instanceof Timestamp) {
        bills.push({ id: doc.id, ...data } as Bill);
      }
    });
    return bills;
  } catch (error) {
    console.error('Error fetching overdue bills:', error);
    return [];
  }
}

/**
 * Fetches push subscriptions for a specific user from Firestore.
 */
async function getSubscriptionsForUser(userId: string): Promise<webPush.PushSubscription[]> {
  if (!userId) {
    console.warn('getSubscriptionsForUser called without userId');
    return [];
  }

  try {
    const subsRef = db.collection('pushSubscriptions');
    // Query for subscriptions matching the userId
    const snapshot = await subsRef.where('userId', '==', userId).get();

    if (snapshot.empty) {
      console.log(`No subscriptions found for user: ${userId}`);
      return [];
    }

    const subscriptions: webPush.PushSubscription[] = [];
    snapshot.forEach((doc: FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>) => {
      const data = doc.data() as StoredSubscription;
      // Construct the full PushSubscription object needed by web-push
      // The document ID is the endpoint
      if (doc.id && data.keys?.p256dh && data.keys?.auth) { // Basic validation
        subscriptions.push({
          endpoint: doc.id,
          keys: {
            p256dh: data.keys.p256dh,
            auth: data.keys.auth,
          },
        });
      } else {
        console.warn(`Subscription document ${doc.id} for user ${userId} has invalid structure.`);
      }
    });
    console.log(`Found ${subscriptions.length} subscriptions for user: ${userId}`);
    return subscriptions;
  } catch (error) {
    console.error(`Error fetching subscriptions for user ${userId}:`, error);
    return []; // Return empty array on error
  }
}

const handler: Handler = async (event: HandlerEvent, context: HandlerContext) => {
  console.log("Running send-reminders scheduled function...");

  const vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;
  const vapidPrivateKey = process.env.VAPID_PRIVATE_KEY;

  if (!vapidPublicKey || !vapidPrivateKey) {
    console.error("VAPID keys are not configured in environment variables.");
    return { statusCode: 500, body: "VAPID keys not configured." };
  }

  webPush.setVapidDetails(
    'mailto:<EMAIL>', // User's correct email address
    vapidPublicKey,
    vapidPrivateKey
  );

  try {
    const billsDue = await getBillsDueTomorrow();
    const overdueBills = await getOverdueBills();
    console.log(`Found ${billsDue.length} bills due tomorrow.`);
    console.log(`Found ${overdueBills.length} overdue bills.`);

    if (billsDue.length === 0 && overdueBills.length === 0) {
      return { statusCode: 200, body: "No bills due tomorrow or overdue." };
    }

    let notificationsSent = 0;
    let notificationsFailed = 0;

    // Process each bill due tomorrow
    for (const bill of billsDue) {
      const userSubscriptions = await getSubscriptionsForUser(bill.userId);
      console.log(`Found ${userSubscriptions.length} subscriptions for user ${bill.userId}.`);

      if (userSubscriptions.length === 0) continue;

      const payload = JSON.stringify({
        title: `Bill Reminder: ${bill.name}`,
        body: `Your bill "${bill.name}" is due tomorrow (${bill.dueDate.toDate().toLocaleDateString()})!`,
        icon: '/icons/icon-192x192.svg',
        // data: { url: `/bills/${bill.id}` } // Optional: Add data for click action
      });

      // Send notification to each subscription for the user
      for (const userSub of userSubscriptions) {
        try {
          console.log(`Sending notification for bill ${bill.id} to endpoint: ${userSub.endpoint.substring(0, 30)}...`);
          await webPush.sendNotification(userSub, payload);
          notificationsSent++;
        } catch (error: any) {
          notificationsFailed++;
          console.error(`Error sending notification to ${userSub.endpoint.substring(0, 30)}:`, error.statusCode, error.body);
          // TODO: Handle specific errors, e.g., 404 or 410 indicate the subscription is invalid and should be removed from your DB.
          // if (error.statusCode === 404 || error.statusCode === 410) {
          //   await db.removeSubscription(userSub.subscription.endpoint);
          // }
        }
      }
    }

    // Process each overdue bill
    for (const bill of overdueBills) {
      const userSubscriptions = await getSubscriptionsForUser(bill.userId);
      console.log(`Found ${userSubscriptions.length} subscriptions for user ${bill.userId}.`);

      if (userSubscriptions.length === 0) continue;

      const payload = JSON.stringify({
        title: `Overdue Bill: ${bill.name}`,
        body: `Your bill "${bill.name}" was due on ${bill.dueDate.toDate().toLocaleDateString()} and is still unpaid!`,
        icon: '/icons/icon-192x192.svg',
      });

      // Send notification to each subscription for the user
      for (const userSub of userSubscriptions) {
        try {
          console.log(`Sending notification for overdue bill ${bill.id} to endpoint: ${userSub.endpoint.substring(0, 30)}...`);
          await webPush.sendNotification(userSub, payload);
          notificationsSent++;
        } catch (error: any) {
          notificationsFailed++;
          console.error(`Error sending notification to ${userSub.endpoint.substring(0, 30)}:`, error.statusCode, error.body);
          // TODO: Handle specific errors, e.g., 404 or 410 indicate the subscription is invalid and should be removed from your DB.
          // if (error.statusCode === 404 || error.statusCode === 410) {
          //   await db.removeSubscription(userSub.subscription.endpoint);
          // }
        }
      }
    }

    const message = `Finished sending reminders. Sent: ${notificationsSent}, Failed: ${notificationsFailed}.`;
    console.log(message);
    return { statusCode: 200, body: message };

  } catch (error) {
    console.error("Error in send-reminders function:", error);
    return { statusCode: 500, body: "Internal server error." };
  }
};

export { handler };
