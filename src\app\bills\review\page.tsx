'use client';
import { useBillsStore } from '@/stores/billsStore';
import { extractAmountFromDescription, extractDueDateFromString, suggestCategory, suggestTags } from '@/utils/billIntelligence';
import { detectDuplicates } from '@/utils/importUtils';
import { useMemo, useState } from 'react';

export default function BillReviewPage() {
    const { bills, updateBill, isLoading } = useBillsStore();
    const [fixing, setFixing] = useState<string | null>(null);
    const [fixedIds, setFixedIds] = useState<string[]>([]);

    // Analyze all bills for AI suggestions
    const suggestions = useMemo(() => {
        if (!bills) return [];
        return bills.flatMap(bill => {
            const fixes = [];
            // 1. Category suggestion
            const aiCategory = suggestCategory(bill.name || '');
            if ((!bill.category || bill.category === 'Other') && aiCategory && aiCategory !== bill.category) {
                fixes.push({ type: 'category', bill, suggestion: aiCategory });
            }
            // 2. Tag suggestion
            const aiTags = suggestTags(bill.name, bill.notes, bill.category, bill.isRecurring);
            // Bill may not have tags property, so treat as empty array if missing
            const billTags = (bill as any).tags ?? [];
            if (aiTags.length > 0 && aiTags.some(tag => !billTags.includes(tag))) {
                fixes.push({ type: 'tags', bill, suggestion: aiTags });
            }
            // 3. Due date extraction
            const aiDueDate = extractDueDateFromString(bill.name || bill.notes || '');
            if ((!bill.dueDate || bill.dueDate === '') && aiDueDate) {
                fixes.push({ type: 'dueDate', bill, suggestion: aiDueDate });
            }
            // 4. Amount extraction
            const aiAmount = extractAmountFromDescription(bill.name || bill.notes || '');
            if ((!bill.amount || bill.amount === 0) && aiAmount) {
                fixes.push({ type: 'amount', bill, suggestion: aiAmount });
            }
            // 5. Duplicate detection
            const dupes = detectDuplicates(bill, bills.filter(b => b.id !== bill.id));
            if (dupes.length > 0) {
                fixes.push({ type: 'duplicate', bill, suggestion: dupes[0].bill });
            }
            // 6. Recurring pattern detection (simple: if name contains month or 'every')
            if (!bill.isRecurring && /(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec|every|monthly|weekly|biweekly)/i.test(bill.name + ' ' + (bill.notes || ''))) {
                fixes.push({ type: 'recurring', bill, suggestion: true });
            }
            // 7. Anomaly detection (amount outlier)
            const similar = bills.filter(b => b.name === bill.name && b.id !== bill.id);
            if (similar.length > 2) {
                const avg = similar.reduce((sum, b) => sum + (b.amount || 0), 0) / similar.length;
                if (bill.amount > avg * 2 || bill.amount < avg * 0.5) {
                    fixes.push({ type: 'anomaly', bill, suggestion: avg });
                }
            }
            return fixes;
        });
    }, [bills]);

    // Handler to apply a single fix
    const handleApplyFix = async (fix: any) => {
        setFixing(fix.bill.id + fix.type);
        let update: any = {};
        if (fix.type === 'category') update.category = fix.suggestion;
        if (fix.type === 'tags') update.tags = fix.suggestion;
        if (fix.type === 'dueDate') update.dueDate = fix.suggestion;
        if (fix.type === 'amount') update.amount = fix.suggestion;
        if (fix.type === 'recurring') update.isRecurring = true;
        // For anomaly, offer to set to average
        if (fix.type === 'anomaly') update.amount = fix.suggestion;
        // For duplicate, could merge or flag (here, just flag)
        if (fix.type === 'duplicate') update.notes = (fix.bill.notes || '') + ` [Possible duplicate of ${fix.suggestion.name}]`;
        await updateBill(fix.bill.id, update);
        setFixedIds(ids => [...ids, fix.bill.id + fix.type]);
        setFixing(null);
    };

    // Handler to apply all fixes
    const handleApplyAll = async () => {
        for (const fix of suggestions) {
            if (!fixedIds.includes(fix.bill.id + fix.type)) {
                await handleApplyFix(fix);
            }
        }
    };

    if (isLoading) return <div className="p-8 text-center">Loading bills...</div>;

    return (
        <div className="max-w-3xl mx-auto p-6">
            <h1 className="text-2xl font-bold mb-4">AI Review & Fix My Bills</h1>
            <p className="mb-6 text-gray-600 dark:text-gray-300">Below are AI-suggested improvements for your bills. You can apply fixes individually or all at once.</p>
            {suggestions.length === 0 ? (
                <div className="p-6 text-center text-green-600 dark:text-green-400">No issues found! Your bills look great.</div>
            ) : (
                <>
                    <button
                        className="mb-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark disabled:opacity-50"
                        onClick={handleApplyAll}
                        disabled={fixing !== null}
                    >
                        Apply All Fixes
                    </button>
                    <ul className="space-y-4">
                        {suggestions.map((fix, idx) => (
                            <li key={fix.bill.id + fix.type} className="p-4 bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
                                <div className="font-semibold">{fix.bill.name}</div>
                                <div className="text-sm mt-1">
                                    {fix.type === 'category' && typeof fix.suggestion === 'string' && <>Suggest category: <b>{fix.suggestion}</b></>}
                                    {fix.type === 'tags' && Array.isArray(fix.suggestion) && <>Suggest tags: <b>{fix.suggestion.join(', ')}</b></>}
                                    {fix.type === 'dueDate' && typeof fix.suggestion === 'string' && <>Suggest due date: <b>{fix.suggestion}</b></>}
                                    {fix.type === 'amount' && typeof fix.suggestion === 'number' && <>Suggest amount: <b>${fix.suggestion}</b></>}
                                    {fix.type === 'duplicate' && typeof fix.suggestion === 'object' && fix.suggestion && 'name' in fix.suggestion && <>Possible duplicate: <b>{fix.suggestion.name}</b></>}
                                    {fix.type === 'recurring' && <>Suggest marking as <b>recurring</b></>}
                                    {fix.type === 'anomaly' && typeof fix.suggestion === 'number' && <>Amount is an outlier. Typical: <b>${fix.suggestion.toFixed(2)}</b></>}
                                </div>
                                <button
                                    className="mt-2 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                                    onClick={() => handleApplyFix(fix)}
                                    disabled={fixing === fix.bill.id + fix.type || fixedIds.includes(fix.bill.id + fix.type)}
                                >
                                    {fixedIds.includes(fix.bill.id + fix.type) ? 'Fixed' : fixing === fix.bill.id + fix.type ? 'Fixing...' : 'Apply Fix'}
                                </button>
                            </li>
                        ))}
                    </ul>
                </>
            )}
        </div>
    );
}