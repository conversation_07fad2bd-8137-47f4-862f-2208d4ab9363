'use client';

import { useFinancialStore } from '@/stores/financialStore';
import { Bill } from '@/types/bill';
import { getBillUrgency, predictNextBillDate, suggestCategory, suggestTags } from '@/utils/billIntelligence';
import { formatDate, parseLocalDateString } from '@/utils/date';
import { analyzeBillPatterns, generatePaymentRecommendations, predictUpcomingBills } from '@/utils/financialIntelligence';
import { memo, useCallback, useMemo, useState } from 'react';

// Memoized SVG Calendar icon
const CalendarIcon = memo(() => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-6 w-6 mr-2 text-primary text-base"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
    aria-hidden="true"
  >
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
));

// Add display name
CalendarIcon.displayName = 'CalendarIcon';

// Memoized component for rendering individual upcoming bill items
const UpcomingBillItem = memo(({ bill, onMarkPaid }: { bill: Bill; onMarkPaid: (id: string) => void }) => {
  // Memoize the handler to prevent recreation on each render
  const handleMarkPaid = useCallback(() => {
    onMarkPaid(bill.id);
  }, [bill.id, onMarkPaid]);

  return (
    <div key={bill.id} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700">
      <div>
        <h3 className="font-medium">{bill.name}</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Due {formatDate(bill.dueDate, 'relative')}
        </p>
      </div>
      <div className="text-right">
        <p className="font-medium">${bill.amount.toFixed(2)}</p>
        <p className="text-xs text-gray-500">{formatDate(bill.dueDate, 'short')}</p>
        <button
          onClick={handleMarkPaid}
          className="mt-2 px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600 transition-colors"
          aria-label={`Mark ${bill.name} as paid`}
        >
          Mark as Paid
        </button>
      </div>
    </div>
  );
});

// Add display name
UpcomingBillItem.displayName = 'UpcomingBillItem';

interface UpcomingBillsProps {
  bills: Bill[];
  onMarkPaid: (id: string) => void;
}

// Memoize the entire component to prevent unnecessary re-renders
export const UpcomingBills = memo(({ bills, onMarkPaid }: UpcomingBillsProps) => {
  // AI Assistant Mode toggle
  const [aiAssistantMode, setAIAssistantMode] = useState(false);
  const [showDetails, setShowDetails] = useState<{ [billId: string]: boolean }>({});
  // Add local state for optimistic UI updates
  const [localPaidBills, setLocalPaidBills] = useState<Set<string>>(new Set());

  // Find next 1-2 unpaid bills due after this week, prioritizing overdue and soon-due bills
  const upcoming = useMemo(() => {
    if (!Array.isArray(bills) || bills.length === 0) return [];

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);
    const todayTime = today.getTime();
    const nextWeekTime = nextWeek.getTime();

    // Separate overdue, due soon, and later bills
    const overdue = bills.filter(bill => {
      // Check our optimistic local state first
      if (bill.paidDate || localPaidBills.has(bill.id)) return false;
      const billDueDate = parseLocalDateString(bill.dueDate);
      if (!billDueDate) return false;
      return billDueDate.getTime() < todayTime;
    });
    const dueSoon = bills.filter(bill => {
      // Check our optimistic local state first
      if (bill.paidDate || localPaidBills.has(bill.id)) return false;
      const billDueDate = parseLocalDateString(bill.dueDate);
      if (!billDueDate) return false;
      return billDueDate.getTime() >= todayTime && billDueDate.getTime() < nextWeekTime;
    });
    const later = bills.filter(bill => {
      // Check our optimistic local state first
      if (bill.paidDate || localPaidBills.has(bill.id)) return false;
      const billDueDate = parseLocalDateString(bill.dueDate);
      if (!billDueDate) return false;
      return billDueDate.getTime() >= nextWeekTime;
    });

    // Sort each group by due date ascending
    const sortByDueDate = (a: Bill, b: Bill) => {
      const dateA = parseLocalDateString(a.dueDate);
      const dateB = parseLocalDateString(b.dueDate);
      if (!dateA || !dateB) return 0;
      return dateA.getTime() - dateB.getTime();
    };
    overdue.sort(sortByDueDate);
    dueSoon.sort(sortByDueDate);
    later.sort(sortByDueDate);

    // Show up to 2 bills, prioritizing overdue, then due soon, then later
    return [...overdue, ...dueSoon, ...later].slice(0, 2);
  }, [bills, localPaidBills]);

  const { transactions } = useFinancialStore();

  // Extract income transactions for recommendations
  const userIncome = useMemo(() => {
    if (!transactions) return [];
    return transactions
      .filter(t => t.type === 'income')
      .map(t => ({ amount: t.amount, date: t.date }));
  }, [transactions]);

  // Memoized patterns, predictions, and recommendations for AI mode
  const billPatterns = useMemo(() => analyzeBillPatterns(bills), [bills]);
  const predictions = useMemo(() => predictUpcomingBills(bills, billPatterns), [bills, billPatterns]);
  // Use user income if available, otherwise empty array
  const recommendations = useMemo(() => generatePaymentRecommendations(bills, userIncome), [bills, userIncome]);

  // Create optimistic mark paid handler with error handling
  const handleMarkPaidOptimistic = useCallback(async (id: string) => {
    // Optimistically update local state
    setLocalPaidBills(prev => new Set(prev).add(id));
    try {
      // Await the actual mark as paid action
      await onMarkPaid(id);
    } catch (error) {
      // Revert optimistic update if the action fails
      setLocalPaidBills(prev => {
        const updated = new Set(prev);
        updated.delete(id);
        return updated;
      });
      // Optionally, show an error notification here
    }
  }, [onMarkPaid]);

  // Return empty div instead of null for consistent SSR/CSR rendering
  if (upcoming.length === 0) return <div className="hidden"></div>;

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm mt-4"
      role="region"
      aria-labelledby="upcoming-bills-heading"
    >
      <div className="flex items-center mb-4 justify-between">
        <div className="flex items-center">
          <CalendarIcon />
          <h2 id="upcoming-bills-heading" className="text-lg font-semibold">Upcoming Bills</h2>
        </div>
        <label className="flex items-center gap-2 text-sm cursor-pointer select-none">
          <input
            type="checkbox"
            checked={aiAssistantMode}
            onChange={e => setAIAssistantMode(e.target.checked)}
            className="form-checkbox accent-blue-600"
          />
          AI Assistant Mode
        </label>
      </div>
      <div className="divide-y divide-gray-100 dark:divide-gray-700">
        {upcoming.map((bill: Bill) => {
          // Predict next due date for recurring bills
          let predictedNextDue: string | null = null;
          if (bill.isRecurring) {
            const dueDateStr = bill.dueDate ?? '';
            predictedNextDue = predictNextBillDate(dueDateStr, bill.paymentHistory) ?? null;
          }
          const showPrediction = predictedNextDue && predictedNextDue !== bill.dueDate;

          // AI Assistant insights
          let aiInfo = null;
          if (aiAssistantMode) {
            const urgency = getBillUrgency(bill);
            const prediction = predictions.find(p => p.billId === bill.id);
            const recommendation = recommendations.find(r => r.billId === bill.id);
            const aiCategory = suggestCategory(bill.name);
            const aiTags = suggestTags(bill.name, '', aiCategory ?? undefined, bill.isRecurring);

            // Friendly summary
            const summary = [
              urgency.level === 'critical' ? '⚠️' : urgency.level === 'high' ? '⏰' : '💡',
              urgency.message,
              recommendation ? `Pay by ${recommendation.recommendedPayDate} (${recommendation.priority})` : null,
              prediction && prediction.predictedAmount !== bill.amount ? `Next amount: $${prediction.predictedAmount} (${prediction.confidence})` : null
            ].filter(Boolean).join(' · ');

            aiInfo = (
              <div className="mt-2 p-2 rounded bg-gray-50 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2">
                  <span className="text-xs font-semibold" style={{ color: urgency.color }}>{urgency.level.toUpperCase()}</span>
                  <span className="text-xs text-gray-500">Risk Score: {urgency.score}</span>
                </div>
                <div className="text-xs text-gray-700 dark:text-gray-200 mt-1">{summary}</div>
                <button
                  className="text-xs text-blue-600 dark:text-blue-300 mt-1 underline"
                  onClick={() => setShowDetails(prev => ({ ...prev, [bill.id]: !prev[bill.id] }))}
                  aria-expanded={!!showDetails[bill.id]}
                >
                  {showDetails[bill.id] ? 'Hide Details' : 'Show Details'}
                </button>
                {showDetails[bill.id] && (
                  <div className="mt-2 space-y-1">
                    {recommendation && (
                      <div className="text-xs">💡 <b>AI Payment Recommendation:</b> Pay by <b>{recommendation.recommendedPayDate}</b> (<span className={`font-semibold ${recommendation.priority === 'high' ? 'text-red-600' : recommendation.priority === 'medium' ? 'text-yellow-600' : 'text-green-600'}`}>{recommendation.priority}</span>)<br /><span className="text-gray-500">{recommendation.reason}</span></div>
                    )}
                    {prediction && prediction.predictedAmount !== bill.amount && (
                      <div className="text-xs">🔮 <b>Predicted Next Amount:</b> ${prediction.predictedAmount} <span className="text-gray-500">({prediction.confidence})</span><br /><span className="text-gray-500">{prediction.reason}</span></div>
                    )}
                    <div className="text-xs">🏷️ <b>AI Category:</b> {aiCategory || 'N/A'}</div>
                    <div className="text-xs">🔖 <b>AI Tags:</b> {aiTags.length > 0 ? aiTags.join(', ') : 'None'}</div>
                    <div className="text-xs">📝 <b>AI Suggestion:</b> {urgency.action}</div>
                  </div>
                )}
              </div>
            );
          }

          return (
            <div key={bill.id} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700">
              <div>
                <h3 className="font-medium flex items-center gap-2">
                  {bill.name}
                  {showPrediction && (
                    <span className="ml-2 px-2 py-0.5 rounded bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs font-semibold">
                      Predicted Next Due: {predictedNextDue}
                    </span>
                  )}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Due {bill.dueDate ? formatDate(bill.dueDate, 'relative') : 'N/A'}
                </p>
                {aiInfo}
              </div>
              <div className="text-right">
                <p className="font-medium">${bill.amount.toFixed(2)}</p>
                <p className="text-xs text-gray-500">{bill.dueDate ? formatDate(bill.dueDate, 'short') : 'N/A'}</p>
                <button
                  onClick={() => handleMarkPaidOptimistic(bill.id)}
                  className="mt-2 px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600 transition-colors"
                  aria-label={`Mark ${bill.name} as paid`}
                >
                  Mark as Paid
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
});

// Add display name
UpcomingBills.displayName = 'UpcomingBills';
