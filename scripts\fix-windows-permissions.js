#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Windows Permission Fix for Next.js Build');
console.log('============================================\n');

// Function to safely remove directory/file
function removeItem(itemPath) {
  try {
    if (fs.existsSync(itemPath)) {
      console.log(`🗑️  Removing ${itemPath}...`);
      const stats = fs.statSync(itemPath);
      if (stats.isDirectory()) {
        fs.rmSync(itemPath, { recursive: true, force: true });
      } else {
        // For files, try to remove read-only attribute first
        try {
          execSync(`attrib -R "${itemPath}"`, { stdio: 'pipe' });
        } catch (e) {
          // Ignore if attrib fails
        }
        fs.unlinkSync(itemPath);
      }
      console.log(`✅ Removed ${itemPath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.warn(`⚠️  Could not remove ${itemPath}:`, error.message);
    
    // Try alternative removal methods for Windows
    try {
      console.log(`🔄 Trying alternative removal for ${itemPath}...`);
      if (fs.statSync(itemPath).isDirectory()) {
        execSync(`rmdir /s /q "${itemPath}"`, { stdio: 'pipe' });
      } else {
        execSync(`del /f /q "${itemPath}"`, { stdio: 'pipe' });
      }
      console.log(`✅ Alternative removal succeeded for ${itemPath}`);
      return true;
    } catch (altError) {
      console.error(`❌ All removal methods failed for ${itemPath}:`, altError.message);
      return false;
    }
  }
}

// Function to fix OneDrive sync issues
function fixOneDriveSync() {
  try {
    console.log('🔄 Checking OneDrive sync status...');
    
    // Check if we're in OneDrive folder
    const currentDir = process.cwd();
    if (currentDir.includes('OneDrive')) {
      console.log('📁 Detected OneDrive folder - applying OneDrive-specific fixes...');
      
      // Try to pause OneDrive sync temporarily
      try {
        console.log('⏸️  Attempting to pause OneDrive sync...');
        execSync('powershell -Command "Get-Process OneDrive -ErrorAction SilentlyContinue | Stop-Process -Force"', { stdio: 'pipe' });
        console.log('✅ OneDrive process stopped');
      } catch (e) {
        console.log('ℹ️  OneDrive process not running or couldn\'t be stopped');
      }
      
      return true;
    }
    return false;
  } catch (error) {
    console.warn('⚠️  OneDrive sync fix failed:', error.message);
    return false;
  }
}

// Function to fix file permissions
function fixPermissions(filePath) {
  try {
    console.log(`🔐 Fixing permissions for ${filePath}...`);
    
    // Remove read-only attributes
    execSync(`attrib -R "${filePath}" /S /D`, { stdio: 'pipe' });
    
    // Take ownership (requires admin rights, but try anyway)
    try {
      execSync(`takeown /f "${filePath}" /r /d y`, { stdio: 'pipe' });
      console.log(`✅ Took ownership of ${filePath}`);
    } catch (e) {
      console.log(`ℹ️  Could not take ownership of ${filePath} (may require admin rights)`);
    }
    
    // Grant full control to current user
    try {
      const username = process.env.USERNAME || 'Everyone';
      execSync(`icacls "${filePath}" /grant "${username}":F /T`, { stdio: 'pipe' });
      console.log(`✅ Granted full control to ${username} for ${filePath}`);
    } catch (e) {
      console.log(`ℹ️  Could not modify ACL for ${filePath}`);
    }
    
    return true;
  } catch (error) {
    console.warn(`⚠️  Permission fix failed for ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
async function main() {
  try {
    // Step 1: Check and fix OneDrive issues
    const isOneDrive = fixOneDriveSync();
    
    // Step 2: Clean problematic build artifacts
    console.log('\n📁 Cleaning build artifacts...');
    const itemsToRemove = [
      '.next',
      '.turbo', 
      'node_modules/.cache',
      'build-out'
    ];
    
    for (const item of itemsToRemove) {
      removeItem(item);
    }
    
    // Step 3: Fix permissions on current directory
    console.log('\n🔐 Fixing directory permissions...');
    fixPermissions('.');
    
    // Step 4: Clear npm cache
    console.log('\n🧹 Clearing npm cache...');
    try {
      execSync('npm cache clean --force', { stdio: 'inherit' });
    } catch (error) {
      console.warn('⚠️  Could not clear npm cache:', error.message);
    }
    
    // Step 5: Reinstall dependencies
    console.log('\n📦 Reinstalling dependencies...');
    try {
      execSync('npm install', { stdio: 'inherit' });
      console.log('✅ Dependencies reinstalled');
    } catch (error) {
      console.error('❌ Failed to reinstall dependencies:', error.message);
      throw error;
    }
    
    // Step 6: Restart OneDrive if it was running
    if (isOneDrive) {
      console.log('\n🔄 Restarting OneDrive...');
      try {
        execSync('start "" "%LOCALAPPDATA%\\Microsoft\\OneDrive\\OneDrive.exe"', { stdio: 'pipe' });
        console.log('✅ OneDrive restarted');
      } catch (e) {
        console.log('ℹ️  Could not restart OneDrive automatically - please restart manually');
      }
    }
    
    console.log('\n🎉 Windows permission fix completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Wait 30 seconds for OneDrive to sync');
    console.log('   2. Run: npm run build');
    console.log('   3. If issues persist, run as administrator');
    
  } catch (error) {
    console.error('\n💥 Permission fix failed:', error.message);
    console.log('\n🔧 Manual steps to try:');
    console.log('   1. Run this script as Administrator');
    console.log('   2. Move project outside OneDrive folder');
    console.log('   3. Disable OneDrive sync for this folder');
    console.log('   4. Use WSL2 for development instead');
    process.exit(1);
  }
}

// Run the fix
main();
