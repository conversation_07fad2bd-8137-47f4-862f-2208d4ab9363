// src/app/api/subscribe/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin'; // Import the initialized Firestore instance
import type { PushSubscription } from 'web-push';

export async function POST(request: NextRequest) {
  try {
    const subscription = await request.json() as PushSubscription;

    if (!subscription || !subscription.endpoint) {
      return NextResponse.json({ success: false, message: 'Invalid subscription object received.' }, { status: 400 });
    }

    // Get the user ID from the request
    // For development/testing, we'll use a query parameter or a default value
    // In production, replace this with your actual auth logic
    
    // OPTION 1: Get from query parameter (for testing)
    const url = new URL(request.url);
    let userId = url.searchParams.get('userId') || 'anonymous-user';
    
    // OPTION 2: Get from cookies (if using cookie-based auth)
    // const cookies = request.cookies;
    // const authCookie = cookies.get('your-auth-cookie-name');
    // userId = authCookie ? parseAuthCookie(authCookie.value) : 'anonymous-user';
    
    // OPTION 3: Get from authorization header (if using JWT/token auth)
    // const authHeader = request.headers.get('authorization');
    // if (authHeader && authHeader.startsWith('Bearer ')) {
    //   const token = authHeader.substring(7);
    //   try {
    //     const decoded = await verifyToken(token); // Implement this function
    //     userId = decoded.userId;
    //   } catch (error) {
    //     console.error('Invalid token:', error);
    //     userId = 'anonymous-user';
    //   }
    // }
    
    // OPTION 4: Get from Next.js Auth.js/NextAuth session
    // import { getServerSession } from 'next-auth';
    // const session = await getServerSession(authOptions);
    // userId = session?.user?.id || 'anonymous-user';
    
    console.log(`Using userId: ${userId} for subscription`);
    
    // Uncomment this if you want to enforce authentication
    // if (userId === 'anonymous-user') {
    //   return NextResponse.json({ success: false, message: 'Authentication required' }, { status: 401 });
    // }

    // Use the subscription endpoint as a unique document ID
    const docRef = db.collection('pushSubscriptions').doc(subscription.endpoint);

    // Save the subscription data (endpoint is the ID, so no need to store it again)
    await docRef.set({
      userId: userId, // Associate with the user
      keys: subscription.keys, // Store the p256dh and auth keys
      createdAt: new Date(), // Optional: Timestamp for when it was added
    });

    console.log(`Subscription saved/updated for endpoint: ${subscription.endpoint.substring(0, 30)}...`);

    return NextResponse.json({ success: true, message: 'Subscription received and saved.' }, { status: 201 });

  } catch (error) {
    console.error('Error processing/saving subscription:', error);
    let errorMessage = 'Failed to process subscription.';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}
