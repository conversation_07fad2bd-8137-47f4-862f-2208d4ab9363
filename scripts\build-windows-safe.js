#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Windows-Safe Build Process');
console.log('=============================\n');

const originalConfig = 'next.config.js';
const safeConfig = 'next.config.windows-safe.js';
const backupConfig = 'next.config.js.backup';

function backupOriginalConfig() {
  try {
    if (fs.existsSync(originalConfig)) {
      console.log('📋 Backing up original Next.js config...');
      fs.copyFileSync(originalConfig, backupConfig);
      console.log('✅ Original config backed up');
      return true;
    }
    return false;
  } catch (error) {
    console.error('❌ Failed to backup config:', error.message);
    return false;
  }
}

function useSafeConfig() {
  try {
    console.log('🔄 Switching to Windows-safe config...');
    if (fs.existsSync(safeConfig)) {
      fs.copyFileSync(safeConfig, originalConfig);
      console.log('✅ Windows-safe config activated');
      return true;
    } else {
      console.error('❌ Windows-safe config not found');
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to switch config:', error.message);
    return false;
  }
}

function restoreOriginalConfig() {
  try {
    if (fs.existsSync(backupConfig)) {
      console.log('🔄 Restoring original config...');
      fs.copyFileSync(backupConfig, originalConfig);
      fs.unlinkSync(backupConfig);
      console.log('✅ Original config restored');
      return true;
    }
    return false;
  } catch (error) {
    console.error('❌ Failed to restore config:', error.message);
    return false;
  }
}

function runBuild() {
  try {
    console.log('\n🏗️  Running Next.js build with safe config...');
    execSync('cross-env NODE_OPTIONS="--max-old-space-size=4096" next build', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        NEXT_TELEMETRY_DISABLED: '1',
        NODE_ENV: 'production'
      }
    });
    console.log('✅ Build completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  let configBackedUp = false;
  
  try {
    // Step 1: Backup original config
    configBackedUp = backupOriginalConfig();
    
    // Step 2: Use safe config
    if (!useSafeConfig()) {
      throw new Error('Could not activate Windows-safe config');
    }
    
    // Step 3: Run build
    const buildSuccess = runBuild();
    
    // Step 4: Restore original config
    if (configBackedUp) {
      restoreOriginalConfig();
    }
    
    if (buildSuccess) {
      console.log('\n🎉 Windows-safe build completed successfully!');
      console.log('\n📝 What was done:');
      console.log('   ✅ Used simplified Next.js config');
      console.log('   ✅ Disabled problematic webpack optimizations');
      console.log('   ✅ Disabled file system caching');
      console.log('   ✅ Reduced memory usage');
      console.log('\n💡 If this worked, consider:');
      console.log('   - Moving project outside OneDrive');
      console.log('   - Using WSL2 for development');
      console.log('   - Running as Administrator');
    } else {
      throw new Error('Build failed even with safe config');
    }
    
  } catch (error) {
    console.error('\n💥 Windows-safe build failed:', error.message);
    
    // Try to restore config on failure
    if (configBackedUp) {
      try {
        restoreOriginalConfig();
      } catch (restoreError) {
        console.error('❌ Could not restore original config:', restoreError.message);
      }
    }
    
    console.log('\n🔧 Alternative solutions:');
    console.log('   1. Run PowerShell as Administrator');
    console.log('   2. Move project to C:\\dev\\ or similar location');
    console.log('   3. Use WSL2: wsl --install');
    console.log('   4. Disable OneDrive sync for this folder');
    console.log('   5. Use npm run dev instead (development mode)');
    
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️  Build interrupted - restoring config...');
  if (fs.existsSync(backupConfig)) {
    restoreOriginalConfig();
  }
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Build terminated - restoring config...');
  if (fs.existsSync(backupConfig)) {
    restoreOriginalConfig();
  }
  process.exit(1);
});

// Run the safe build
main();
