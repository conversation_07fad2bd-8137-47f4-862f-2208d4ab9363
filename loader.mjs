// Custom ESM loader for faster development
// This loader bypasses unnecessary transformations for node_modules
// that are already compatible with ESM

// List of packages to bypass transformations for
const bypassPackages = [
    'firebase',
    '@firebase',
    'googleapis',
    'date-fns',
    'recharts',
    'framer-motion',
    '@headlessui',
    '@heroicons',
    'react-big-calendar',
    'zustand'
];

// Node's default resolver

// Helper for checking if a module should be bypassed
function shouldBypass(url) {
    return bypassPackages.some(pkg => url.includes(`/node_modules/${pkg}/`));
}

// Next.js resolve hook - speeds up module resolution
export function resolve(specifier, context, nextResolve) {
    if (shouldBypass(specifier)) {
        // Let Next.js handle the resolution to avoid Windows path issues
        return nextResolve(specifier, context);
    }

    return nextResolve(specifier, context);
}

// Next.js load hook - speeds up loading
export function load(url, context, nextLoad) {
    // Skip transformation for certain packages
    if (shouldBypass(url)) {
        return nextLoad(url, { ...context, format: 'module' });
    }

    return nextLoad(url, context);
}
