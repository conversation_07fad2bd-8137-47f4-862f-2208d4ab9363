"use client";
import { useAuth } from '@/hooks/useAuth';

export default function AccountSettingsPage() {
  const { user } = useAuth();

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm mt-8">
      <h1 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">Account</h1>

      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center text-2xl font-bold text-primary">
            {(user?.displayName?.[0] || user?.email?.[0] || 'U')}
          </div>
          <div>
            <div className="font-medium text-lg text-gray-900 dark:text-white">{user?.displayName || 'Local User'}</div>
            <div className="text-gray-500 dark:text-gray-400 text-sm">{user?.email || 'No email (local mode)'}</div>
          </div>
        </div>
      </div>

      <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg mb-4">
        <h2 className="text-lg font-medium mb-2 text-gray-800 dark:text-gray-200">Local-Only Mode</h2>
        <p className="text-sm text-gray-600 dark:text-gray-300">
          Authentication and cloud features are disabled. Your data stays on this device.
        </p>
      </div>

      <div className="text-xs text-gray-500 dark:text-gray-400">
        Looking for Google Calendar sync or sign-in? Those have been removed for an auth-free experience.
      </div>
    </div>
  );
}
