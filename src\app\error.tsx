'use client'; // Error components must be Client Components

import { useEffect } from 'react';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <h2 className="text-2xl font-semibold text-red-600 mb-4">Something went wrong!</h2>
      <p className="text-gray-700 dark:text-gray-300 mb-6">{error.message || 'An unexpected error occurred.'}</p>
      <button
        onClick={reset} // Attempt to recover by re-rendering the segment
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
      >
        Try again
      </button>
    </div>
  );
}
