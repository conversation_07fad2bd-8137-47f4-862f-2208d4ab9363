'use client';
import { useChangelogNotification } from '@/hooks/useChangelogNotification';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function ChangelogPage() {
    const [htmlContent, setHtmlContent] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const router = useRouter();
    const { markChangelogAsRead } = useChangelogNotification();

    useEffect(() => {
        const loadChangelog = async () => {
            setIsLoading(true);
            try {
                const res = await fetch('/CHANGELOG.md');
                if (!res.ok) {
                    throw new Error('Changelog not found');
                }
                const text = await res.text();
                
                // Dynamically import marked
                const { marked } = await import('marked');
                
                const parsed = marked.parse(text);
                if (typeof parsed === 'string') {
                    setHtmlContent(parsed);
                } else {
                    // Handle cases where marked.parse might return a Promise
                    // This is less common with modern `marked` but good for robustness
                    const resolvedHtml = await parsed;
                    setHtmlContent(resolvedHtml);
                }
                markChangelogAsRead();
            } catch (e: any) {
                setError(e.message || 'Unable to load changelog.');
            } finally {
                setIsLoading(false);
            }
        };

        loadChangelog();
    }, [markChangelogAsRead]);

    if (error) return (
        <div className="max-w-2xl mx-auto p-8 flex flex-col items-center justify-center min-h-[60vh]">
            <div className="bg-red-100 text-red-700 rounded-lg px-6 py-4 text-center shadow">
                {error}
            </div>
        </div>
    );
    if (isLoading || !htmlContent) return (
        <div className="max-w-2xl mx-auto p-8 flex flex-col items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mb-4"></div>
            <div className="text-gray-500">Loading changelog...</div>
        </div>
    );

    return (
        <div className="max-w-2xl mx-auto p-6 md:p-10 min-h-[60vh]">
            <button
                onClick={() => router.push('/settings')}
                className="mb-6 flex items-center gap-2 text-primary hover:underline focus:outline-none focus:ring-2 focus:ring-primary/30 focus:ring-offset-2 rounded px-2 py-1 bg-white dark:bg-gray-900/60 shadow"
                aria-label="Back to Settings"
            >
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Settings
            </button>
            <div className="flex items-center gap-3 mb-6">
                <svg className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5v14h14V5H5zm2 2h10v10H7V7zm2 2v6h6V9H9z" />
                </svg>
                <h1 className="text-3xl font-bold tracking-tight">Changelog</h1>
            </div>
            <div className="prose dark:prose-invert max-w-none bg-white/80 dark:bg-gray-900/60 rounded-xl shadow p-6 transition-all">
                {/* eslint-disable-next-line react/no-danger */}
                <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
            </div>
        </div>
    );
}
