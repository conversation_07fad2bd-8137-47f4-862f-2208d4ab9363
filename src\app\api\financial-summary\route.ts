// src/app/api/financial-summary/route.ts
import { NextRequest, NextResponse } from 'next/server';
// Transaction type is not directly used here anymore, but FinancialSummary is.
// calculateSummary will handle Transaction type internally.
import transactionsStore from '../../../lib/transactionsStore';
// Import the calculateSummary function
import { calculateSummary } from '../../../utils/financialIntelligence';


// GET /api/financial-summary - Fetch calculated financial summary
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/financial-summary - Calculating summary');
    // In a real app, you might fetch transactions from DB here or calculate via DB query
    const summary = calculateSummary(transactionsStore.getTransactions()); // Get transactions from store
    return NextResponse.json(summary);
  } catch (error) {
    console.error('Failed to calculate financial summary:', error);
    return NextResponse.json({ message: 'Failed to calculate financial summary' }, { status: 500 });
  }
}
