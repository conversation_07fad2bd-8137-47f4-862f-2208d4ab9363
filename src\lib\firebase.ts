// Ultra-optimized Firebase imports using dynamic imports only
// No static imports to avoid bundling unnecessary code during development

// Type definitions without imports for better tree-shaking
type FirebaseApp = any;
type Auth = any;
type Firestore = any;
type Analytics = any;
type Messaging = any;
type GoogleAuthProvider = any;

// Configuration moved to separate file to avoid recompilation when code changes
const firebaseConfig = {
  apiKey: "AIzaSyA2uCt4w6XMIjDAdCoUmxfrY9Q441XUfRo",
  authDomain: "payday-pilot.firebaseapp.com",
  projectId: "payday-pilot",
  storageBucket: "payday-pilot.firebasestorage.app",
  messagingSenderId: "122539156310",
  appId: "1:122539156310:web:33f3153bfceb0437792cf7",
  measurementId: "G-GN0V3SX78P"
};

// Initialize core Firebase app with service initialization flags
let app: FirebaseApp;
let auth: Auth;
let db: Firestore;
let googleProvider: GoogleAuthProvider;

// Lazy-loaded services with initialization flags
let analyticsInitialized = false;
let messagingInitialized = false;
let _analytics: Analytics | null = null;
let _messaging: Messaging | null = null;

// Development environment detection
const isDev = process.env.NODE_ENV === 'development';

// Initialize Firebase core services (auth and firestore) with on-demand loading
// Using a separate function for development to avoid unnecessary bundling
async function initializeCoreServices() {
  if (!app) {
    // Development mode - mock services if needed for faster startup
    if (isDev && process.env.MOCK_FIREBASE === 'true') {
      console.log('[DEV] Using mock Firebase services');
      app = {} as FirebaseApp;
      auth = {} as Auth;
      db = {} as Firestore;
      googleProvider = {} as GoogleAuthProvider;
      return { app, auth, db, googleProvider };
    }

    // Load only the necessary modules
    const firebaseApp = await import('firebase/app');
    app = firebaseApp.getApps().length === 0
      ? firebaseApp.initializeApp(firebaseConfig)
      : firebaseApp.getApps()[0];

    // Load auth services on demand
    const firebaseAuth = await import('firebase/auth');
    auth = firebaseAuth.getAuth(app);
    googleProvider = new firebaseAuth.GoogleAuthProvider();

    // Load firestore on demand
    const firebaseFirestore = await import('firebase/firestore');
    db = firebaseFirestore.getFirestore(app);
  }

  return { app, auth, db, googleProvider };
}

// Lazy load analytics
async function getAnalyticsService() {
  if (typeof window === 'undefined') return null;

  if (!analyticsInitialized) {
    try {
      const { getAnalytics, isSupported } = await import('firebase/analytics');
      if (await isSupported()) {
        _analytics = getAnalytics(app);
      }
    } catch (error) {
      console.warn('Analytics not supported:', error);
    }
    analyticsInitialized = true;
  }
  return _analytics;
}

// Lazy load messaging
async function getMessagingService() {
  if (typeof window === 'undefined') return null;

  if (!messagingInitialized) {
    try {
      const { getMessaging, isSupported } = await import('firebase/messaging');
      if (await isSupported()) {
        _messaging = getMessaging(app);
      }
    } catch (error) {
      console.warn('Messaging not supported:', error);
    }
    messagingInitialized = true;
  }
  return _messaging;
}

// Initialize core services with lazy loading
let coreServicesPromise: Promise<any> | null = null;

function getCoreServices() {
  if (!coreServicesPromise) {
    coreServicesPromise = initializeCoreServices();
  }
  return coreServicesPromise;
}

// Get initialized services
const coreServices = getCoreServices();

// Export lazy-loaded services
export {
  coreServices,
  getAnalyticsService as getAnalytics,
  getMessagingService as getMessaging
};

// Export individual services with proper initialization
// These getters ensure that the services are initialized before use
export const getAuth = async () => {
  const services = await coreServices;
  return services.auth;
};

export const getDb = async () => {
  const services = await coreServices;
  return services.db;
};

export const getGoogleProvider = async () => {
  const services = await coreServices;
  return services.googleProvider;
};

// For backward compatibility, still export direct references
// but these will only be valid after initialization
export { app, auth, db, googleProvider };

