'use client';

export default function SimpleDashboard() {
  return (
    <div className="min-h-screen bg-gray-50 px-4 py-8 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-6 text-3xl font-bold text-gray-900 dark:text-gray-100">
          Interface Smoke Test
        </h1>

        <div className="mb-6 grid gap-4">
          <div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-700 dark:bg-green-900/40">
            <h2 className="mb-2 text-xl font-semibold text-green-800 dark:text-green-200">React is running</h2>
            <p className="text-green-700 dark:text-green-300">
              The minimal dashboard renders without loading the full application shell.
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Mobile layout checks</h3>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <div className="rounded-lg border bg-white p-4 text-sm text-gray-600 shadow dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400">
              <h4 className="mb-2 font-medium text-gray-900 dark:text-gray-100">Small screen</h4>
              <p>Content remains readable on narrow devices.</p>
            </div>

            <div className="rounded-lg border bg-white p-4 text-sm text-gray-600 shadow dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400">
              <h4 className="mb-2 font-medium text-gray-900 dark:text-gray-100">Touch friendly</h4>
              <button className="rounded bg-blue-500 px-4 py-2 text-sm font-semibold text-white transition hover:bg-blue-600">
                Tap me
              </button>
            </div>

            <div className="rounded-lg border bg-white p-4 text-sm text-gray-600 shadow sm:col-span-2 lg:col-span-1 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400">
              <h4 className="mb-2 font-medium text-gray-900 dark:text-gray-100">Responsive grid</h4>
              <p>The layout adapts across breakpoints.</p>
            </div>
          </div>

          <div className="mt-8 rounded-lg border border-blue-200 bg-blue-50 p-4 text-sm text-blue-700 dark:border-blue-800 dark:bg-blue-900/30 dark:text-blue-200">
            <h4 className="mb-2 font-medium">Display checklist</h4>
            <ul className="space-y-1">
              <li>- Text remains legible without zooming.</li>
              <li>- Touch targets are at least 44 pixels tall.</li>
              <li>- Cards fill available width on mobile.</li>
              <li>- No horizontal scrolling on handheld devices.</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
