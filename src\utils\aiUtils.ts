import { differenceInDays, format, parseISO, isAfter, isBefore, addDays } from 'date-fns';
import { Bill } from '@/types/bill';

export interface BillAnalysis {
  totalDue: number;
  overdueBills: Bill[];
  dueTodayBills: Bill[];
  upcomingBills: Bill[];
  overdueTotal: number;
  dueTodayTotal: number;
  upcomingTotal: number;
  topCategories: Array<{ category: string; total: number }>;
  largestBill?: Bill;
  smallestBill?: Bill;
  billsByMonth: Record<string, number>;
  paymentPattern: {
    onTime: number;
    late: number;
    veryLate: number;
  };
}

export interface AIResponse {
  text: string;
  data?: any;
  type: 'text' | 'chart' | 'list' | 'comparison';
}

/**
 * Analyze bill data to provide insights
 */
export function analyzeBillData(bills: Bill[]): BillAnalysis {
  const now = new Date();
  const oneWeekLater = addDays(now, 7);
  
  // Filter bills by status
  const overdueBills = bills.filter(bill => 
    bill.dueDate && isBefore(parseISO(bill.dueDate), now) && !bill.isPaid
  );
  
  const dueTodayBills = bills.filter(bill => {
    if (!bill.dueDate || bill.isPaid) return false;
    const dueDate = parseISO(bill.dueDate);
    return dueDate.getDate() === now.getDate() && 
           dueDate.getMonth() === now.getMonth() && 
           dueDate.getFullYear() === now.getFullYear();
  });
  
  const upcomingBills = bills.filter(bill => 
    bill.dueDate && 
    isAfter(parseISO(bill.dueDate), now) && 
    isBefore(parseISO(bill.dueDate), oneWeekLater) && 
    !bill.isPaid
  );
  
  // Calculate totals
  const totalDue = bills.reduce((sum, bill) => 
    !bill.isPaid ? sum + (bill.amount || 0) : sum, 0);
    
  const overdueTotal = overdueBills.reduce((sum, bill) => 
    sum + (bill.amount || 0), 0);
    
  const dueTodayTotal = dueTodayBills.reduce((sum, bill) => 
    sum + (bill.amount || 0), 0);
    
  const upcomingTotal = upcomingBills.reduce((sum, bill) => 
    sum + (bill.amount || 0), 0);
  
  // Get category insights
  const categorySums: Record<string, number> = {};
  bills.forEach(bill => {
    const category = bill.category || 'Uncategorized';
    categorySums[category] = (categorySums[category] || 0) + (bill.amount || 0);
  });
  
  const topCategories = Object.entries(categorySums)
    .map(([category, total]) => ({ category, total }))
    .sort((a, b) => b.total - a.total)
    .slice(0, 3);
  
  // Find min/max bills
  const nonZeroBills = bills.filter(bill => (bill.amount || 0) > 0);
  const largestBill = nonZeroBills.length 
    ? nonZeroBills.reduce((max, bill) => 
        (!max || (bill.amount || 0) > (max.amount || 0)) ? bill : max, 
        undefined as Bill | undefined) 
    : undefined;
    
  const smallestBill = nonZeroBills.length 
    ? nonZeroBills.reduce((min, bill) => 
        (!min || (bill.amount || 0) < (min.amount || 0)) ? bill : min, 
        undefined as Bill | undefined) 
    : undefined;
  
  // Calculate monthly breakdown
  const billsByMonth: Record<string, number> = {};
  bills.forEach(bill => {
    if (bill.dueDate) {
      const month = format(parseISO(bill.dueDate), 'MMMM');
      billsByMonth[month] = (billsByMonth[month] || 0) + (bill.amount || 0);
    }
  });
  
  // Calculate payment patterns
  const paymentPattern = {
    onTime: 0,
    late: 0,
    veryLate: 0
  };
  
  bills.forEach(bill => {
    if (bill.isPaid && bill.paidDate && bill.dueDate) {
      const daysLate = differenceInDays(
        parseISO(bill.paidDate), 
        parseISO(bill.dueDate)
      );
      
      if (daysLate <= 0) paymentPattern.onTime++;
      else if (daysLate <= 7) paymentPattern.late++;
      else paymentPattern.veryLate++;
    }
  });
  
  return {
    totalDue,
    overdueBills,
    dueTodayBills,
    upcomingBills,
    overdueTotal,
    dueTodayTotal,
    upcomingTotal,
    topCategories,
    largestBill,
    smallestBill,
    billsByMonth,
    paymentPattern
  };
}

/**
 * Generate AI response based on user query and bill analysis
 */
export function generateResponse(query: string, analysis: BillAnalysis): AIResponse {
  // Normalize the query for easier matching
  const normalizedQuery = query.toLowerCase().trim();
  
  // Check for overdue bills
  if (normalizedQuery.includes('overdue') || normalizedQuery.includes('late')) {
    if (analysis.overdueBills.length === 0) {
      return {
        text: "Great news! You don't have any overdue bills at the moment.",
        type: 'text'
      };
    }
    
    const billList = analysis.overdueBills.map(bill => 
      `• ${bill.name}: $${bill.amount?.toFixed(2)} (due ${format(parseISO(bill.dueDate || ''), 'PP')})`
    ).join('\n');
    
    return {
      text: `You have ${analysis.overdueBills.length} overdue bill${analysis.overdueBills.length > 1 ? 's' : ''} totaling $${analysis.overdueTotal.toFixed(2)}:\n\n${billList}\n\nI recommend prioritizing these payments to avoid late fees.`,
      data: analysis.overdueBills,
      type: 'list'
    };
  }
  
  // Check for upcoming bills
  if (normalizedQuery.includes('upcoming') || normalizedQuery.includes('next') || normalizedQuery.includes('soon')) {
    const combinedBills = [...analysis.dueTodayBills, ...analysis.upcomingBills];
    
    if (combinedBills.length === 0) {
      return {
        text: "You don't have any bills due today or in the coming week.",
        type: 'text'
      };
    }
    
    const billList = combinedBills.map(bill => {
      const dueDate = parseISO(bill.dueDate || '');
      const isDueToday = format(dueDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');
      return `• ${bill.name}: $${bill.amount?.toFixed(2)} (due ${isDueToday ? 'today' : format(dueDate, 'PP')})`;
    }).join('\n');
    
    return {
      text: `You have ${combinedBills.length} bill${combinedBills.length > 1 ? 's' : ''} due today or in the next 7 days, totaling $${(analysis.dueTodayTotal + analysis.upcomingTotal).toFixed(2)}:\n\n${billList}`,
      data: combinedBills,
      type: 'list'
    };
  }
  
  // Check for budget analysis
  if (normalizedQuery.includes('budget') || normalizedQuery.includes('spending') || normalizedQuery.includes('analysis')) {
    const topCategoriesText = analysis.topCategories.map(cat => 
      `• ${cat.category}: $${cat.total.toFixed(2)}`
    ).join('\n');
    
    return {
      text: `Based on your bills, here's a spending breakdown:\n\n${topCategoriesText}\n\nYour largest expense category is ${analysis.topCategories[0]?.category}, representing ${((analysis.topCategories[0]?.total / analysis.totalDue) * 100).toFixed(1)}% of your total bills.`,
      data: analysis.topCategories,
      type: 'chart'
    };
  }
  
  // Check for bill optimization
  if (normalizedQuery.includes('optimize') || normalizedQuery.includes('reduce') || normalizedQuery.includes('save')) {
    if (analysis.topCategories.length === 0) {
      return {
        text: "I don't see enough bills to make optimization recommendations yet.",
        type: 'text'
      };
    }
    
    return {
      text: `Looking at your bills, here are some optimization opportunities:\n\n1. Your ${analysis.topCategories[0]?.category} expenses are your highest category at $${analysis.topCategories[0]?.total.toFixed(2)}. Consider researching alternatives or negotiating rates.\n\n2. You have ${analysis.overdueBills.length} overdue bills which might incur late fees.\n\n3. Try to schedule payments for the ${analysis.largestBill?.name} bill ($${analysis.largestBill?.amount?.toFixed(2)}) earlier in the month to better manage cash flow.`,
      type: 'list'
    };
  }
  
  // Default response - summary
  return {
    text: `Here's a summary of your financial situation:\n\n• Total bills due: $${analysis.totalDue.toFixed(2)}\n• Overdue: $${analysis.overdueTotal.toFixed(2)} (${analysis.overdueBills.length} bills)\n• Due today: $${analysis.dueTodayTotal.toFixed(2)} (${analysis.dueTodayBills.length} bills)\n• Upcoming this week: $${analysis.upcomingTotal.toFixed(2)} (${analysis.upcomingBills.length} bills)\n\nYour largest bill is ${analysis.largestBill?.name} at $${analysis.largestBill?.amount?.toFixed(2)}.`,
    type: 'text'
  };
}
