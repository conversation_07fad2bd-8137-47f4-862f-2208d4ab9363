'use client';

import { useState, useEffect } from 'react';

interface ResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isPwa: boolean;
  isStandalone: boolean;
}

/**
 * Hook for responsive design and PWA-related checks
 */
export function useResponsive(): ResponsiveState {
  // Initialize with default values that match server-side rendering
  // This prevents hydration mismatches
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDesktop, setIsDesktop] = useState(true);
  const [isPwa, setIsPwa] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  
  // Track if component is mounted to avoid hydration issues
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Set mounted state to true
    setIsMounted(true);
    
    const checkResponsive = () => {
      // Skip if not in browser environment
      if (typeof window === 'undefined') return;
      
      try {
        const width = window.innerWidth;
        
        // Breakpoints based on common device sizes
        setIsMobile(width < 768);
        setIsTablet(width >= 768 && width < 1024);
        setIsDesktop(width >= 1024);
        
        // Check for PWA status - safely
        let isInStandaloneMode = false;
        let isInPwaMode = false;
        
        if ('matchMedia' in window) {
          isInStandaloneMode = 
            window.matchMedia('(display-mode: standalone)').matches ||
            window.matchMedia('(display-mode: fullscreen)').matches;
            
          if (window.navigator && 'standalone' in window.navigator) {
            isInStandaloneMode = isInStandaloneMode || 
              (window.navigator as Navigator & { standalone?: boolean }).standalone === true;
          }
          
          isInPwaMode = 
            window.matchMedia('(display-mode: standalone)').matches ||
            window.matchMedia('(display-mode: fullscreen)').matches ||
            window.matchMedia('(display-mode: minimal-ui)').matches;
        }
        
        setIsStandalone(isInStandaloneMode);
        setIsPwa(isInPwaMode);
      } catch (error) {
        console.warn('Error checking responsive state:', error);
      }
    };

    // Only run client-side code after component is mounted
    // This prevents hydration mismatches
    if (typeof window !== 'undefined') {
      // Run on mount
      checkResponsive();
      
      // Listen for window resize
      window.addEventListener('resize', checkResponsive);
      
      // Listen for display mode changes - safely
      const displayModes = ['browser', 'standalone', 'minimal-ui', 'fullscreen'];
      const mediaQueryListeners: MediaQueryList[] = [];
      
      if ('matchMedia' in window) {
        displayModes.forEach(mode => {
          try {
            const mql = window.matchMedia(`(display-mode: ${mode})`);
            const listener = (e: MediaQueryListEvent) => {
              if (e.matches) {
                setTimeout(checkResponsive, 100);
              }
            };
            
            // Use the appropriate event listener method
            if (mql.addEventListener) {
              mql.addEventListener('change', listener);
            } else if (mql.addListener) {
              // For older browsers
              mql.addListener(listener as any);
            }
            
            mediaQueryListeners.push(mql);
          } catch (error) {
            console.warn(`Error setting up media query for ${mode}:`, error);
          }
        });
      }

      // Cleanup
      return () => {
        window.removeEventListener('resize', checkResponsive);
        
        if ('matchMedia' in window) {
          displayModes.forEach((_, index) => {
            if (index < mediaQueryListeners.length) {
              const mql = mediaQueryListeners[index];
              try {
                if (mql.removeEventListener) {
                  mql.removeEventListener('change', mql.onchange as EventListener);
                } else if (mql.removeListener) {
                  // For older browsers
                  mql.removeListener(mql.onchange as any);
                }
              } catch (error) {
                console.warn('Error removing media query listener:', error);
              }
            }
          });
        }
      };
    }
    
    // Empty cleanup function for server-side rendering
    return () => {};
  }, []);

  // If not mounted yet (server-side rendering), return default values
  // This ensures consistent rendering between server and client
  if (!isMounted && typeof window === 'undefined') {
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isPwa: false,
      isStandalone: false
    };
  }
  
  return {
    isMobile,
    isTablet,
    isDesktop,
    isPwa,
    isStandalone
  };
}
