// PaidBillsArchive.tsx
'use client';

import { useBillsStore } from '@/stores/billsStore';
import { useMemo, useState } from 'react';
import { formatDate } from '@/utils/date';
import { Bill } from '@/types/bill';

interface FilterState {
  search: string;
  startDate: string;
  endDate: string;
  vendor: string;
}

export function PaidBillsArchive() {
  const { bills, markBillUnpaid } = useBillsStore();
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    startDate: '',
    endDate: '',
    vendor: '',
  });

  const paidBills = useMemo(() => {
    if (!bills) return [];
    return bills.filter(b => b.paidDate);
  }, [bills]);

  // Filter logic
  const filteredBills = useMemo(() => {
    return paidBills.filter(bill => {
      const matchesSearch = filters.search
        ? bill.name.toLowerCase().includes(filters.search.toLowerCase())
        : true;
      const matchesVendor = filters.vendor
        ? (bill.vendor || '').toLowerCase().includes(filters.vendor.toLowerCase())
        : true;
      const billPaidDate = bill.paidDate ? new Date(bill.paidDate) : null;
      const matchesStart = filters.startDate
        ? billPaidDate && billPaidDate >= new Date(filters.startDate)
        : true;
      const matchesEnd = filters.endDate
        ? billPaidDate && billPaidDate <= new Date(filters.endDate)
        : true;
      return matchesSearch && matchesVendor && matchesStart && matchesEnd;
    });
  }, [paidBills, filters]);

  return (
    <div className="max-w-2xl mx-auto p-4 bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-800">
      <h2 className="text-xl font-bold mb-4 text-blue-900 dark:text-blue-200 flex items-center gap-2">
        <span role="img" aria-label="archive">🗂️</span> Paid Bills Archive
      </h2>
      <div className="flex flex-wrap gap-2 mb-4">
        <input
          type="text"
          placeholder="Search by name..."
          className="px-2 py-1 rounded border border-gray-300 dark:bg-gray-800 dark:border-gray-700"
          value={filters.search}
          onChange={e => setFilters(f => ({ ...f, search: e.target.value }))}
        />
        <input
          type="text"
          placeholder="Vendor..."
          className="px-2 py-1 rounded border border-gray-300 dark:bg-gray-800 dark:border-gray-700"
          value={filters.vendor}
          onChange={e => setFilters(f => ({ ...f, vendor: e.target.value }))}
        />
        <input
          type="date"
          className="px-2 py-1 rounded border border-gray-300 dark:bg-gray-800 dark:border-gray-700"
          value={filters.startDate}
          onChange={e => setFilters(f => ({ ...f, startDate: e.target.value }))}
        />
        <input
          type="date"
          className="px-2 py-1 rounded border border-gray-300 dark:bg-gray-800 dark:border-gray-700"
          value={filters.endDate}
          onChange={e => setFilters(f => ({ ...f, endDate: e.target.value }))}
        />
      </div>
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {filteredBills.length === 0 && (
          <div className="py-6 text-center text-gray-500 dark:text-gray-400">No paid bills found.</div>
        )}
        {filteredBills.map(bill => (
          <div key={bill.id} className="py-4 flex flex-col md:flex-row md:items-center md:justify-between gap-2">
            <div>
              <div className="font-medium text-gray-900 dark:text-white">{bill.name}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Vendor: {bill.vendor || '—'}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Amount: ${bill.amount.toFixed(2)}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Paid: {bill.paidDate ? formatDate(bill.paidDate, 'medium') : '—'}</div>
            </div>
            <button
              className="mt-2 md:mt-0 px-3 py-1.5 bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded hover:bg-yellow-200 dark:hover:bg-yellow-700 text-sm font-medium border border-yellow-200 dark:border-yellow-700 transition"
              onClick={() => markBillUnpaid(bill.id)}
            >
              Undo Paid
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}

export default PaidBillsArchive;
