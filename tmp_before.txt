﻿'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import NotificationBadge from './NotificationBadge';

interface NotificationPreviewProps {
  className?: string;
}

export function NotificationPreview({ className = '' }: NotificationPreviewProps) {
  const [unreadCount, setUnreadCount] = useState(0);
  const [hasNotifications, setHasNotifications] = useState(false);

  useEffect(() => {
    const updateNotificationCount = () => {
      try {
        const storedNotifications = localStorage.getItem('notifications');
        const storedReadStatus = localStorage.getItem('notificationsReadStatus');

        if (storedNotifications) {
          const notifications = JSON.parse(storedNotifications);
          const readStatus = storedReadStatus ? JSON.parse(storedReadStatus) : {};

          const unread = notifications.filter((n: any) => !readStatus[n.id]).length;
          setUnreadCount(unread);
          setHasNotifications(notifications.length > 0);
        } else {
          setUnreadCount(0);
          setHasNotifications(false);
        }
      } catch (error) {
        console.error('Error reading notifications:', error);
        setUnreadCount(0);
        setHasNotifications(false);
      }
    };

    // Initial load
    updateNotificationCount();

    // Listen for storage changes
    const handleStorageChange = () => {
      updateNotificationCount();
    };

    window.addEventListener('storage', handleStorageChange);

    // Also listen for custom events
    window.addEventListener('notificationsUpdated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('notificationsUpdated', handleStorageChange);
    };
  }, []);

  return (
    <div className={`p-4 sm:p-5 text-center text-gray-500 dark:text-gray-400 h-full flex flex-col justify-center items-center relative ${className}`}>
      {/* Notification icon with badge */}
      <div className="relative mb-3">
        <motion.div
          animate={unreadCount > 0 ? {
            scale: [1, 1.1, 1],
            rotate: [0, -5, 5, -5, 0]
          } : {}}
          transition={{
            duration: 0.8,
            repeat: unreadCount > 0 ? Infinity : 0,
            repeatDelay: 4,
            ease: "easeInOut"
          }}
          className="text-2xl sm:text-3xl filter drop-shadow-sm"
        >
          ðŸ””
        </motion.div>

        {/* Notification badge */}
        {unreadCount > 0 && (
          <div className="absolute -top-1 -right-1">
            <NotificationBadge count={unreadCount} size="sm" />
          </div>
        )}
      </div>

      {/* Status text */}
      <div className="space-y-1">
        <p className={`text-sm sm:text-base font-semibold leading-tight ${unreadCount > 0
            ? 'text-gray-800 dark:text-gray-100'
            : 'text-gray-600 dark:text-gray-300'
          }`}>
          {unreadCount > 0
            ? `${unreadCount} new notification${unreadCount !== 1 ? 's' : ''}`
            : hasNotifications
              ? 'All caught up!'
              : 'Stay updated'
          }
        </p>

        {unreadCount > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-xs text-blue-600 dark:text-blue-400 font-medium"
          >
            Tap to view
          </motion.div>
        )}
      </div>

      {/* Subtle pulse animation for active notifications */}
      {unreadCount > 0 && (
        <motion.div
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.1, 0.2, 0.1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl pointer-events-none"
        />
      )}

      {/* Subtle animation for empty state */}
      {!hasNotifications && (
        <motion.div
          animate={{ opacity: [0.3, 0.6, 0.3] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          className="absolute inset-0 bg-gradient-to-br from-gray-50/30 to-gray-100/30 dark:from-gray-800/20 dark:to-gray-700/20 rounded-xl pointer-events-none"
        />
      )}
    </div>
  );
}

export default NotificationPreview;

