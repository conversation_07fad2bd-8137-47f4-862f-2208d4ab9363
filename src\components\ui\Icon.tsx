import { ReactNode } from 'react';
import { cn } from '@/utils/cn';

interface IconProps {
  children: ReactNode;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  variant?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error' | 'neutral';
  className?: string;
}

// Consistent icon sizing
const sizeClasses = {
  xs: 'h-3 w-3',      // 12px
  sm: 'h-4 w-4',      // 16px
  md: 'h-5 w-5',      // 20px - default
  lg: 'h-6 w-6',      // 24px
  xl: 'h-8 w-8',      // 32px
  '2xl': 'h-12 w-12'  // 48px - dashboard cards
};

// Consistent icon colors
const variantClasses = {
  primary: 'text-primary-500',
  secondary: 'text-secondary-500',
  accent: 'text-accent-500',
  info: 'text-info-500',
  success: 'text-success-500',
  warning: 'text-warning-500',
  error: 'text-error-500',
  neutral: 'text-neutral-500'
};

export function Icon({
  children,
  size = 'md',
  variant = 'neutral',
  className = ''
}: IconProps) {
  return (
    <span className={cn(
      sizeClasses[size],
      variantClasses[variant],
      'inline-flex items-center justify-center',
      className
    )}>
      {children}
    </span>
  );
}

// Standardized dashboard icons with consistent stroke weight
export function DashboardIcon({ 
  children, 
  variant = 'primary',
  className = '' 
}: { 
  children: ReactNode; 
  variant?: IconProps['variant'];
  className?: string;
}) {
  return (
    <Icon size="2xl" variant={variant} className={cn('stroke-2', className)}>
      {children}
    </Icon>
  );
}

// Common dashboard icons with consistent styling
export const DashboardIcons = {
  AddBill: ({ variant = 'primary' }: { variant?: IconProps['variant'] }) => (
    <DashboardIcon variant={variant}>
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    </DashboardIcon>
  ),
  
  Calendar: ({ variant = 'accent' }: { variant?: IconProps['variant'] }) => (
    <DashboardIcon variant={variant}>
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    </DashboardIcon>
  ),
  
  Settings: ({ variant = 'secondary' }: { variant?: IconProps['variant'] }) => (
    <DashboardIcon variant={variant}>
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    </DashboardIcon>
  ),
  
  Notifications: ({ variant = 'info' }: { variant?: IconProps['variant'] }) => (
    <DashboardIcon variant={variant}>
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    </DashboardIcon>
  ),
  
  AIAssistant: ({ variant = 'secondary' }: { variant?: IconProps['variant'] }) => (
    <DashboardIcon variant={variant}>
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
    </DashboardIcon>
  ),
  
  BillsOverview: ({ variant = 'info' }: { variant?: IconProps['variant'] }) => (
    <DashboardIcon variant={variant}>
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>
    </DashboardIcon>
  ),
  
  Calculator: ({ variant = 'success' }: { variant?: IconProps['variant'] }) => (
    <DashboardIcon variant={variant}>
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    </DashboardIcon>
  )
};

// Notification badge component with consistent styling
export function NotificationBadge({ 
  show = false, 
  count, 
  className = '' 
}: { 
  show?: boolean; 
  count?: number; 
  className?: string; 
}) {
  if (!show) return null;
  
  return (
    <span className={cn(
      'absolute -top-0.5 -right-0.5 h-2.5 w-2.5 rounded-full bg-error-500 border-2 border-white dark:border-gray-900',
      'flex items-center justify-center',
      count && count > 0 && 'h-5 w-5 min-w-[1.25rem]',
      className
    )}>
      {count && count > 0 && (
        <span className="text-xs font-bold text-white leading-none">
          {count > 99 ? '99+' : count}
        </span>
      )}
    </span>
  );
}
